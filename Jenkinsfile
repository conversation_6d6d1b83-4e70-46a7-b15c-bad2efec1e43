pipeline {
    agent any
    environment {
        NODEJS_HOME = tool 'NodeJS-16.20.0'
        PATH = "${env.NODEJS_HOME}/bin:${env.PATH}"
        NPM_CONFIG_REGISTRY = 'https://mirrors.huaweicloud.com/repository/npm/'

    }
    stages {
        stage('Build') {
            steps {
                    sh 'rm -rf node_modules'
                    sh 'npm cache clean -f'
                    sh 'npm install'
                    sh 'npm run build'
            }
        }
        stage('Deploy') {
            steps {
                script {

                  sshagent(['df25f97b-514b-498b-8866-36686481e2ea']) {
                      def remoteServer = "**************"

                      // 部署到远程服务器
                      sh "scp -r dist/ root@${remoteServer}:/opt/tomcat/webapps/"
                      sh "ssh root@${remoteServer} cp /opt/tomcat/webapps/ROOT/WEB-INF.tar.gz /opt/tomcat/webapps/dist &"
                      sh "ssh root@${remoteServer} tar -xzvf /opt/tomcat/webapps/dist/WEB-INF.tar.gz -C /opt/tomcat/webapps/dist &"
                      sh "ssh root@${remoteServer} rm -rf /opt/tomcat/webapps/ROOT &"
                      sh "ssh root@${remoteServer} mv /opt/tomcat/webapps/dist /opt/tomcat/webapps/ROOT &"
                      // 在远程服务器上启动应用
//                       sh "ssh root@${remoteServer} sh /opt/app/sunmei-settlement-web/start-web.sh &"

                    }
                }
            }
        }
    }
}
