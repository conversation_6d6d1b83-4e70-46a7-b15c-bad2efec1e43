@import './variables.module.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './ruoyi.scss';

body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 15px;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

@for $i from 1 through 1000 {
  .w-#{$i} {
    width: #{$i}px;
  }
  
  .w-#{$i}-important {
    width: #{$i}px !important;
  }

  .h-#{$i} {
    height: #{$i}px;
  }

  .mt-#{$i} {
    margin-top: #{$i}px;
  }

  .ml-#{$i} {
    margin-left: #{$i}px;
  }

  .mr-#{$i} {
    margin-right: #{$i}px;
  }

  .mb-#{$i} {
    margin-bottom: #{$i}px;
  }
  .text-#{$i} {
    font-size: #{$i}px;
  }
}

.font-bold {
  font-weight: bold;
}

.flex {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.flex-jus-start {
  justify-content: flex-start !important;
  ;
}

.flex-jus-end {
  justify-content: flex-end !important;
  ;
}

.flex-jus-between {
  justify-content: space-between !important;
  ;
}

.flex-jus-around {
  justify-content: space-around !important;
  ;
}

.flex-item-start {
  align-items: flex-start !important;
  ;
}

.flex-item-end {
  align-items: flex-end !important;
  ;
}

.flex-item-between {
  align-items: space-between !important;
  ;
}

.flex-item-around {
  align-items: space-around !important;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.noborder {
  border: none !important;
}

.no-p {
  padding: 0 !important;
}

.no-p-t {
  padding-top: 0 !important;
}

.no-p-r {
  padding-right: 0 !important;
}

.no-p-b {
  padding-bottom: 0 !important;
}

.no-p-l {
  padding-left: 0 !important;
}

.no-m {
  margin: 0 !important;
}

.no-m-t {
  margin-top: 0 !important;
}

.no-m-r {
  margin-right: 0 !important;
}

.no-m-b {
  margin-bottom: 0 !important;
}

.no-m-l {
  margin-left: 0 !important;
}

.no-b {
  border: none !important;
}

.no-b-t {
  border-top: 0 !important;
}

.no-b-r {
  border-right: 0 !important;
}

.no-b-b {
  border-bottom: 0 !important;
}

.no-b-l {
  border-left: 0 !important;
}


.m-5 {
  margin: 5px !important
}

.m-10 {
  margin: 10px !important
}

.m-15 {
  margin: 15px !important
}

.m-20 {
  margin: 20px !important
}

.m-25 {
  margin: 25px !important
}

.m-30 {
  margin: 30px !important
}

.m-35 {
  margin: 35px !important
}

.m-40 {
  margin: 40px !important
}

.m-45 {
  margin: 45px !important
}

.m-50 {
  margin: 50px !important
}

.m-55 {
  margin: 55px !important
}

.m-60 {
  margin: 60px !important
}

.m-65 {
  margin: 65px !important
}

.m-70 {
  margin: 70px !important
}

.m-75 {
  margin: 75px !important
}

.m-80 {
  margin: 80px !important
}

.m-85 {
  margin: 85px !important
}

.m-90 {
  margin: 90px !important
}

.m-95 {
  margin: 95px !important
}

.m-100 {
  margin: 100px !important
}

.m-0-5 {
  margin: 0 5px !important
}

.m-0-10 {
  margin: 0 10px !important
}

.m-0-15 {
  margin: 0 15px !important
}

.m-0-20 {
  margin: 0 20px !important
}

.m-0-25 {
  margin: 0 25px !important
}

.m-0-30 {
  margin: 0 30px !important
}

.m-0-35 {
  margin: 0 35px !important
}

.m-0-40 {
  margin: 0 40px !important
}

.m-0-45 {
  margin: 0 45px !important
}

.m-0-50 {
  margin: 0 50px !important
}

.m-0-55 {
  margin: 0 55px !important
}

.m-0-60 {
  margin: 0 60px !important
}

.m-0-65 {
  margin: 0 65px !important
}

.m-0-70 {
  margin: 0 70px !important
}

.m-0-75 {
  margin: 0 75px !important
}

.m-0-80 {
  margin: 0 80px !important
}

.m-0-85 {
  margin: 0 85px !important
}

.m-0-90 {
  margin: 0 90px !important
}

.m-0-95 {
  margin: 0 95px !important
}

.m-0-100 {
  margin: 0 100px !important
}

.m-5-0 {
  margin: 5px 0 !important
}

.m-10-0 {
  margin: 10px 0 !important
}

.m-15-0 {
  margin: 15px 0 !important
}

.m-20-0 {
  margin: 20px 0 !important
}

.m-25-0 {
  margin: 25px 0 !important
}

.m-30-0 {
  margin: 30px 0 !important
}

.m-35-0 {
  margin: 35px 0 !important
}

.m-40-0 {
  margin: 40px 0 !important
}

.m-45-0 {
  margin: 45px 0 !important
}

.m-50-0 {
  margin: 50px 0 !important
}

.m-55-0 {
  margin: 55px 0 !important
}

.m-60-0 {
  margin: 60px 0 !important
}

.m-65-0 {
  margin: 65px 0 !important
}

.m-70-0 {
  margin: 70px 0 !important
}

.m-75-0 {
  margin: 75px 0 !important
}

.m-80-0 {
  margin: 80px 0 !important
}

.m-85-0 {
  margin: 85px 0 !important
}

.m-90-0 {
  margin: 90px 0 !important
}

.m-95-0 {
  margin: 95px 0 !important
}

.m-100-0 {
  margin: 100px 0 !important
}

.mt-5 {
  margin-top: 5px !important
}

.mt-10 {
  margin-top: 10px !important
}

.mt-15 {
  margin-top: 15px !important
}

.mt-20 {
  margin-top: 20px !important
}

.mt-25 {
  margin-top: 25px !important
}

.mt-30 {
  margin-top: 30px !important
}

.mt-35 {
  margin-top: 35px !important
}

.mt-40 {
  margin-top: 40px !important
}

.mt-45 {
  margin-top: 45px !important
}

.mt-50 {
  margin-top: 50px !important
}

.mt-55 {
  margin-top: 55px !important
}

.mt-60 {
  margin-top: 60px !important
}

.mt-65 {
  margin-top: 65px !important
}

.mt-70 {
  margin-top: 70px !important
}

.mt-75 {
  margin-top: 75px !important
}

.mt-80 {
  margin-top: 80px !important
}

.mt-85 {
  margin-top: 85px !important
}

.mt-90 {
  margin-top: 90px !important
}

.mt-95 {
  margin-top: 95px !important
}

.mt-100 {
  margin-top: 100px !important
}

.mb-5 {
  margin-bottom: 5px !important
}

.mb-10 {
  margin-bottom: 10px !important
}

.mb-15 {
  margin-bottom: 15px !important
}

.mb-20 {
  margin-bottom: 20px !important
}

.mb-25 {
  margin-bottom: 25px !important
}

.mb-30 {
  margin-bottom: 30px !important
}

.mb-35 {
  margin-bottom: 35px !important
}

.mb-40 {
  margin-bottom: 40px !important
}

.mb-45 {
  margin-bottom: 45px !important
}

.mb-50 {
  margin-bottom: 50px !important
}

.mb-55 {
  margin-bottom: 55px !important
}

.mb-60 {
  margin-bottom: 60px !important
}

.mb-65 {
  margin-bottom: 65px !important
}

.mb-70 {
  margin-bottom: 70px !important
}

.mb-75 {
  margin-bottom: 75px !important
}

.mb-80 {
  margin-bottom: 80px !important
}

.mb-85 {
  margin-bottom: 85px !important
}

.mb-90 {
  margin-bottom: 90px !important
}

.mb-95 {
  margin-bottom: 95px !important
}

.mb-100 {
  margin-bottom: 100px !important
}

.ml-5 {
  margin-left: 5px !important
}

.ml-10 {
  margin-left: 10px !important
}

.ml-15 {
  margin-left: 15px !important
}

.ml-20 {
  margin-left: 20px !important
}

.ml-25 {
  margin-left: 25px !important
}

.ml-30 {
  margin-left: 30px !important
}

.ml-35 {
  margin-left: 35px !important
}

.ml-40 {
  margin-left: 40px !important
}

.ml-45 {
  margin-left: 45px !important
}

.ml-50 {
  margin-left: 50px !important
}

.ml-55 {
  margin-left: 55px !important
}

.ml-60 {
  margin-left: 60px !important
}

.ml-65 {
  margin-left: 65px !important
}

.ml-70 {
  margin-left: 70px !important
}

.ml-75 {
  margin-left: 75px !important
}

.ml-80 {
  margin-left: 80px !important
}

.ml-85 {
  margin-left: 85px !important
}

.ml-90 {
  margin-left: 90px !important
}

.ml-95 {
  margin-left: 95px !important
}

.ml-100 {
  margin-left: 100px !important
}

.mr-5 {
  margin-right: 5px !important
}

.mr-10 {
  margin-right: 10px !important
}

.mr-15 {
  margin-right: 15px !important
}

.mr-20 {
  margin-right: 20px !important
}

.mr-25 {
  margin-right: 25px !important
}

.mr-30 {
  margin-right: 30px !important
}

.mr-35 {
  margin-right: 35px !important
}

.mr-40 {
  margin-right: 40px !important
}

.mr-45 {
  margin-right: 45px !important
}

.mr-50 {
  margin-right: 50px !important
}

.mr-55 {
  margin-right: 55px !important
}

.mr-60 {
  margin-right: 60px !important
}

.mr-65 {
  margin-right: 65px !important
}

.mr-70 {
  margin-right: 70px !important
}

.mr-75 {
  margin-right: 75px !important
}

.mr-80 {
  margin-right: 80px !important
}

.mr-85 {
  margin-right: 85px !important
}

.mr-90 {
  margin-right: 90px !important
}

.mr-95 {
  margin-right: 95px !important
}

.mr-100 {
  margin-right: 100px !important
}

.p-5 {
  padding: 5px !important
}

.p-10 {
  padding: 10px !important
}

.p-15 {
  padding: 15px !important
}

.p-20 {
  padding: 20px !important
}

.p-25 {
  padding: 25px !important
}

.p-30 {
  padding: 30px !important
}

.p-35 {
  padding: 35px !important
}

.p-40 {
  padding: 40px !important
}

.p-45 {
  padding: 45px !important
}

.p-50 {
  padding: 50px !important
}

.p-55 {
  padding: 55px !important
}

.p-60 {
  padding: 60px !important
}

.p-65 {
  padding: 65px !important
}

.p-70 {
  padding: 70px !important
}

.p-75 {
  padding: 75px !important
}

.p-80 {
  padding: 80px !important
}

.p-85 {
  padding: 85px !important
}

.p-90 {
  padding: 90px !important
}

.p-95 {
  padding: 95px !important
}

.p-100 {
  padding: 100px !important
}

.p-0-5 {
  padding: 0 5px !important
}

.p-0-10 {
  padding: 0 10px !important
}

.p-0-15 {
  padding: 0 15px !important
}

.p-0-20 {
  padding: 0 20px !important
}

.p-0-25 {
  padding: 0 25px !important
}

.p-0-30 {
  padding: 0 30px !important
}

.p-0-35 {
  padding: 0 35px !important
}

.p-0-40 {
  padding: 0 40px !important
}

.p-0-45 {
  padding: 0 45px !important
}

.p-0-50 {
  padding: 0 50px !important
}

.p-0-55 {
  padding: 0 55px !important
}

.p-0-60 {
  padding: 0 60px !important
}

.p-0-65 {
  padding: 0 65px !important
}

.p-0-70 {
  padding: 0 70px !important
}

.p-0-75 {
  padding: 0 75px !important
}

.p-0-80 {
  padding: 0 80px !important
}

.p-0-85 {
  padding: 0 85px !important
}

.p-0-90 {
  padding: 0 90px !important
}

.p-0-95 {
  padding: 0 95px !important
}

.p-0-100 {
  padding: 0 100px !important
}

.p-5-0 {
  padding: 5px 0 !important
}

.p-10-0 {
  padding: 10px 0 !important
}

.p-15-0 {
  padding: 15px 0 !important
}

.p-20-0 {
  padding: 20px 0 !important
}

.p-25-0 {
  padding: 25px 0 !important
}

.p-30-0 {
  padding: 30px 0 !important
}

.p-35-0 {
  padding: 35px 0 !important
}

.p-40-0 {
  padding: 40px 0 !important
}

.p-45-0 {
  padding: 45px 0 !important
}

.p-50-0 {
  padding: 50px 0 !important
}

.p-55-0 {
  padding: 55px 0 !important
}

.p-60-0 {
  padding: 60px 0 !important
}

.p-65-0 {
  padding: 65px 0 !important
}

.p-70-0 {
  padding: 70px 0 !important
}

.p-75-0 {
  padding: 75px 0 !important
}

.p-80-0 {
  padding: 80px 0 !important
}

.p-85-0 {
  padding: 85px 0 !important
}

.p-90-0 {
  padding: 90px 0 !important
}

.p-95-0 {
  padding: 95px 0 !important
}

.p-100-0 {
  padding: 100px 0 !important
}

.pt-5 {
  padding-top: 5px !important
}

.pt-10 {
  padding-top: 10px !important
}

.pt-15 {
  padding-top: 15px !important
}

.pt-20 {
  padding-top: 20px !important
}

.pt-25 {
  padding-top: 25px !important
}

.pt-30 {
  padding-top: 30px !important
}

.pt-35 {
  padding-top: 35px !important
}

.pt-40 {
  padding-top: 40px !important
}

.pt-45 {
  padding-top: 45px !important
}

.pt-50 {
  padding-top: 50px !important
}

.pt-55 {
  padding-top: 55px !important
}

.pt-60 {
  padding-top: 60px !important
}

.pt-65 {
  padding-top: 65px !important
}

.pt-70 {
  padding-top: 70px !important
}

.pt-75 {
  padding-top: 75px !important
}

.pt-80 {
  padding-top: 80px !important
}

.pt-85 {
  padding-top: 85px !important
}

.pt-90 {
  padding-top: 90px !important
}

.pt-95 {
  padding-top: 95px !important
}

.pt-100 {
  padding-top: 100px !important
}

.pb-5 {
  padding-bottom: 5px !important
}

.pb-10 {
  padding-bottom: 10px !important
}

.pb-15 {
  padding-bottom: 15px !important
}

.pb-20 {
  padding-bottom: 20px !important
}

.pb-25 {
  padding-bottom: 25px !important
}

.pb-30 {
  padding-bottom: 30px !important
}

.pb-35 {
  padding-bottom: 35px !important
}

.pb-40 {
  padding-bottom: 40px !important
}

.pb-45 {
  padding-bottom: 45px !important
}

.pb-50 {
  padding-bottom: 50px !important
}

.pb-55 {
  padding-bottom: 55px !important
}

.pb-60 {
  padding-bottom: 60px !important
}

.pb-65 {
  padding-bottom: 65px !important
}

.pb-70 {
  padding-bottom: 70px !important
}

.pb-75 {
  padding-bottom: 75px !important
}

.pb-80 {
  padding-bottom: 80px !important
}

.pb-85 {
  padding-bottom: 85px !important
}

.pb-90 {
  padding-bottom: 90px !important
}

.pb-95 {
  padding-bottom: 95px !important
}

.pb-100 {
  padding-bottom: 100px !important
}

.pl-5 {
  padding-left: 5px !important
}

.pl-10 {
  padding-left: 10px !important
}

.pl-15 {
  padding-left: 15px !important
}

.pl-20 {
  padding-left: 20px !important
}

.pl-25 {
  padding-left: 25px !important
}

.pl-30 {
  padding-left: 30px !important
}

.pl-35 {
  padding-left: 35px !important
}

.pl-40 {
  padding-left: 40px !important
}

.pl-45 {
  padding-left: 45px !important
}

.pl-50 {
  padding-left: 50px !important
}

.pl-55 {
  padding-left: 55px !important
}

.pl-60 {
  padding-left: 60px !important
}

.pl-65 {
  padding-left: 65px !important
}

.pl-70 {
  padding-left: 70px !important
}

.pl-75 {
  padding-left: 75px !important
}

.pl-80 {
  padding-left: 80px !important
}

.pl-85 {
  padding-left: 85px !important
}

.pl-90 {
  padding-left: 90px !important
}

.pl-95 {
  padding-left: 95px !important
}

.pl-100 {
  padding-left: 100px !important
}

.pr-5 {
  padding-right: 5px !important
}

.pr-10 {
  padding-right: 10px !important
}

.pr-15 {
  padding-right: 15px !important
}

.pr-20 {
  padding-right: 20px !important
}

.pr-25 {
  padding-right: 25px !important
}

.pr-30 {
  padding-right: 30px !important
}

.pr-35 {
  padding-right: 35px !important
}

.pr-40 {
  padding-right: 40px !important
}

.pr-45 {
  padding-right: 45px !important
}

.pr-50 {
  padding-right: 50px !important
}

.pr-55 {
  padding-right: 55px !important
}

.pr-60 {
  padding-right: 60px !important
}

.pr-65 {
  padding-right: 65px !important
}

.pr-70 {
  padding-right: 70px !important
}

.pr-75 {
  padding-right: 75px !important
}

.pr-80 {
  padding-right: 80px !important
}

.pr-85 {
  padding-right: 85px !important
}

.pr-90 {
  padding-right: 90px !important
}

.pr-95 {
  padding-right: 95px !important
}

.pr-100 {
  padding-right: 100px !important
}

.table1 {
  margin: 0;
  padding: 0;
  border-collapse: collapse;
  font-size: 14px;
}

.table1 th,
.table1 td {
  border: 1px solid #ebeef5;
  padding: 8px 25px;
}

.table1 th {
  background-color: #f5f7fa;
}