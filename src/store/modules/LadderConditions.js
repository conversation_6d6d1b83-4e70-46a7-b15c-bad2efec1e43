
const useLadderConditionsStore = defineStore(
  'ladderConditionsStore',
  {
    state: () => ({
      timeList: [],
      formData:{
        id: '',
        name: '', // 获取收费信息
        billName: '',
        userId: '',
        timeList: [
          {
            id: '',
            timeTypeList: {},
            dateMonth: [],
            dateRange: [],
            timeType: {}
          }
        ]
      }
    }),
    actions: {
      // 添加一个阶梯条件框
      addFeeItem(index, itemIndex, obj) {
        // console.log("addFeeItem", index,itemIndex);
        this.timeList[itemIndex].shopManagerList[index].feeList.push(obj)
      },
      // 删除一个阶梯条件框
      removeFeeItem(index, feeIndex, itemIndex){
        // console.log("removeFeeItem", index, feeIndex);
        this.timeList[itemIndex].shopManagerList[index].feeList.splice(feeIndex, 1)
      }
    }
  })
  
export default useLadderConditionsStore
  