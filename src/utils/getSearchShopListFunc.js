import { getSearchShopList } from '@/api/chargingManager/FeeManager'

// 定义常量
const SEARCH_SHOP_LIST_KEY = 'searchShopList'
// 数据转换函数
const transformShopData = (data) => {
  return data.map((item) => ({
    value: item.shopId,
    label: `${item.shopId}-${item.shopName}`
  }))
}

// 获取所有门店信息
export const getSearchShopListFunc = async () => {
  
  // 读取本地缓存门店信息
  const cachedShopList = sessionStorage.getItem(SEARCH_SHOP_LIST_KEY)

  if(sessionStorage.getItem('loading') === 'true' || cachedShopList){
    return JSON.parse(cachedShopList) || []
  }
  try {
    sessionStorage.setItem('loading', 'true')
    const res = await getSearchShopList({ shopName: '' })

    if (res.code === 200 && res.data.length > 0) {
      const transformedData = transformShopData(res.data)

      // 保存到sessionStorage中
      sessionStorage.setItem(SEARCH_SHOP_LIST_KEY, JSON.stringify(transformedData))

      return transformedData
    }
    sessionStorage.setItem('loading', 'false')
    return []
  } catch (error) {
    sessionStorage.setItem('loading', 'false')
    console.error('Failed to fetch shop list:', error)
    throw new Error('Failed to fetch shop list')
  }
}