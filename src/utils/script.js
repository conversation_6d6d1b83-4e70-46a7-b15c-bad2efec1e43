// 通用方法

// 自动算高度
export const autoHeightFunc = (num) => {
  return document.documentElement.clientHeight - num
}

// 禁用当前月之后的所有月份
export const disabledDate = (date) => {
  const today = new Date()
  const currentMonth = today.getMonth() // 当前月份
  const currentYear = today.getFullYear() // 当前年份
  // 禁用当前年份之后的月份
  if (date.getFullYear() > currentYear) {
    return true
  }
  // 禁用当前年份和当前月份之后的月份
  if (date.getFullYear() === currentYear && date.getMonth() >= currentMonth) {
    return true
  }
  return false
}

// 将时间格式转换为年月日时分秒
export const formatDateTime = (date) => {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}
// 返回年月日不带时分秒
export const formatDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
// 返回年月
export const formatYearMonth = (date) => {
  if (!date) return ''
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  return `${year}-${month}`
}

// 钱数判断 允许负数
export const handleMinusAmountInput = (value, obj, key) => {
  // 1. 去除所有非数字、小数点和负号的字符
  let filtered = value.replace(/[^\d.-]/g, '')
  
  // 2. 确保只有一个负号且只能在开头
  const minusCount = (filtered.match(/-/g) || []).length
  if (minusCount > 1) {
    // 如果有多个负号，只保留第一个
    filtered = '-' + filtered.replace(/-/g, '')
  } else if (minusCount === 1 && !filtered.startsWith('-')) {
    // 如果负号不在开头，移到开头
    filtered = '-' + filtered.replace(/-/g, '')
  }
  
  // 3. 确保只有一个小数点
  const dotIndex = filtered.indexOf('.')
  if (dotIndex !== -1) {
    filtered = filtered.substring(0, dotIndex + 1) + 
               filtered.substring(dotIndex + 1).replace(/\./g, '')
  }
  
  // 4. 限制小数点后最多两位
  if (dotIndex !== -1 && filtered.length - dotIndex > 3) {
    filtered = filtered.substring(0, dotIndex + 3)
  }
  
  // 5. 不能以小数点开头（如果是正数）
  if (filtered.startsWith('.') && !filtered.startsWith('-.')) {
    filtered = '0' + filtered
  }
  // 处理以"-."开头的情况
  else if (filtered.startsWith('-.')) {
    filtered = '-0' + filtered.substring(1)
  }
  
  // 6. 如果只有"-"，设为空或根据需求处理
  if (filtered === '-') {
    filtered = '-'
    // 或者可以设为 '' 取决于你的需求
    // filtered = ''
  }
  
  obj[key] = filtered // 更新金额
}
export const handleAmountInput = (value, obj, key) => {
  // 1. 去除所有非数字和小数点的字符
  let filtered = value.replace(/[^\d.]/g, '')
  // 2. 确保只有一个小数点
  const dotIndex = filtered.indexOf('.')
  if (dotIndex !== -1) {
    filtered = filtered.substring(0, dotIndex + 1) + 
                    filtered.substring(dotIndex).replace(/\./g, '')
  }
  // 3. 限制小数点后最多两位
  if (dotIndex !== -1 && filtered.length - dotIndex > 3) {
    filtered = filtered.substring(0, dotIndex + 3)
  }
  // 4. 不能以小数点开头
  if (filtered.startsWith('.')) {
    filtered = '0' + filtered
  }
  // console.log("过滤后的金额:", filtered);
  obj[key] = filtered // 更新金额
}

// 参数拼接
export const paramsConcat = (params) => {
  let str = ''
  for (let key in params) {
    if(params[key]){
      str += `${key}=${params[key]}&`
    }
  }
  return str.slice(0, -1)
}