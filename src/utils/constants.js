// 定义常量

// 分页常量信息
export const paginationInfoConstant = {
  currentPage: 1, // 当前页码
  pageSize: 20,
  pageSizes: [10, 20, 50, 100],
  totalCount: 0, // 总条数
  layout: 'total, sizes, prev, pager, next, jumper'
}
// 是否状态
export const isStatus = [
  { id: 1, label: '是', value: 1, valueString: '1' },
  { id: 2, label: '否', value: 0, valueString: '0' }
]
// 调整状态
export const adjustStatus = [
  { id: 1, label: '待确认', value: '1' },
  { id: 2, label: '已调整', value: '2' }
]
// 类型对照表
export const tabList = [
  {
    id: 100,
    label: '商标使用费',
    value: 'trademark',
    children: [],
    disabled: false
  },
  {
    id: 101,
    label: '会员管理费',
    value: 'member',
    children: [],
    disabled: false
  },
  {
    id: 102,
    label: '输送客源费',
    value: 'transport',
    children: [],
    disabled: false
  },
  {
    id: 103,
    label: 'AI经营管家服务费',
    value: 'ai',
    children: [],
    disabled: false
  },
  {
    id: 104,
    label: '运营品质检查费',
    value: 'check',
    children: [],
    disabled: false
  },
  {
    id: 105,
    label: '品质督导费',
    value: 'supervise',
    children: [],
    disabled: false
  },
  {
    id: 21,
    label: '运营顾问费',
    value: 'consult',
    children: [],
    disabled: false
  },
  {
    id: 106,
    label: '市场营销费',
    value: 'market',
    children: [],
    disabled: false
  },
  {
    id: 107,
    label: '财务咨询费',
    value: 'finance',
    children: [],
    disabled: false
  },
  {
    id: 108,
    label: '店长工资',
    value: 'manager',
    children: [],
    disabled: false
  },
  {
    id: 109,
    label: '营销经理工资',
    value: 'marketing',
    children: [],
    disabled: false
  }
]

export const leList = [
  { id: 1, title: `<`, value: '<' },
  { id: 2, title: `≤`, value: '≤' }
]

export const brandTypeList = {
  'member': [
    { id: 1, title: `积分产生数*`, value: 'integralNum' },
    { id: 2, title: `产生积分的消费金额*`, value: 'integralPrice' }
  ],
  'trademark': [
    { id: 1, title: `营业收入*`, value: 'gmv' },
    { id: 2, title: `房费收入*`, value: 'roomFee' }
  ],
  'transport': [
    { id: 1, title: `售出间夜数*`, value: 'nightNum' },
    { id: 2, title: `订单金额*`, value: 'orderPrice' },
    { id: 3, title: `营业收入*`, value: 'gmv' }
  ]
}

export const sourceTypeList = [
  { id: 1, title: `比例`, value: 'ratio', visible: true },
  { id: 2, title: `定额`, value: 'quota', visible: true },
  { id: 3, title: `保底`, value: 'guarantee', visible: true },
  { id: 5, title: '封顶', value: 'cap', visible: true },
  { id: 6, title: '组合', value: 'combine', visible: true },
  { id: 4, title: `特殊`, value: 'special', visible: true }
]

export const guaranteeList = [
  { id: 1, title: `比例`, visible: true, calculateType: 1, value: 'ratio' },
  { id: 2, title: `定额`, visible: true, calculateType: 2, value: 'quota' },
  { id: 3, title: `特殊`, visible: true, calculateType: 3, value: 'special' }
]

export const monthList = function () {
  const arr = []
  for (let i = 1; i <= 12; i++) {
    arr.push({ id: i, title: `${i}月`, value: i })
  }
  return arr
}

export const workDaySymbolList = [
  { id: 1, title: `<`, value: '1' },
  { id: 2, title: `≤`, value: '2' },
  { id: 3, title: `>`, value: '3' },
  { id: 4, title: `≥`, value: '4' }
]

export const quotaTypeList = {
  'member': [
    { id: 3, title: '约定房量', value: 'fixedRoom' },
    { id: 4, title: '实际房量', value: 'roomNumRatio' },
    { id: 2, title: '固定金额', value: 'fixedPrice' }
  ],
  'ai': [
    { id: 3, title: '固定金额', value: 'fixedPrice' }, // fixedPriceRatio
    { id: 2, title: '限时折扣', value: 'limitDiscount' },
    { id: 4, title: '实际房量', value: 'roomNum' }
  ],
  'default': [
    { id: 1, title: '依据房量计算', value: 'roomNum' },
    { id: 2, title: '固定金额', value: 'fixedPrice' }
  ]
}
// 选择时间选项
export const timeOptions = [
  { id: 0, title: `长期`, type: 'selectLongTerm' },
  { id: 1, title: `每年固定月份`, type: 'dateRangeMonth' },
  // { id: 2, title: `按月分段计费`, type: "input" },
  // { id: 3, title: `开业__月以后（不含此月）`, type: "input" },
  { id: 4, title: `固定日期`, type: 'dateRange' }
]

export const shopManagerStatusList = [
  { id: 1, title: '有店长', visible: true },
  { id: 4, title: '有营销经理', visible: true },
  { id: 5, title: '有店长或有营销经理', visible: true },
  { id: 2, title: '无店长', visible: true },
  { id: 6, title: '无营销经理', visible: true },
  { id: 7, title: '​无店长或无营销经理', visible: true },
  { id: 3, title: '无限制', visible: true }
]
//设置时间选择器长期选择
export const shortcutsLengDay = [
  {
    text: '长期',
    value: () => {
      const end = new Date('2099-12-31')
      const start = new Date()
      //格式为yyyy-MM-dd
      return [start, end]
    }
  }
]

export const shortcuts713 = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]
// 长期和当前日期
export const shortcutsNowLong = [
  {
    text: '当前日期',
    value: () => {
      return new Date() // 设置一个很远的日期表示长期
    }
  },
  {
    text: '长期',
    value: () => {
      return new Date(9999, 11, 31) // 设置一个很远的日期表示长期
    }
  }
]

// 阶梯条件
export const ladderConditionList = [
  { label: 'occ', value: '出租率', visible: true, unit: '%', describe: '出租率', width: '140px', width2: '220px' },
  { label: 'gmv', value: '营业收入', visible: true, unit: '元', describe: '营业收入', width: '180px', width2: '260px' },
  { label: 'roomNum', value: '房量', visible: true, unit: '间', describe: '房量', width: '140px', width2: '220px' },
  { label: 'cardFee', value: '售卡金额', visible: false, condition: ['member'], unit: '元', describe: '售卡金额', width: '180px', width2: '260px' },
  { label: 'revpark', value: '单房收益', visible: false, condition: ['trademark'], unit: '元', describe: '单房收益价格', width: '180px', width2: '260px' },
  { label: 'unlimited', value: '无限制', visible: true }
]
// 费用周期
export const feeCycleList = [
  { id: 1, title: '元/月', value: 1 },
  { id: 2, title: '元/年', value: 2 }
]
// 详情数据格式
export const initialTimeList = [
  {
    name: '',
    timeTypeList: {},
    dateMonth: [],
    dateRange: [],
    months: [],
    timeType: {}, // 是否选择日期段选择器
    id: '',
    batchNumber: undefined,
    remark: '',
    shopManagerList: [// 人员在店
      {
        shopManagerStatus: [], // 是否在店状态
        workDays: 0,
        betType: ['unlimited'], // 阶梯条件选中值
        feeList: [
          {
            type: 1,
            value: 0,
            fixedPrice: '',
            roomPrice: '',
            percentNum: '',
            paramType: '',
            quotaType: '',
            feeTypeList: [
              {
                id: 1,
                calculateType: '',
                calculateUnit: 1,
                discountNum: 100,
                minGuarantee: {
                  detailList: [
                    {
                      fixedPrice: '',
                      calculateUnit: 1,
                      roomValue: '',
                      roomPrice: '',
                      quotaType: { id: 0, roomPrice: 0 },
                      id: '',
                      calculateType: '',
                      discountNum: 100,
                      specialName: '',
                      percentNum: '',
                      specialExpress: '',
                      calculateExpression: '',
                      freePrice: '0',
                      symbolGreaterThan: 1,
                      paramType: ''
                    },
                    {
                      fixedPrice: '',
                      calculateUnit: 1,
                      roomValue: '',
                      roomPrice: '',
                      quotaType: { id: 0, roomPrice: 0 },
                      id: '',
                      calculateType: '',
                      discountNum: 100,
                      specialName: '',
                      percentNum: '',
                      specialExpress: '',
                      calculateExpression: '',
                      freePrice: '0',
                      symbolGreaterThan: 1,
                      paramType: ''
                    }
                  ]
                }
              }
            ]
          }
        ]
      }
    ]
  }
]
// 阶梯条件后的加号
export const addFeeItemDatas = {
  type: 1,
  value: 0,
  feeTypeList: [
    {
      id: 1,
      discountNum: 100,
      paramType: '',
      calculateUnit: 1,
      minGuarantee: {
        detailList: [
          {
            roomValue: '',
            roomPrice: '',
            quotaType: '',
            calculateUnit: 1,
            id: '',
            discountNum: 100,
            specialName: '',
            percentNum: '',
            specialExpress: '',
            calculateExpression: '',
            freePrice: '0',
            paramType: ''
          },
          {
            roomValue: '',
            roomPrice: '',
            quotaType: '',
            id: '',
            discountNum: 100,
            specialName: '',
            percentNum: '',
            specialExpress: '',
            calculateExpression: '',
            freePrice: '0',
            paramType: ''
          }
        ]
      }
    }
  ]
}
// 选择时间后的加号
export const addDateItemDatas = {
  timeTypeList: {},
  dateMonth: [],
  dateRange: [],
  timeType: {},
  id: '',
  shopManagerList: [
    {
      shopManagerStatus: [],
      workDays: 0,
      betType: ['unlimited'],
      feeList: [
        {
          type: 1,
          value: 0,
          feeTypeList: [
            {
              id: 1,
              discountNum: 100,
              paramType: '',
              calculateUnit: 1,
              minGuarantee: {
                detailList: [
                  {
                    calculateType: '',
                    calculateUnit: 1,
                    roomValue: '',
                    roomPrice: '',
                    quotaType: '',
                    id: '',
                    discountNum: 100,
                    specialName: '',
                    percentNum: '',
                    specialExpress: '',
                    calculateExpression: '',
                    roomNumRatio: '',
                    freePrice: '0',
                    paramType: ''
                  },
                  {
                    calculateType: '',
                    roomValue: '',
                    roomPrice: '',
                    quotaType: '',
                    id: '',
                    discountNum: 100,
                    specialName: '',
                    percentNum: '',
                    specialExpress: '',
                    roomNumRatio: '',
                    calculateExpression: '',
                    freePrice: '0',
                    paramType: ''
                  }
                ]
              }
            }
          ]
        }
      ]
    }
  ]
}
// 人员在店
export const addShopManager = {
  // shopManagerStatus: state, 2 4 5
  betType: ['unlimited'],
  feeList: [
    {
      type: 1,
      value: 0,
      feeTypeList: [
        {
          id: 1,
          discountNum: 100,
          paramType: '',
          calculateUnit: 1,
          minGuarantee: {
            detailList: [
              {
                calculateType: '',
                calculateUnit: 1,
                roomValue: '',
                roomPrice: '',
                quotaType: '',
                id: '',
                discountNum: 100,
                specialName: '',
                percentNum: '',
                specialExpress: '',
                calculateExpression: '',
                roomNumRatio: '',
                freePrice: '0',
                paramType: ''
              },
              {
                calculateType: '',
                roomValue: '',
                roomPrice: '',
                quotaType: '',
                id: '',
                discountNum: 100,
                specialName: '',
                percentNum: '',
                specialExpress: '',
                roomNumRatio: '',
                calculateExpression: '',
                freePrice: '0',
                paramType: ''
              }
            ],
            value: 0
          }
        }
      ]
    }
  ],
  feeTypeList: [{ type: 1, value: 0 }]
}
// 阶梯条件切换
export const addBet = {
  type: 1,
  value: 0,
  feeTypeList: [
    {
      id: 1,
      discountNum: 100,
      paramType: '',
      calculateUnit: 1,
      minGuarantee: {
        detailList: [
          {
            calculateType: '',
            calculateUnit: 1,
            roomValue: '',
            roomPrice: '',
            quotaType: '',
            id: '',
            discountNum: 100,
            specialName: '',
            percentNum: '',
            specialExpress: '',
            calculateExpression: '',
            roomNumRatio: '',
            paramType: ''
          },
          {
            calculateType: '',
            roomValue: '',
            roomPrice: '',
            quotaType: '',
            id: '',
            discountNum: 100,
            specialName: '',
            percentNum: '',
            specialExpress: '',
            roomNumRatio: '',
            calculateExpression: '',
            paramType: ''
          }
        ],
        value: 0
      }
    }
  ]
}
// 计费基数导入 导入项
export const importBillingBaseDatas = [
  { id: 1, name: '店长工资实收实付季度调整', value: '1' },
  { id: 2, name: '店长绩效', value: '2' },
  { id: 3, name: '营销经理工资实收实付季度调整', value: '3' },
  { id: 4, name: '营销经理绩效', value: '4' },
  { id: 5, name: '售卡金额', value: '5' },
  { id: 6, name: '售卡返佣金额', value: '6' },
  { id: 7, name: '舆情系统使用费', value: '7' }
]
// 计费基数
export const billingBaseDatas = [
  { id: 1, name: '房费合计/元', value: '1', type: 'sb' },
  { id: 2, name: '商品费/元', value: '2', type: 'sb' },
  { id: 3, name: '房间数量', value: '3', type: 'sb' },
  { id: 4, name: '出租率', value: '4', type: 'sb' },
  { id: 5, name: '平均房价', value: '5', type: 'sb' },
  { id: 6, name: '夜审天数', value: '6', type: 'sb' },
  { id: 7, name: '舆情系统使用费', value: '7', type: 'sb' },
  { id: 8, name: '积分产生数', value: '8', type: 'sh' },
  { id: 9, name: '预订间夜数', value: '9', type: 'sh' },
  { id: 10, name: '积分抵现', value: '10', type: 'sh' },
  { id: 11, name: '中央订单金额', value: '11', type: 'sh' },
  { id: 12, name: '预定返佣数', value: '12', type: 'sh' },
  { id: 13, name: '线上平台费', value: '13', type: 'sh' },
  { id: 14, name: '店长基本工资', value: '14', type: 'gz' },
  { id: 15, name: '店长绩效工资', value: '15', type: 'gz' },
  { id: 16, name: '店长工资季度调整', value: '16', type: 'gz' },
  { id: 17, name: '营销经理基本工资', value: '17', type: 'gz' },
  { id: 18, name: '营销经理绩效工资', value: '18', type: 'gz' },
  { id: 19, name: '营销经理季度调整', value: '19', type: 'gz' }

]

// 审核状态
export const billingStatusList = [
  { id: 1, label: '待录入', value: 1 },
  { id: 2, label: '待审核', value: 2 },
  { id: 3, label: '已通过', value: 3 },
  { id: 4, label: '已驳回', value: 4 },
  { id: 5, label: '已保存', value: 5 }
]
// 调整类型
export const adjustmentTypeList = [
  { id: 1, label: '管理费签章流程', value: 1 },
  { id: 2, label: '手动调整', value: 2 }
]
// 舞弊状态
export const fraudStatusList = [
  { id: 0, label: '舞弊', value: '0' },
  { id: 1, label: '闭环', value: '1' }
]
// 认领状态
export const claimStatusList = [
  { id: 1, label: '全部', value: 1 },
  { id: 2, label: '待认领', value: 2 },
  { id: 3, label: '已认领', value: 3 }
]

// 渠道账单单据类型
export const billTypeMap = {
  1: 'OTA结算对账单',
  2: '直销佣金对账单',
  3: '其他对账单',
  4: '抵扣账单',
  5: '包房回款账单'
}

// 
export const dzdOptionsList = {
  exports: [ // 0-模糊导入，1-抖音ebk，2-美团ebk
    // {
    //   id: 1,
    //   label: '对账导入',
    //   value: '0'
    // },
    {
      id: 2,
      label: '按渠道单号模糊导入',
      value: '0'
    },
    {
      id: 3,
      label: '美团EBK导入',
      value: '2'
    },
    {
      id: 4,
      label: '抖音EBK导入',
      value: '1'
    }
  ],
  // 订单处理 下载400订单 线下全量订单 线下本店订单 过滤钟点房 过滤分销代订 过滤2.0订单 过滤下线本店 剔除暂缓店 过滤25.1.1前 15分钟转入住 不足9元按9元 其他异常过滤
  roders: [
    // 下载接口用不了  pms那面没给接口  只是个假下载
    {
      id: 2,
      label: '下载400订单',
      value: '2',
      type: 'download',
      branch: '400'
    },
    {
      id: 3,
      label: '线下全量订单',
      value: '3',
      type: 'download',
      branch: 'offlineAll'
    },
    {
      id: 4,
      label: '线下跨店单',
      value: '4',
      type: 'download',
      branch: ''
    },
    {
      id: 5,
      label: '过滤钟点房',
      value: '5',
      type: '',
      branch: 'clockFilter'
    },
    {
      id: 6,
      label: '过滤分销代订',
      value: '6',
      type: '',
      branch: 'fxddFilter'
    },
    {
      id: 7,
      label: '过滤2.0订单',
      value: '7',
      type: '',
      branch: 'orderV2Filter'
    },
    {
      id: 8,
      label: '过滤线下本店',
      value: '8',
      type: 'id',
      branch: 'xxbdFilter'
    },
    {
      id: 9,
      label: '剔除暂缓店',
      value: '9',
      type: 'id',
      branch: 'zhdFilter'
    },
    {
      id: 10,
      label: '过滤25.1.1前',
      value: '10',
      type: '',
      branch: 'order25Filter'
    },
    {
      id: 11,
      label: '15分钟转入住',
      value: '11',
      type: 'id',
      branch: 'zrz15Filter'
    },
    {
      id: 12,
      label: '不足9元按9元',
      value: '12',
      type: 'id',
      branch: 'bm9Filter'
    },
    {
      id: 13,
      label: '其他异常过滤(按入住单号过滤)',
      value: '13',
      type: 'jd',
      branch: 'orderNoFilter'
    },
    {
      id: 14,
      label: '其他异常过滤(500以下售卡免补)',
      value: '14',
      type: 'jd',
      branch: 'sk500Filter'
    }
  ]
}