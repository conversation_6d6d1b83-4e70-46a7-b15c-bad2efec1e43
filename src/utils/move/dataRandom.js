// 生成随机数据的辅助函数
export const generateRandomData = () => {
  const data = []
  for (let i = 0; i < 50; i++) {
    data.push({
      branchNo: `FB${String(i + 1).padStart(3, '0')}`, // 分店号
      branchName: `分店${i + 1}`, // 分店名称
      period: `2024-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}`, // 期间
      isAudited: (Math.random() > 0.5), // 审核否
      isSettled: true, // 是否结清 Math.random() > 0.5
      lastDebt: Number(Math.random() * 10000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 上期欠款
      currentPayment: Number(Math.random() * 8000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 本期收款
      penalty: Number(Math.random() * 500).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 违约金
      currentReceivable: Number(Math.random() * 12000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 本期应收
      prepaymentOffset: Number(Math.random() * 3000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 冲预收款
      amountPayable: Number(Math.random() * 15000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 应缴金额
      actualPayment: Number(Math.random() * 14000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 本期实收
      endingBalance: Number(Math.random() * 2000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 期末结余
      initialPrepayment: Number(Math.random() * 5000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 期初预收
      currentPrepayment: Number(Math.random() * 4000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 本期预收
      endingPrepayment: Number(Math.random() * 6000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 期末预收
      paymentTime: `2024-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')} ${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`, // 缴费时间
      paymentDate: `2024-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`, // 缴费日期
      status: ['正常', '待审核', '已结清'][Math.floor(Math.random() * 3)], // 状态
      openingDate: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`, // 开业日期
      terminationDate: Math.random() > 0.8 ? `2024-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}` : '', // 解约日期
      region: ['华北', '华南', '华东', '西南'][Math.floor(Math.random() * 4)], // 大区
      district: ['城中区', '城东区', '城西区', '城南区'][Math.floor(Math.random() * 4)], // 城区
      operatingIncome: Number(Math.random() * 100000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 营业收入
      trademarkFee: Number(Math.random() * 2000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 商标使用费
      pointsGenerated: Math.floor(Math.random() * 1000), // 积分产生数
      pointsAmount: Number(Math.random() * 1000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 积分款
      reservedRoomNights: Math.floor(Math.random() * 100), // 预定间夜数
      reservationCommission: Number(Math.random() * 2000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 预定佣金
      operationInspectionFee: Number(Math.random() * 1000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 运营质检费
      systemMaintenanceFee: Number(Math.random() * 800).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 系统维护费
      roomCount: Math.floor(Math.random() * 50) + 20, // 房间数量
      occupancyRate: (Math.random() * 100).toFixed(2) + '%', // 出租率
      averageRoomRate: Number(Math.random() * 300 + 200).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 平均房价
      pointsDeduction: Number(Math.random() * 500).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 积分抵现
      managerSalaryPrepaid: Number(Math.random() * 8000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 店长工资(预收)
      managerPerformanceSalary: Number(Math.random() * 3000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 店长绩效工资
      centralOrderAmount: Number(Math.random() * 20000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 中央订单金额
      marketingFee: Number(Math.random() * 2000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 市场营销费
      managerBaseSalary: Number(Math.random() * 5000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 店长基本工资
      qualitySupervisionFee: Number(Math.random() * 1000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 品质督导费
      operationConsultingFee: Number(Math.random() * 1500).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 运营顾问费
      pointsDeductionAmount: Number(Math.random() * 800).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 积分抵现金额
      reservationCommissionAmount: Number(Math.random() * 2000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 预定返佣金额
      reservationCommissionCount: Math.floor(Math.random() * 50), // 预定返佣数
      nightDeepDays: Math.floor(Math.random() * 30), // 夜深天数
      publicOpinionSystemFee: Number(Math.random() * 500).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 舆情系统使用费
      financialConsultingFee: Number(Math.random() * 1000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 财务咨询费
      salaryQuarterlyAdjustment: Number(Math.random() * 1000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 工资季度调整
      customerSourceFee: Number(Math.random() * 1500).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 客源输送费
      membershipManagementFee: Number(Math.random() * 800).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 会员管理费
      onlinePlatformFee: Number(Math.random() * 1200).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 线上平台费
      marketingManagerSalary: Number(Math.random() * 8000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 营销经理工资
      marketingManagerPerformance: Number(Math.random() * 3000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 营销经理绩效工资
      marketingManagerQuarterlyAdjustment: Number(Math.random() * 1000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 营销经理工资季度调整
      marketingManagerBaseSalary: Number(Math.random() * 5000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 营销经理基本工资
      entryPerson: `操作员${Math.floor(Math.random() * 10) + 1}`, // 录入人员
      collectionPerson: `收费员${Math.floor(Math.random() * 5) + 1}` // 收费人员
    })
  }
  return data
}
// 合计行计算方法
export const getSummaries = (param) => {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    const values = data.map((item) => {
      // 如果是数字格式的字符串（带逗号的），先转换回数字
      const value = item[column.property]
      if (typeof value === 'string' && /^[\d,]+\.?\d*$/.test(value)) {
        return parseFloat(value.replace(/,/g, ''))
      }
      return 0
    })
    
    if (!values.every((value) => Number.isNaN(value))) {
      const sum = values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
      
      // 将合计结果也格式化为千位分隔的格式
      sums[index] = sum.toLocaleString('en-US', { 
        minimumFractionDigits: 2, 
        maximumFractionDigits: 2 
      })
    } else {
      sums[index] = ''
    }
  })
  return sums
}

export const table1Demo = () => {
  return [
    {
      companyName: '费用项目',
      averageRoomRate: Number(Math.random() * 300 + 200).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 平均房价
      pointsDeduction: Number(Math.random() * 500).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 积分抵现
      managerSalaryPrepaid: Number(Math.random() * 8000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 店长工资(预收)
      managerPerformanceSalary: Number(Math.random() * 3000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) // 店长绩效工资
    },
    {
      companyName: '商标使用费',
      averageRoomRate: Number(Math.random() * 300 + 200).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 平均房价
      pointsDeduction: Number(Math.random() * 500).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 积分抵现
      managerSalaryPrepaid: Number(Math.random() * 8000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 店长工资(预收)
      managerPerformanceSalary: Number(Math.random() * 3000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) // 店长绩效工资
    },
    {
      companyName: '费用分项etc',
      averageRoomRate: Number(Math.random() * 300 + 200).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 平均房价
      pointsDeduction: Number(Math.random() * 500).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 积分抵现
      managerSalaryPrepaid: Number(Math.random() * 8000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 店长工资(预收)
      managerPerformanceSalary: Number(Math.random() * 3000).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) // 店长绩效工资
    }
  ]
}

export const table2Demo = [
  {
    branchNo: '1',

    name: '平均房价',
    val: Number(Math.random() * 300 + 200).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 平均房价,
    dw: '元'
  },
  {
    branchNo: '2',
    name: '积分抵现',
    val: Number(Math.random() * 3000 + 200).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 平均房价,
    dw: '元'
  },
  {
    branchNo: '3',
    name: '店长工资(预收)',
    val: Number(Math.random() * 3000 + 200).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 平均房价,
    dw: '元'
  },
  {
    branchNo: '4',
    name: '店长绩效工资',
    val: Number(Math.random() * 3000 + 200).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }), // 平均房价,
    dw: '元'
  }
]

