const toString = Object.prototype.toString

function is (val, type) {
  return toString.call(val) === `[object ${type}]`
}

function isDef (val) {
  return typeof val !== 'undefined'
}

function isUnDef (val) {
  return !isDef(val)
}

function isObject (val) {
  return val !== null && is(val, 'Object')
}

function isEmpty (val) {
  if (val === null || val === undefined || typeof val === 'undefined') {
    return true
  }
  if (isArray(val) || isString(val)) {
    return val.length === 0
  }

  if (val instanceof Map || val instanceof Set) {
    return val.size === 0
  }

  if (isObject(val)) {
    return Object.keys(val).length === 0
  }

  return false
}

function isDate (val) {
  return is(val, 'Date')
}

function isNull (val) {
  return val === null
}

function isNullAndUnDef (val) {
  return isUnDef(val) && isNull(val)
}

function isNullOrUnDef (val) {
  return isUnDef(val) || isNull(val)
}

function isNumber (val) {
  return is(val, 'Number')
}

function isPromise (val) {
  return is(val, 'Promise') && isObject(val) && typeof val.then === 'function' && typeof val.catch === 'function'
}

function isString (val) {
  return is(val, 'String')
}

function isFunction (val) {
  return typeof val === 'function'
}

function isBoolean (val) {
  return is(val, 'Boolean')
}

function isRegExp (val) {
  return is(val, 'RegExp')
}

function isArray (val) {
  return Array.isArray(val)
}

function isWindow (val) {
  return typeof window !== 'undefined' && is(val, 'Window')
}

function isElement (val) {
  return isObject(val) && !!val.tagName
}

function isMap (val) {
  return is(val, 'Map')
}

const isServer = typeof window === 'undefined'
const isClient = !isServer

function isUrl (path) {
  const reg =
    /(((^https?:(?:\/\/)?)(?:[-:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%#\/.\w-_]*)?\??(?:[-\+=&%@.\w_]*)#?(?:[\w]*))?)$/
  return reg.test(path)
}

// 是否是图片链接
function isImgPath (path) {
  return /(https?:\/\/|data:image\/).*?\.(png|jpg|jpeg|gif|svg|webp|ico)/gi.test(path)
}

function isEmptyVal (val) {
  return val === '' || val === null || val === undefined
}

export {
  is, isArray, isBoolean, isClient, isDate, isDef, isElement, isEmpty, isEmptyVal, isFunction, isImgPath, isMap, isNull,
  isNullAndUnDef,
  isNullOrUnDef,
  isNumber, isObject, isPromise, isRegExp, isServer, isString, isUnDef, isUrl, isWindow
}

