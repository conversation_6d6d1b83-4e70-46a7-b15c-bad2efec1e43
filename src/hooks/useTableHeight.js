import { debounce } from 'lodash'
import { nextTick, onBeforeUnmount, onMounted, ref, unref } from 'vue'
export function useTableHeight(containerRef, options = {}) {
  const tableHeight = ref(0)
  const isCalculating = ref(false) // 添加加载状态
  const { offset = 70, dynamicOffset = 0, debounceTime = 300 } = options // 增大防抖时间
  const calculateHeight = () => {
    if (isCalculating.value) return
    isCalculating.value = true
    const el = unref(containerRef)
    const domElement = el?.$el || el
    if (domElement) {
      requestAnimationFrame(() => {
        const rect = domElement.getBoundingClientRect()
        const elementOffsetTop = rect.top + window.scrollY
        const height = document.documentElement.clientHeight
        tableHeight.value = height - offset - elementOffsetTop - dynamicOffset
        isCalculating.value = false
      })
    }
  }
  const debouncedCalculateHeight = debounce(calculateHeight, debounceTime)

  const setupEventListeners = () => {
    nextTick(() => {
      calculateHeight()
      window.addEventListener('resize', debouncedCalculateHeight)
    })
  }

  // 处理组件卸载和失活时的逻辑
  const teardownEventListeners = () => {
    window.removeEventListener('resize', debouncedCalculateHeight)
  }

  onMounted(setupEventListeners)
  onBeforeUnmount(teardownEventListeners)

  // 处理缓存组件激活和失活的逻辑
  onActivated(setupEventListeners)
  onDeactivated(teardownEventListeners)
   
  const resetTableHeight = () => {
    nextTick(() => {
      calculateHeight()
    })
  }
  return { tableHeight, resetTableHeight }
}
