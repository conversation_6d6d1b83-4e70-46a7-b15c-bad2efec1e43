<template>
  <el-dialog v-model="dialogOpen" title="批量导入" width="600px" append-to-body>
    <el-form ref="modelRef" :model="form" :rules="rules" label-width="80px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="导入文件" prop="fileList">
            <el-upload
              ref="uplodRef"
              class="upload-demo"
              drag
              :auto-upload="false"
              action="#"
              accept=".xls,.xlsx"
              :on-change="handleChange"
              :file-list="form.fileList"
              :before-upload="onBeforeUpload"
              :multiple="multiple"
            >
              <el-icon class="el-icon--upload">
                <upload-filled />
              </el-icon>
              <div class="el-upload__text">
                仅支持xls、xlsx格式文件，格式可参考模板，
                <el-button type="primary" link @click.stop="downloadTemp">
                  下载模板
                </el-button>
              </div>
            </el-upload>
          </el-form-item>
        </el-col>              
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="uploadLoading" @click="submitUpload">
          确 认
        </el-button>
        <el-button @click="cancel">
          取 消
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="CommonUpload">
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },

  uploadLoading: {
    type: Boolean,
    default: false
  },

  multiple: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['downloadTemp', 'upload', 'update:open'])
const { proxy } = getCurrentInstance()

const data = reactive({
  form: {},
  rules: {
    fileList: [{ required: true, message: '请选择文件上传', trigger: 'change' }]
  }
})

const { form, rules } = toRefs(data)

const dialogOpen = computed({
  get () {
    return props.open
  },
  set (val) {
    emit('update:open', val)
  }
})

/** 取消按钮 */
const cancel = () => {
  dialogOpen.value = false
  reset()
}

const modelRef = ref(null)
/** 重置表单内容 */
const reset = () => {
  modelRef.value.resetFields()
}

const onBeforeUpload = (file) => {
  const isIMAGE = file.type === 'xls' || 'xlsx'
  if (!isIMAGE) {
    proxy.$modal.msgError('仅支持 xls, xlsx 格式文件!')
  }
  return isIMAGE
}

const handleChange = (file, fileList) => {
  if (props.multiple) {
    form.value.fileList = fileList
  } else {
    form.value.fileList = [file]
  }
  proxy.$refs['modelRef'].validate()
}

// 下载模板
const downloadTemp = () => {
  emit('downloadTemp')
}

// 导入
const submitUpload = () => {
  proxy.$refs['modelRef'].validate((valid) => {
    if (valid) {
      emit('upload', form.value.fileList)
    }
  })
}
</script>