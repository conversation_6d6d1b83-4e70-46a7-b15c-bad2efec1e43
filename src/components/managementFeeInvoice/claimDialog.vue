<template>
  <!-- 认领弹窗 -->
  <el-dialog title="款项认领" width="500" @closed="handleCancel">
    <el-form :model="confirmInfo">
      <el-form-item label="门店名称" label-width="100">
        <el-select
          v-model="shop.id"
          filterable
          remote
          remote-show-suffix
          reserve-keyword
          placeholder="请输入门店编号/门店名称"
          :remote-method="remoteMethod"
          :loading="searchLoading"
          style="width: 100%"
          @change="handleShopChange(shop.id)"
        >
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item
        v-for="item in bwo_type"
        :key="item.value"
        :label="item.label"
        label-width="100"
      >
        <el-input
          v-model="item.value2"
          autocomplete="off"
          placeholder="请输入金额"
          @blur="formatToTwoDecimals(item)"
        />
      </el-form-item>
      <el-form-item v-if="!formData.wbfkType" label="舞弊罚款" label-width="100">
        <!-- 该门店已舞弊闭环，且舞弊罚款金额和打款金额一致时，允许填写，
      如果未舞弊闭环，填写舞弊罚款时提醒：请先操作舞弊闭环后，再认领罚款
      如果舞弊打款和舞弊罚款金额不一致，填写舞弊罚款时提醒：舞弊罚款金额与认款金额不相符，请确认后再认领 -->
        <el-input
          v-model="formData.wbfk"
          autocomplete="off"
          :placeholder="formData.wbfkInfo"
          :disabled="formData.wbfkInfo == '舞弊罚款金额与认款金额不相符，请确认后再认领'"
        />
      </el-form-item>
      <el-form-item label="合计认领" label-width="100">
        <el-text :type="renlingType ? 'primary' : 'danger'" style="font-size: 28px">{{
          formData.thisMoney.toFixed(2)
        }}</el-text>
      </el-form-item>
      <el-form-item label="剩余金额" label-width="100">
        <span style="font-size: 20px; color: #999"> {{ props.data.noMoney.toFixed(2) }}</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleFormSure">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ElMessage } from 'element-plus'

import {
  claimOrder, // 认领订单
  getBankInfo, // 查询银行列表
  getPreBalance // 查询门店舞弊状态
} from '@/api/managementFeeInvoice/claimFunds.js'
import { formatYearMonth } from '@/utils/script.js'
// 获取门店列表方法
import { getSearchShopListFunc } from '@/utils/getSearchShopListFunc'
const { proxy } = getCurrentInstance()
const { bwo_type } = proxy.useDict('bwo_type')
for (let item of bwo_type.value) {
  item.value2 = ''
}
const emit = defineEmits(['close'])
const confirmInfo = ref({}) // 弹窗数据

const handleCancel = () => {
  emit('close', {
    type: 'close'
  })
}
const handleFormSure = () => {
  if (Number(formData.value.thisMoney) === 0) {
    ElMessage.error('至少填写一项费用!')
    return false
  }
  if (!renlingType.value) {
    if (props.data.noMoney > 0 && formData.value.thisMoney <= 0) {
      ElMessage.error('金额大于0，认款金额必须大于0')
    }
    if (props.data.noMoney < 0 && formData.value.thisMoney >= 0) {
      ElMessage.error('金额小于0，认款金额必须小于0')
    }
    if (props.data.noMoney > 0 && formData.value.thisMoney > props.data.noMoney) {
      ElMessage.error('认领金额不能超过剩余金额!')
    }
    if (props.data.noMoney < 0 && formData.value.thisMoney < props.data.noMoney) {
      ElMessage.error('认领金额不能小于剩余金额!')
    }
    return false
  }

  const params = formData.value
  params.claimOrderDetailList = []
  for (let item of bwo_type.value) {
    if (item.value2 !== '-') {
      params.claimOrderDetailList.push({
        typeCode: item.value,
        claimMoney: item.value2
      })
    }
  }
  if (formData.value.wbfk) {
    params.claimOrderDetailList.push({
      typeCode: '201',
      claimMoney: formData.value.wbfk
    })
  }

  claimOrder(params).then((res) => {
    if (res.code === 200) {
      emit('close', {
        type: 'success',
        shopId: shop.id.value,
        shopName: shop.id.label.split('-')[1]
      })
    } else {
      ElMessage.error(res.msg)
    }
  })
}
// const handleFormSure2 = () => {
//   if (!renlingType.value) {
//     ElMessage.error("认领金额不能超过剩余");
//   }
// };
// 接收参数
const props = defineProps({
  data: Object
})
const formData = ref({
  shopId: '', // 门店id
  waterOrderId: '', // 查询列表的id
  sysCode: '', //
  inBank: '', // 入账银行：查询列表的bankNum
  thisMoney: 0, // 认领列表
  claimOrderDetailList: [],
  wbfk: '', // 舞弊状态
  wbfkType: true, // 是否舞弊
  wbfkInfo: '请输入金额', // 罚款说明
  cheatAccount: '' // 舞弊罚款
})

const queryGetPreBalance = (id) => {
  if (!id) {
    // 判断打款一致性
    if (props.data.isSame === '1') {
      shop.id = {
        value: props.data.shopId,
        label: `${props.data.shopId}-${props.data.shopName}`
      }
    } else {
      shop.id = {
        value: '',
        label: ''
      }
    }
  }
  // console.log(shop.id);
  if (shop.id) {
    formData.value.shopId = shop.id.value
    // 查询门店舞弊状态
    getPreBalance({
      shopId: shop.id.value,
      atime: formatYearMonth(props.data.transactionDate) + '-01'
      // atime: "2025-05-01",
    }).then((res) => {
      // console.log(res);
      if (res.code === 200 && res.data) {
        formData.value.wbfkType = res.data.status !== '1'
        formData.value.cheatAccount = res.data.cheatAccount
        // if (formData.value.wbfkType) {
        //   formData.value.wbfkInfo = "请先操作舞弊闭环后，再认领罚款";
        // }
      }
    })
  }
}

onMounted(() => {
  // console.log(props.data);
  formData.value.waterOrderId = props.data.id
  formData.value.sysCode = props.data.sysCode

  queryGetPreBalance()

  // 去列表中查询银行编码
  getBankInfo().then((res) => {
    if (res.code === 200) {
      for (let item of res.data) {
        if (item.TYPENAME === props.data.bankName) {
          formData.value.inBank = item.TYPECODE
        }
      }
    }
  })
})

const renlingType = ref(false)
// 监控bwo_type的变化，将变化的值赋值给formData
watch(
  bwo_type,
  (newVal) => {
    // 当输入的值不是数字时，撤销非数字的字符
    // console.log("bwo_type变化了", newVal);
    for (let item of newVal) {
      if (item.value2) {
        // 如果是数字则不做处理
        if (!(/^-?(?:\d+(\.\d*)?|\.\d+)$/.test(item.value2) || item.value2 === '-')) {
          item.value2 = ''
        }
      }
    }
    // 求总数
    formData.value.thisMoney = 0
    for (let item of newVal) {
      if (item.value2 && item.value2 !== '-') {
        formData.value.thisMoney += item.value2 * 1
      }
    }
    // 如果认领金额不等于舞弊金额,则不能填写舞弊罚款
    if (formData.value.thisMoney !== formData.value.cheatAccount) {
      formData.value.wbfk = ''
      formData.value.wbfkInfo = '舞弊罚款金额与认款金额不相符，请确认后再认领'
    } else {
      formData.value.wbfkInfo = '请输入金额'
    }
  },
  { deep: true }
)
watch(
  formData,
  (newVal) => {
    // console.log("formData变化了", newVal);
    if (props.data.noMoney > 0) {
      renlingType.value = newVal.thisMoney > 0 && newVal.thisMoney <= props.data.noMoney
    }
    if (props.data.noMoney < 0) {
      renlingType.value = newVal.thisMoney < 0 && newVal.thisMoney >= props.data.noMoney
    }
  },
  { deep: true }
)

const shop = reactive({
  id: '',
  list: []
})
// 动态搜索select下拉框数据
const options = ref([])
const searchShopList = ref([])
// 加载中状态
const searchTime = ref(null)
const searchLoading = ref(false)
const remoteMethod = (query) => {
  // 获取所有门店名称和id信息
  if (searchShopList.value.length <= 0) {
    searchLoading.value = true
    getSearchShopListFunc().then((res) => {
      let obj = structuredClone(res)
      // 添加全选选项 门店id为-1 表示全部门店
      // obj.push({value:-1,label:'999999-全部门店'})
      searchShopList.value = obj
      searchLoading.value = false
    })
  }
  clearTimeout(searchTime.value)
  searchTime.value = setTimeout(() => {
    if (query && query.length >= 2) {
      // 过滤出所有包含查询字符串的选项 取前100条数据展示出来
      options.value = searchShopList.value
        .filter((item) => {
          return item.label.includes(query)
        })
        .slice(0, 500)
    } else {
      options.value = []
    }
  }, 300)
}
// 选中门店
const handleShopChange = (val) => {
  console.log('门店id:', val)
  // 判断是否已经添加了
  let isExist = shop.list.filter((item) => item.value === val.value)
  if (isExist.length > 0) {
    ElMessage.error(`${val.label} 该门店已添加!`)
  } else {
    shop.list.push(val)
  }
  // 选中后在下拉列表中删除
  // shop.id = "";
  formData.value.wbfk = ''
  options.value = []
  queryGetPreBalance(shop.id)
}
// 处理输入的数据
function formatToTwoDecimals(item) {
  if (item.value2) {
    // 删除结尾的 .
    if (item.value2.endsWith('.')) {
      item.value2 = item.value2.slice(0, -1)
    }
    item.value2 = Number(item.value2).toFixed(2) // 保留两位小数
  }
}
</script>
