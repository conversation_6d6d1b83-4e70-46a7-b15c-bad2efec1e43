<template>
  <el-dialog 
    v-model="chargingManager.verificationFormulaType" 
    title="核验公式" 
    width="800"
    style="height:80%"
    :before-close="handleClose"
  >
    <el-row :gutter="10">
      <el-col :span="12">
        <el-row>
          <el-col>
            <el-select
              v-model="shop.id"
              filterable
              remote
              remote-show-suffix
              reserve-keyword
              placeholder="请输入门店编号/门店名称"
              :remote-method="remoteMethod"
              :loading="chargingManager.search"
              style="width: 100%"
              @change="handleShopChange(shop.id)"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item"
              />
            </el-select>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="12">
        <el-date-picker
          v-model="month"
          type="month"
          placeholder="选择月份"
        />
        <el-button 
          type="primary" 
          style="margin-left: 10px;" 
          :loading="loading.search"
          @click="submit()"
        >
          开始对比
        </el-button>
      </el-col>
    </el-row>
    <el-row ref="shopList" style="max-height:200px;overflow-y: auto;margin-top: 10px;">
      <el-col style="">
        <!-- 选中的门店 -->
        <el-tag 
          v-for="(item,index) in shop.list" 
          :key="item.name" 
          closable 
          type="primary"
          size="large"
          style="margin:0 10px 10px 0;"
          @close="deleteShop(index)"
        >
          {{ item.label }}
        </el-tag>
      </el-col>
    </el-row>
    <div style="padding:0 20px;">
      <el-divider style="margin:10px 0;" />
    </div>
    <div style="overflow-y: auto;" :style="{height: autoHeight+'px'}">
      <div v-for="(item,index) in datatable" :key="index">
        <div style="padding:10px;" >
          <b style="margin-right: 5px;">
            {{ item.shopId }}
            {{ item.shopName }}
          </b>
          <el-tag v-if="item.compareResult" type="success">
            核验成功
          </el-tag>
          <el-tag v-else type="danger">核验失败</el-tag>
        </div>
        <!-- 如果成功则不显示表格 -->
        <el-table 
          v-if="!item.compareResult" 
          v-loading="loading.search" 
          :data="item.recordList"
          style="width: 99%"
        >
          <el-table-column prop="chargeCode" label="费用编号" width="100"></el-table-column>
          <el-table-column prop="chargeName" label="费用名称" width="180"></el-table-column>
          <el-table-column prop="compareResult" label="对比结果" >
            <template #default="{ row }">
              <el-tag v-if="row.compareResult" type="success">核验成功</el-tag>
              <el-tag v-else type="danger">核验失败</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="chargePrice" label="公式计算金额" ></el-table-column>
          <el-table-column prop="feeBillPrice" label="账单金额" ></el-table-column>
        </el-table>
      </div>
    </div>

  </el-dialog>
</template>
<script setup>
// 费用调整弹窗

import { ElMessage } from 'element-plus'
import { watch } from 'vue'

import { verificationFormulaList } from '@/api/chargingManager/search'
// 获取门店列表方法
import chargingManagerStore from '@/store/modules/ChargingManager'
import { getSearchShopListFunc } from '@/utils/getSearchShopListFunc'
const chargingManager = chargingManagerStore()
    
// 每次重新打开时重置数据
watch(
  () => chargingManager.verificationFormulaType,
  () => {
    shop.list = []
    month.value = ''
    datatable.value = []
  }
)
   
// 选中的月份
const month = ref('')
const shop = reactive({
  id: '',
  list: []
})
const datatable = ref([])
// 加载中状态
const searchTime = ref(null)
// 动态搜索select下拉框数据
const options = ref([])
const searchShopList = ref([])
const remoteMethod = (query) => {
  // 获取所有门店名称和id信息
  if(searchShopList.value.length <= 0){
    chargingManager.search = true
    getSearchShopListFunc().then((res) => {
      let obj = structuredClone(res)
      // 添加全选选项 门店id为-1 表示全部门店
      // obj.push({value:-1,label:'999999-全部门店'})
      searchShopList.value = obj
      chargingManager.search = false
    })
  }
  clearTimeout(searchTime.value)
  searchTime.value = setTimeout(() => {
    if (query && query.length >= 2) {
      // 过滤出所有包含查询字符串的选项 取前100条数据展示出来
      options.value = searchShopList.value.filter((item) => {
        return item.label.includes(query)
      }).slice(0, 500)
    } else {
      options.value = []
    }
  }, 300)
}
// 选中门店
const handleShopChange = (val) => {
  // console.log("门店id:",val);
  // 判断是否已经添加了
  let isExist = shop.list.filter((item) => item.value === val.value)
  if(isExist.length > 0){
    ElMessage.error(`${val.label} 该门店已添加!`)
  }else{
    shop.list.push(val)
  }
  // 选中后在下拉列表中删除
  shop.id = ''
  options.value = []
  setHeight()
}
// 删除选中门店
const deleteShop = (index) => {
  shop.list.splice(index, 1)
  setHeight()
}
const loading = reactive({
  search: false
})
// 提交表单
const submit = () => {
  if(loading.search){
    return false
  }
  if(shop.list.length <= 0){
    ElMessage.error('请选择门店!')
    return
  }
  if(!month.value){
    ElMessage.error('请选择月份!')
    return
  }
  loading.search = true
  const date = new Date(month.value)
  const params = {
    shopId: shop.list.map((item) => item.value).join(','),
    atime: `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-01`
  }
  // console.log("提交数据:", params);
  verificationFormulaList(params).then((res) => {
    loading.search = false
    // console.log("核验公式返回数据:", res);
    datatable.value = res.data
  })
}
// 关闭弹窗
const handleClose = () => {
  chargingManager.verificationFormulaType = false
}
// 自动高度
const autoHeight = ref(300)
const shopList = ref(null)
const setHeight = () => {
  setTimeout(() => {
    if (shopList.value) {
      const height = shopList.value.$el.offsetHeight // 获取高度
      autoHeight.value = document.documentElement.clientHeight * 0.8 - height - 135
      // console.log('自动高度:', autoHeight.value);
    }
  }, 50)
}
</script>