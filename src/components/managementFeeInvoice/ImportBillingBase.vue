<template>
  <el-dialog 
    v-model="store.type" 
    :title="store.typeName" 
    width="800"
    :before-close="handleClose"
  >
    <div class="p-30">
      <el-form label-width="100">
        <el-form-item label="导入文件" prop="file">
          <div class="flex flex-jus-start flex-item-start" style="width: 100%;">
            <el-button type="primary" plain class="mr-10" @click="downloadDemo">下载模板</el-button>
            <el-upload
              v-model:file-list="formData.fileList"
              class="upload-demo"
              style="flex: 1;"
              action="/"
              multiple
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :limit="1"
              :on-exceed="handleExceed"
              :auto-upload="false"
            >
              <el-button type="primary">上传文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传xls文件
                </div>
              </template>
            </el-upload>
                        
          </div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="formData.loading" @click="submitForm">导入</el-button>
        <el-button @click="store.type = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
// 计费基数导入弹窗

import { upload } from '@/api/managementFeeInvoice/importBillingBase.js'
import useManagementFeeInvoiceStore from '@/store/modules/ManagementFeeInvoice'
import { download } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import { watch } from 'vue'
const store = useManagementFeeInvoiceStore()

// 每次重新打开时重置数据
watch(
  () => store.type,
  () => {
    formData.fileList = []
  }
)

// 表单数据
const formData = reactive({
  fileList: [], // 附件列表
  loading: false
})
    
// 提交表单
const submitForm = () => {
  if(formData.loading){
    return false
  }
  if (formData.fileList.length === 0) {
    ElMessage.warning('请上传文件')
    return
  }
  formData.loading = true

  console.log('提交表单:', formData)

  let params = new FormData()
  params.append('file', formData.fileList[0].raw)
  params.append('type', store.typeId)
  params.append('atime', store.atime)
               
  upload(params).then((res) => {
    console.log('上传成功:', res)
    formData.loading = false
    if(res.code == 200){
      ElMessage.success(res.msg)
      // ElMessageBox.alert(
      //     res.msg, 
      //     '提示', 
      //     {
      //     confirmButtonText: '确定',
      // })
      store.type = false
    }else{
      ElMessage.error(res.msg)
    }
  }).catch((err) => {
    console.log('上传失败:', err)
    formData.loading = false
    // ElMessage.error(err);
  })

}

// 移除已上传
const handleRemove = (file, uploadFiles) => {
  console.log(file, uploadFiles)
}

// 已上传文件的点击
const handlePreview = (uploadFile) => {
  console.log('handlePreview:', uploadFile)
}

// 超出限制
const handleExceed = (files, uploadFiles) => {
  ElMessage.warning(
    `先移除后再重新上传`
  )
}

// 移除文件之前的钩子，询问用户是否删除
const beforeRemove = (uploadFile, uploadFiles) => {
  return ElMessageBox.confirm(
    `确定要删除 ${uploadFile.name} 吗?`,
    '警告',
    {
      type: 'warning'
    }
  ).then(
    () => true,
    () => false
  )
}
// 下载模板
const downloadDemo = () => {
  // type 明细如下
  let arr = [
    {id: 1, name: '店长工资实收实付季度调整', type: 'f28'},
    {id: 2, name: '店长绩效', type: 'f16'},
    {id: 3, name: '营销经理工资实收实付季度调整', type: 'f38'},
    {id: 4, name: '营销经理绩效', type: 'f36'},
    {id: 5, name: '舆情系统使用费', type: 'f26'},
    {id: 6, name: '售卡金额', type: 'f32'},
    {id: 16, name: '售卡返佣金额', type: 'f23'},
    {id: 17, name: '店长基本工资', type: 'f19'},
    {id: 18, name: '营销经理基本工资', type: 'f39'}
  ]
  // console.log("下载模板:",arr,store.typeId,arr.find(item => item.id == store.typeId))
  if(store.typeId){
    const params = {
      type: store.typeId,
      name: arr.find((item) => item.id == store.typeId)['name']
    }
    const url = `/shop/baseImport/downloadTemplate?type=${params.type}`
    // console.log("下载模板地址:",url);
    download(url, {}, `${params.name}_${new Date().getTime()}.xls`)
  }
}
// 关闭弹窗事件
const handleClose = () => {
  store.type = false
}
</script>