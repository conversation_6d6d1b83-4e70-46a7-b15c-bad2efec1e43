<template>
  <div>
    <el-input
      v-if="feeType.calculateType == 'ratio'"
      v-model="feeType.percentNum"
      placeholder="输入值"
      :style="{width:(LadderConditionsStore.formData.name == 'transport')?'300px':'260px'}"
    >%
      <template #prepend>
        <!-- 商标 会员运输显示下拉 -->
        <el-select
          v-if="options.length > 0"
          v-model="feeType.paramType"
          clearable
          style="width: 120px;background-color: #fff"
          class="input-with-select"
        >
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.title"
            :value="item.value"
          ></el-option>
        </el-select>
        <template v-else>
          营业收入*
        </template>
      </template>
      <template #append>
        <template
          v-if="
            LadderConditionsStore.formData.name == 'transport' &&
              feeType.paramType == 'nightNum'
          "
        >元/间夜</template>
        <template v-else>%</template>
      </template>
    </el-input>
    <!-- 如果是比例 且是运输 则隐藏折扣 -->
    <el-input
      v-if="
        feeType.calculateType == 'ratio' &&
          LadderConditionsStore.formData.name != 'transport'
      "
      v-model="feeType.discountNum"
      style="width: 190px;margin-left:10px;"
      placeholder="输入值"
      class="countBox"
    >%
      <template #prepend>折扣</template>
      <template #append>%</template>
    </el-input>
  </div>
</template>
<script setup>
// 比例组件

import { brandTypeList } from '@/utils/constants.js'
// 引入响应式数据
import useLadderConditionsStore from '@/store/modules/LadderConditions'
const LadderConditionsStore = useLadderConditionsStore()
const props = defineProps(['feeType'])
// 比例的下拉选项筛选
const options = ref([])
// 设置比例的下拉选项筛选
let name = LadderConditionsStore.formData.name
if(['member', 'trademark', 'transport'].includes(name)) {
  options.value = brandTypeList[name]
}

// 验证默认值是否在下拉列表中，如果不是则设置为空
if(options.value.length > 0 && options.value.filter((item) => item.value == props.feeType.paramType).length <= 0) {
  props.feeType.paramType = options.value[0].value
}else if(options.value.length > 0 && options.value.filter((item) => item.value == props.feeType.paramType).length > 0){
  // 找到了匹配项，不需要更改默认值
}else{
  // 如果没有选项则设置默认值
  props.feeType.paramType = 'gmv'
}
</script>