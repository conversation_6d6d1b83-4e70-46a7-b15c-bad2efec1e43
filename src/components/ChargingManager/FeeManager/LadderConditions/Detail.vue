<template>
  <div class='flex flex-jus-start'>
    <div class='flex flex-jus-start'>
      <div class='flex flex-jus-start'>
        <el-input
          v-model="props.datas.fee.minValue"
          placeholder="输入值"
          class="input-with-select"
          :style="{width:classic.width}"
        >
          <template #append>{{ classic.unit }}</template>
        </el-input>
        <el-select
          v-model="props.datas.fee.symbolGreaterThan"
          clearable
          placeholder="选择"
          style="width: 80px; margin-left:10px;"
        >
          <el-option
            v-for="item in leList"
            :key="item.id"
            :label="item.value"
            :value="item.id"
          />
        </el-select>
      </div>
      <span style="padding: 0 10px;">{{ classic.describe }}</span>
      <div class='flex flex-jus-start'>
        <el-input
          v-model="props.datas.fee.maxValue"
          placeholder="输入值"
          class="input-with-select"
          :style="{ width: classic.width2 }"
        >
          <template #prepend>
            <el-select
              v-model="props.datas.fee.symbolLessThan"
              placeholder="选择"
              clearable
              style="width: 80px; background-color: #fff"
            >
              <el-option
                v-for="item in leList"
                :key="item.id"
                :label="item.value"
                :value="item.id"
              />
            </el-select>
          </template>
          <template #append>{{ classic.unit }}</template>
        </el-input>
      </div>

    </div>
    <!-- 添加删除按钮 -->
    <div style="margin-left: 10px;flex:1;">
      <el-button @click="addFeeItem(datas.index, datas.itemIndex)">
        <el-icon><Plus /></el-icon>
      </el-button>
      <el-button
        v-if="datas.feeIndex !== 0"
        type="danger"
        plain
        @click="LadderConditionsStore.removeFeeItem(datas.index, datas.feeIndex, datas.itemIndex)"
      >
        <el-icon><CloseBold /></el-icon>
      </el-button>
    </div>
  </div>

</template>
<script setup>
// 阶梯条件 出租率 营业收入 房量 单房收益
// 引入常量
import {
  addFeeItemDatas,
  leList
} from '@/utils/constants.js'

// 引入 store
import useLadderConditionsStore from '@/store/modules/LadderConditions'
const LadderConditionsStore = useLadderConditionsStore()
const props = defineProps(['datas', 'classic'])
// 添加阶梯条件费用项
const addFeeItem = (index, itemIndex) => {
  LadderConditionsStore.addFeeItem(index, itemIndex, structuredClone(addFeeItemDatas))
}
</script>