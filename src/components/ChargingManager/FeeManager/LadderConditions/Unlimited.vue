<script setup>
// 类型组件

import Guarantee from '@/components/ChargingManager/FeeManager/LadderConditions/Guarantee'
import Quota from '@/components/ChargingManager/FeeManager/LadderConditions/Quota'
import Ratio from '@/components/ChargingManager/FeeManager/LadderConditions/Ratio'
import Special from '@/components/ChargingManager/FeeManager/LadderConditions/Special'
import { sourceTypeList } from '@/utils/constants.js'
const props = defineProps(['datas'])
</script>

<template>
  <div v-for="feeType in props.datas.fee.feeTypeList" class="flex flex-jus-start flex-item-start">
    <el-select
      v-model="feeType.calculateType"
      placeholder="选择"
      clearable
      style="width: 80px"
    >
      <el-option
        v-for="item in sourceTypeList"
        :key="item.id"
        :label="item.title"
        :value="item.value"
      ></el-option>
    </el-select>
    <div class="ml-10 flex flex-column flex-jus-start flex-item-start" style="flex:1;">
      <!-- 比例组件 -->
      <Ratio v-if="feeType.calculateType == 'ratio'" :fee-type="feeType" />
      <!-- 定额组件 -->
      <Quota v-if="feeType.calculateType == 'quota'" :fee-type="feeType" />
      <!-- 特殊组件 -->
      <Special v-if="feeType.calculateType == 'special'" :fee-type="feeType" />
            
      <div v-for="(itemDetail, minIndex) in feeType.minGuarantee.detailList"> 
        <!-- 保底组件 定额组件 组合组件 -->
        <Guarantee 
          v-if="['guarantee','cap','combine'].includes(feeType.calculateType)" 
          :fee-type="itemDetail" 
        />
        <div  v-if="feeType.calculateType == 'combine' && minIndex == 0" style="color:red;margin-bottom: 10px;padding-left:30px;">
          +
        </div>
      </div>

    </div>
  </div>
</template>