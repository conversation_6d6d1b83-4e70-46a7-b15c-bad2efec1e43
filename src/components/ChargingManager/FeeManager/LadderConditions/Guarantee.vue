<template>
  <div class="flex" style="margin-bottom:10px;">
    <el-select
      v-model="feeType.calculateType"
      placeholder="选择"
      clearable
      style="width: 80px;margin-right:10px;"
    >
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.title"
        :value="item.value"
      ></el-option>
    </el-select>

    <div class='flex'>
      <!-- 比例组件 -->
      <Ratio v-if="feeType.calculateType == 'ratio'" :fee-type="feeType" />
    </div>
    <div class='flex'>
      <!-- 特殊组件 -->
      <Special v-if="feeType.calculateType == 'special'" :fee-type="feeType" />
    </div>
    <div class='flex'>
      <!--定额组件 -->
      <Quota v-if="feeType.calculateType == 'quota'" :fee-type="feeType" />
    </div>
  </div>
</template>

<script setup >
// 保底组件 封顶组件 组合组件
import { sourceTypeList } from '@/utils/constants.js'
// 细分选项过滤
const options = sourceTypeList.filter((item) => ([1, 2, 4].includes(item.id)))
// 引入组件
import Quota from '@/components/ChargingManager/FeeManager/LadderConditions/Quota'
import Ratio from '@/components/ChargingManager/FeeManager/LadderConditions/Ratio'
import Special from '@/components/ChargingManager/FeeManager/LadderConditions/Special'
const props = defineProps(['feeType'])

</script>