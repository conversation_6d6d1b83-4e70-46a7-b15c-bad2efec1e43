<template>
  <div>
    <!-- 定额后的第一个下拉框 -->
    <el-select
      v-model="feeType.quotaType"
      clearable
      style="width: 145px"
      @change="handleCurrentChange"
    >
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.title"
        :value="item.value"
      ></el-option>
    </el-select>
    <!-- 被锁定 只有依据房量计算显示 -->
    <template v-if="feeType.quotaType == 'roomNum'">
      <el-input
        v-model="feeType.roomValue"
        disabled
        style="width: 126px;margin-left: 10px;"
        placeholder="实际房量"
        class="countBox"
      >%
        <template #append
        ><span style="text-align: center">*</span></template
        >
      </el-input>
      <!-- 依据房量计算 -->
      <el-input
        v-model="feeType.roomPrice"
        style="width: 180px;margin-left: 10px;"
        placeholder="输入值"
        class="countBox"
      >%
        <template #append>
          <span style="text-align: center; width: 36px">元/间/月</span>
        </template>
      </el-input>
    </template>

    <!-- 固定金额 -->
    <el-input
      v-if="['fixedPrice','fixedPriceRatio'].includes(feeType.quotaType)"
      v-model="feeType.fixedPrice"
      style="width: 190px;margin-left: 10px;"
      placeholder="输入值"
      class="countBox"
    >%
      <template #append>
        <el-select v-model="feeType.calculateUnit" placeholder="选择" style="width: 80px;">
          <el-option 
            v-for="item in feeCycleList"
            :key="item.id"
            :label="item.title" 
            :value="item.value"
          ></el-option>
        </el-select>
      </template>
    </el-input>

    <!-- 约定房量 只有会员有约定房量 -->
    <el-input
      v-if="feeType.quotaType == 'fixedRoom'"
      disabled
      style="width: 120px;margin-left: 10px;"
      placeholder="*80"
      class="countBox"
    >%
      <template #append>
        <span style="text-align: center; width: 36px">元/间/月</span>
      </template>
    </el-input>
    <!-- 只有会员有实际房量  -->
    <el-input
      v-if="feeType.quotaType == 'roomNumRatio'"
      style="width: 240px"
      placeholder="实际房量*15%*"
      disabled
      class="countBox ml-10"
    >%
      <template #append>
        <span style="text-align: center">80元/间/月</span>
      </template>
    </el-input>

    <!-- 限时折扣 只有ai有限时折扣 -->
    <template v-if="feeType.quotaType == 'limitDiscount'">
      <el-input
        v-model="feeType.fixedPrice"
        style="width: 180px;margin-left: 10px;"
        placeholder="输入值"
        class="countBox"
      >%
        <template #append>
          <el-select v-model="feeType.calculateUnit" placeholder="选择" style="width: 80px;">
            <el-option 
              v-for="item in feeCycleList"
              :key="item.id"
              :label="item.title" 
              :value="item.value"
            ></el-option>
          </el-select>
        </template>
      </el-input>

      <el-input 
        v-model="feeType.freePrice"
        style="width: 240px;margin-left: 10px;"
        placeholder="输入值"
        class="countBox"
      >%
        <template #prepend>
          <span style="display: inline-block;">减免</span>
        </template>
        <template #append>
          <el-select v-model="feeType.calculateUnit" placeholder="选择" style="width: 80px;">
            <el-option 
              v-for="item in feeCycleList"
              :key="item.id"
              :label="item.title" 
              :value="item.value"
            ></el-option>
          </el-select>
        </template>
      </el-input>
    </template>

    <!-- 只有ai的固定金额才有折扣 -->
    <el-input
      v-if="feeType.quotaType == 'fixedPriceRatio'"
      v-model="feeType.discountNum"
      style="width: 160px;margin-left: 10px;"
      placeholder="输入值"
      class="countBox"
    >%
      <template #prepend>
        <span style="display: inline-block; width: 12px; text-align: left">折扣</span>
      </template>
      <template #append>
        <span style="text-align: center; display: inline-block; width: 2px">%</span>
      </template>
    </el-input>

  </div>
</template>
<script setup>
// 定额组件
const props = defineProps(['feeType'])
import { feeCycleList, quotaTypeList } from '@/utils/constants.js'
// 引入响应式数据
import useLadderConditionsStore from '@/store/modules/LadderConditions'
const LadderConditionsStore = useLadderConditionsStore()

// 比例的下拉选项筛选
const options = ref([])

// 修改下拉选项
if(['member', 'ai'].includes(LadderConditionsStore.formData.name)){
  options.value = quotaTypeList[LadderConditionsStore.formData.name]
}else{
  options.value = quotaTypeList['default']
}

const handleCurrentChange = (val) => {
  // 当类型为运营质检费->定额时候 单位默认为 元/年
  if(LadderConditionsStore.formData.name === 'check' && val === 'fixedPrice'){
    props.feeType.calculateUnit = 2
  }
  props.feeType.paramType = val
}

// 验证默认值是否在下拉列表中，如果不是则设置为空
if(options.value.length > 0 && options.value.filter((item) => item.value == props.feeType.quotaType).length <= 0) {
  props.feeType.quotaType = options.value[0].value
}
</script>