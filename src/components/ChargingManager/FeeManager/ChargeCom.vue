<template>
  <!-- 添加或修改用户配置对话框 -->
  <el-card
    style="width: max(80%,900px)"
    class="mt-15"
  >
    <el-form 
      ref="chargeRef" 
      :model="LadderConditionsStore.formData" 
      label-width="100px"
      :disabled="props.datas.preview"
    >
      <el-row>
        <el-col :span="10">
          <el-form-item label="费用名称" prop="name">
            <el-tree-select
              v-model="LadderConditionsStore.formData.name"
              disabled
              :data="tabList"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" style="margin-left: 20px">
          <el-form-item label="账单显示名称" prop="billName">
            <el-input
              v-model="LadderConditionsStore.formData.billName"
              style="width: 100%"
              placeholder="不填写则显示默认费用名称"
            />
          </el-form-item>
        </el-col>
     
      </el-row>
      <div
        v-for="(item, itemIndex) in LadderConditionsStore.timeList"
        :key="itemIndex"
        :class="itemIndex > 0 ? 'dateBox' : ''"
      >
        <!-- 选择时间 -->
        <el-row>
          <el-form-item
            label="选择时间"
            class="is-required asterisk-left"
            style="width: 100%;"
          >
            <div style="width: 100%;">
              <el-row>
                <el-col :span="10">
                  <el-select
                    v-model="item.timeType"
                    placeholder="请选择"
                    class="full-width"
                    value-key="id"
                    @change="selectTimeType($event, itemIndex)"
                  >
                    <el-option
                      v-for="itemOption in timeOptions"
                      :key="itemOption.id"
                      :label="itemOption.title"
                      :value="itemOption"
                    />
                  </el-select>
                </el-col>

                <el-col :span="5" style="padding-left: 15px; width: 120px">
                  <!-- 长期不显示 添加一组数据 -->
                  <el-button
                    v-if="item.timeType.id > 0"
                    @click="addDateItem(itemIndex)"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-button>
                  <!-- 如果是添加的数据则可以删除 -->
                  <el-button
                    v-if="itemIndex !== 0"
                    type="danger"
                    plain
                    @click="removeDateItem(itemIndex)"
                  >
                    <el-icon><CloseBold /></el-icon>
                  </el-button>
                </el-col>
              </el-row>
              <!-- 时间选择器 -->
              <el-row>
                <!-- 固定月份和固定日期显示 -->
                <el-col
                  v-if="['dateRange', 'dateRangeMonth'].includes(item.timeType.type)"
                  :span="24"
                  class="line50"
                >
                  <div style="display: flex;flex-direction: row;justify-content: flex-start;align-items: center;">
                    <div>
                      <el-date-picker
                        v-model="item.dateRange"
                        type="daterange"
                        unlink-panels
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        style="width: 260px"
                        :shortcuts="shortcutsLengDay"
                      />
                    </div>
                    <!-- 固定月份显示 -->
                    <div>
                      <el-select
                        v-if="['dateRangeMonth'].includes(item.timeType.type)"
                        v-model="item.months"
                        multiple
                        placeholder="选择月份"
                        style="margin-left: 20px;min-width: 120px;max-width: 350px;"
                      >
                        <el-option
                          v-for="item in monthList()"
                          :key="item.id"
                          :label="item.title"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </div>
                  </div>
                </el-col>
              </el-row>

            </div>
          </el-form-item>
        </el-row>
        <div v-for="(shopManager, index) in item.shopManagerList" :key="index">
          <!-- 人员在店 -->
          <el-row>
            <el-col :span="22">
              <el-form-item label="人员在店" class="is-required asterisk-left">
                <el-select
                  v-model="shopManager.shopManagerStatus"
                  placeholder="请选择是否在店"
                  clearable
                  :disabled="shopManager.disabled"
                  style="width: 400px"
                  @change="selectShopManager($event, index, itemIndex)"
                >
                  <el-option
                    v-for="item in shopManagerStatusList"
                    :key="item.id"
                    :label="item.title"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row
            v-if="[1,4,5].includes(shopManager.shopManagerStatus)"
          >
            <el-form-item v-model="shopManager.shopManagerStatus" label="在店天数">
              <el-col>
                <el-input
                  v-model="shopManager.workDays"
                  placeholder="天数"
                  style="width: 220px"
                >
                  <template #prepend>
                    <el-select
                      v-model="shopManager.workDaySymbol"
                      placeholder="选择符号"
                      style="width: 100px;background-color: white;"
                    >
                      <el-option
                        v-for="item in workDaySymbolList"
                        :label="item.title"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                  <template #append> 天 </template>
                </el-input>
              </el-col>
            </el-form-item>
          </el-row>
          <!-- 阶梯条件 -->
          <el-row>
            <el-form-item label="阶梯条件" class="is-required">
              <el-radio-group
                v-model="shopManager.betType[0]"
                @change="handleBet($event, index, itemIndex)"
              >

                <template v-for="item in ladderConditionList">
                  <el-radio 
                    v-if="item.visible || item.condition.includes(LadderConditionsStore.formData.name)"
                    :key="item.value"
                    :value="item.label"
                  >{{ item.value }}</el-radio>
                </template>
              </el-radio-group>
            </el-form-item>
          </el-row>

          <!-- 调整开始 -->
          <el-card
            v-for="(fee, feeIndex) in shopManager.feeList"
            :key="feeIndex"
            shadow="never"
            class="cardBg"
            style="font-size: 14px;"
          >

            <!-- 阶梯条件的组件 -->
            <div v-if="shopManager.betType[0] != 'unlimited'">
              <!-- 标题 无限制不显示标题 -->
              <div style="padding:10px 0;">
                <!-- {{ shopManager.betType }} - {{ ladderConditionList }} - {{ ladderConditionList.filter(item=>item.label == shopManager.betType) }} -->
                {{ ladderConditionList.filter(item=>item.label == shopManager.betType)[0]['value'] }}:
              </div>
              <!-- 条件选择 -->
              <Detail 
                :datas="{fee, feeIndex, index, itemIndex}" 
                :classic='ladderConditionList.filter(item=>item.label == shopManager.betType)[0]' 
              />
            </div>
            <!-- 类型 -->
            <div>
              <div style="padding:10px 0;">
                类型:
              </div>
              <Unlimited 
                :datas="{
                  fee,
                  feeIndex, 
                  index, 
                  itemIndex
                }" 
              />
            </div>

            <!-- 计算公式 -->
            <div v-if="fee.calculateExpression">
              <el-row class="calcBox">
                相应公式：<el-text class="mx-1" style="color: #d75c48">{{
                  fee.calculateExpression
                }}</el-text>
              </el-row>
              <el-row class="calcBox">
                公式说明：<el-text class="mx-1" style="color: #d75c48">{{
                  fee.calculateDesc
                }}</el-text>
              </el-row>
            </div>
            <div>
              <div style="padding:10px 0;">费用说明：</div>
              <el-input v-model="fee.calculateCustomDesc" type="text" style="width: max(70%,590px);"></el-input>
            </div>
          </el-card>
          <!-- 调整结束 -->
        </div>

        <el-row>
          <el-col :span="24">
            <el-form-item
              class="is-required"
              label="批&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;次"
            >
              <el-input-number v-model="item.batchNumber" :min="1" :max="10000" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注"
            >
              <el-input
                v-model="item.remark"
                type="textarea"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <div class="flex flex-jus-end">
      <el-button 
        v-if='!props.datas.preview' 
        type="primary"
        :loading="submitLoading"
        @click="submitForm"
      >确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-card>
</template>
<script setup name="chargeCom">
import {
  addCharge,
  getChargeInfo
} from '@/api/chargingManager/FeeManager/index'
import store from '@/store'
import {
  addBet // 阶梯条件切换
  , // 详情数据格式
  addDateItemDatas, // 选择时间后的加号
  addShopManager, // 阶梯条件对照表
  initialTimeList, // 时间快捷选项
  ladderConditionList, // 是否在店选项
  monthList, // 选择时间选项
  shopManagerStatusList,
  shortcutsLengDay,
  tabList, // 类型对照表
  timeOptions, // 月份对照表
  workDaySymbolList
} from '@/utils/constants.js'
import { ElMessage } from 'element-plus'

// 引入组件
import Detail from '@/components/ChargingManager/FeeManager/LadderConditions/Detail'
import Unlimited from '@/components/ChargingManager/FeeManager/LadderConditions/Unlimited'
// 引入响应式数据
import useLadderConditionsStore from '@/store/modules/LadderConditions'
const LadderConditionsStore = useLadderConditionsStore()
// 接收父组件传递的数据
const props = defineProps(['datas'])
// console.log("props", props);

// 深拷贝
LadderConditionsStore.timeList = structuredClone(initialTimeList)
// 表单提交数据
LadderConditionsStore.formData.name = tabList.filter((item) => item.label == props.datas.feeName)[0].value// 获取收费信息
// 执行父层组件的关闭事件
const emit = defineEmits(['openType'])
/** 取消按钮 */
function cancel() {
  // console.log('关闭子组件')
  emit('openType', false) // 触发自定义事件并传递数据
}

/** 默认获取数据 */
function initFunc(row) {
  getChargeInfo(row.id).then((response) => {
    // console.log("获取回显数据:", response.data.timeList);
    LadderConditionsStore.formData.name = response.data.name
    LadderConditionsStore.formData.billName = response.data.billName
    LadderConditionsStore.formData.id = response.data.id

    LadderConditionsStore.timeList.length = 0
    LadderConditionsStore.timeList.push(response.data.timeList[0])
    console.log(LadderConditionsStore.timeList, 'LadderConditionsStore.timeList')
    LadderConditionsStore.timeList?.[0].shopManagerList.forEach((v) => {
      console.log(v, 'v')
      if([2, 6, 7].includes(v.shopManagerStatus)) v.disabled = true
    })
  })
}
// 如果是编辑或预览则默认调用
// console.log("props", props.datas.chargeName);
if(props.datas.chargeName){
  initFunc(props.datas)
}else{
  // 新增则重置数据
  LadderConditionsStore.formData.id = ''
  LadderConditionsStore.formData.billName = ''

}

const submitLoading = ref(false)

/** 提交按钮 */
function submitForm() {
  if(submitLoading.value){
    return false
  }
  let timeList = LadderConditionsStore.timeList
  //校验时间不可为空
  for (let i = 0; i < timeList.length; i++) {
    if ( timeList[i].dateRange == null || timeList[i].dateRange.length == 0) {
      ElMessage.error('执行失败，请选择时间')
      return
    }
    //校验店长在店
    if (timeList[i].shopManagerList.length == 0) {
      ElMessage.error('执行失败，请选择店长在店状态')
      return
    }
    for (let j = 0; j < timeList[i].shopManagerList.length; j++) {
      const shopManager = timeList[i].shopManagerList[j]
      // 原有校验逻辑
      if (
        shopManager.shopManagerStatus === null ||
        shopManager.shopManagerStatus === undefined ||
        shopManager.shopManagerStatus === '' ||
        shopManager.shopManagerStatus.length === 0 
      ) {
        ElMessage.error('执行失败，请选择人员是否在店')
        return
      }
      // 校验对赌条件
      if (shopManager.betType[0] === null || shopManager.betType[0] === undefined || shopManager.betType[0] === '') {
        ElMessage.error('执行失败，请选择阶梯条件')
        return
      }
    }
    //校验批次
    if (!timeList[i].batchNumber) {
      ElMessage.error('执行失败，请填写批次')
      return
    }
  }

  timeList[0].name = LadderConditionsStore.formData.name
  LadderConditionsStore.formData.timeList = LadderConditionsStore.timeList
  LadderConditionsStore.formData.shopId = props.datas.shopId
  LadderConditionsStore.formData.userId = store.state.value.user.id // props.datas.id;
  LadderConditionsStore.formData.username = store.state.value.user.name
  // 删除多余的公式说明 (不删除后端无法接收到完整数据)
  let feeList = timeList[0]['shopManagerList'][0]['feeList']
  for(let i = 0; i < feeList.length; i++){
    delete feeList[i]['calculateDesc']
    delete feeList[i]['calculateExpression']
  }
  // console.log("提交按钮", timeList);
  console.log(LadderConditionsStore, 'LadderConditionsStore')
  submitLoading.value = true
  addCharge(LadderConditionsStore.formData).then((response) => {
    ElMessage.success(LadderConditionsStore.formData.id != '' ?'修改成功':'新增成功')
    cancel() // 关闭窗口
    submitLoading.value = false
  }).catch(() => {
    submitLoading.value = false
  })
}

const addDateItem = (index) => {
  // console.log("addDateItem方法:", index);
  // 复制一个默认数据格式
  LadderConditionsStore.timeList.push(structuredClone(addDateItemDatas))
}

const removeDateItem = (index) => {
  // console.log("删除当前新增的数据组", index);
  LadderConditionsStore.timeList.splice(index, 1)
}

// 切换时间类型
const selectTimeType = (val, index) => {
  // console.log("切换时间类型:", val,index);
  if (val && val.id == 0) {
    LadderConditionsStore.timeList[index].dateRange = ['', '2099-12-31']
    // console.log("切换时间类型:", LadderConditionsStore.timeList);
  }
}

// 人员在店状态切换
const selectShopManager = (item, index, itemIndex) => {
  console.log(item, 'item')
  LadderConditionsStore.timeList[itemIndex].shopManagerList = []
  // console.log("人员在店状态切换selectShopManager:", item, index);
  // 有店长 有营销经理 有店长或有营销经理 会自动生成一个相反的数据状态
  if ([1, 4, 5].includes(item)) {
    // 如果选择有店长，添加第二个选择框并自动选中"无店长"  索引为1时 切换人员状态最多两条
    // console.log(LadderConditionsStore.timeList[itemIndex], itemIndex)
  
    // 默认选中"无店长"
    let state = 2
    // 有营销经理
    if (item === 4) {state = 6} 
    // 有店长或有营销经理
    if (item === 5) {state = 7}

    // console.log("设置新增数据的默认人员状态:", state);
    // const obj = structuredClone(initialTimeList[0]['shopManagerList'][0]);
    // obj.shopManagerStatus = state;
    // LadderConditionsStore.timeList[itemIndex].shopManagerList.push(obj)
    let haveObj = structuredClone(addShopManager)
    let noObj = structuredClone(addShopManager)
    haveObj.shopManagerStatus = item
    noObj.shopManagerStatus = state
    noObj.disabled = true
    LadderConditionsStore.timeList[itemIndex].shopManagerList.push(haveObj)
    LadderConditionsStore.timeList[itemIndex].shopManagerList.push(noObj)
    // console.log(LadderConditionsStore.timeList[itemIndex].shopManagerList)
  } else {
    let noObj = structuredClone(addShopManager)
    noObj.shopManagerStatus = item
    LadderConditionsStore.timeList[itemIndex].shopManagerList.push(noObj)
  }
  // 如果item为空则删除对应的数据
  if(!item && index != 0){
    LadderConditionsStore.timeList[itemIndex].shopManagerList.splice(index, 1)
  }

}
// 阶梯条件切换
const handleBet = (item, index, itemIndex) => {
  // console.log("阶梯条件切换:", item);
  let feeList = LadderConditionsStore.timeList[itemIndex].shopManagerList[index].feeList
  feeList.length = 0
  // // 费用初始化数据
  // const newFeeItem = structuredClone(initialTimeList[0].shopManagerList[0].feeList[0]);
  // feeList.push(newFeeItem);
  feeList.push(structuredClone(addBet))
}

</script>
<style lang="scss" scoped>
tr {
  height: 50px;
  line-height: 50px;
}
.itemBox {
  //   padding: 10px 0;
  display: flex;
  justify-content: flex-start;
  flex-direction: row;
  align-items: center;
  background-color: #f8f9fc;
}
.betBox {
  background-color: #f8f9fc;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.treeBox {
  .el-input-group__append {
    width: 10px !important;
  }
  .el-tree-node__content {
    height: 40px;
    font-size: 14px;
  }
}
.bg {
  background-color: #edf0f3;
}
.cardBg {
  margin-left: 100px;
  background: #f8f9fc;
  margin-bottom: 20px;
  max-width: 90%;
}
.line50 {
  height: 70px !important;
  line-height: 70px !important;
}
.dateBox {
  border: 1px dashed #bbc6ce;
  padding-top: 20px;
  margin-bottom: 20px;
}

input::placeholder {
  color: #e05943 !important; /* 设置为你想要的颜色 */
  opacity: 1 !important; /* 确保颜色不透明 */
}

/* 修改 textarea 的 placeholder 颜色 */
textarea::placeholder {
  color: #e05943 !important; /* 设置为你想要的颜色 */
  opacity: 1 !important; /* 确保颜色不透明 */
}

/* 修改 select 的样式 */
select {
  color: #e05943 !important; /* 设置选择框的文本颜色 */
}

/* 如果需要在 select 中显示 placeholder 样式，可以使用一个默认选项 */
select option {
  color: #e05943 !important; /* 设置默认选项的颜色 */
}

/* 选择框的默认选项 */
select option[disabled] {
  color: #e05943 !important; /* 设置为你想要的颜色 */
}
.input-with-select .el-input-group__prepend {
  background-color: #f5f7fa;
}
.calcBox {
  min-height: 40px;
  line-height: 40px;
  // margin-bottom: 20px;
}

</style>

