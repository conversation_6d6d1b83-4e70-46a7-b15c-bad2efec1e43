<!-- eslint-disable vue/require-default-prop -->
<template>
  <div>
    <el-radio-group v-model="queryForm.tabPosition">
      <el-radio-button :value="1">
        订单明细
      </el-radio-button>
      <el-radio-button :value="2">
        分店汇总
      </el-radio-button>
    </el-radio-group>
    <el-form
      :model="queryForm"
      class="mt-15"
      :inline="true"
      label-width="110px" 
      @submit.prevent
      @keydown.enter="emit('searchForm', queryForm)"
    >
      <el-form-item v-if="queryForm.tabPosition == 1" label="渠道单号" prop="channelNo">
        <el-input
          v-model="queryForm.channelNo"
          placeholder="请输入渠道单号"
          clearable
          class="w-240"
        />
      </el-form-item>
      <el-form-item v-if="queryForm.tabPosition == 1" label="预定单号" prop="bookNo">
        <el-input
          v-model="queryForm.bookNo"
          placeholder="请输入预定单号"
          clearable
          class="w-240"
        />
      </el-form-item>
      <el-form-item label="门店编号" prop="shopId">
        <el-input
          v-model="queryForm.shopId"
          placeholder="请输入门店编号/门店名称"
          clearable
          class="w-240"
        />
        <!-- :prefix-icon="Search" -->
      </el-form-item>
      <el-form-item v-if="queryForm.tabPosition == 1" label="入住单号" prop="fieldNo">
        <el-input
          v-model="queryForm.fieldNo"
          placeholder="请输入入住单号"
          clearable
          class="w-240"
        />
      </el-form-item>
    
      <div>
        <div class="flex flex-jus-start">
          <el-button type="primary" icon="Search" @click="emit('searchForm', queryForm)">
            查询
          </el-button>
          <el-button icon="Refresh" @click="resetForm()">
            重置
          </el-button>
          <el-button icon="Download" type="warning" plain @click="handleExport()">
            导出
          </el-button>

          <div v-if="queryForm.tabPosition === 1">
            <span
              style="
                margin: 10px 15px 0;
                width: 0;
                height: 15px;
                border-left: 1px solid #bbb;
              "
            ></span>
            <el-dropdown
              v-if="queryForm.billType == '1'"
              style="margin-right: 13px"
              @command="handleImportChange"
            >
              <el-button type="warning" plain>
                对账导入<el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="item in dzdOptionsList.exports"
                    :key="item.value"
                    :command="item.value"
                  >
                    {{ item.label }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-button
              v-if="queryForm.billType == '2'"
              type="primary"
              plain
              @click="handleSupplementaryOrder()"
            >
              补结订单
            </el-button>
            <el-button
              v-if="['3', '4'].includes(queryForm.billType)"
              type="primary"
              plain
              @click="handleImportChange(0)"
            >
              对账导入
            </el-button>
            <el-button type="primary" plain @click="handleRecalculate">
              账单重算
            </el-button>
            <el-button
              v-if="queryForm.billType == '3'"
              type="primary"
              plain
              @click="handleOfflineCrossStoreOrders()"
            >
              线下跨店单
            </el-button>
            <el-dropdown
              v-if="queryForm.billType == '3'"
              style="margin: 0 13px"
              @command="handleOrderManagement"
            >
              <el-button type="primary">
                订单处理<el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="item in dzdOptionsList.roders"
                    :key="item.value"
                    :command="item"
                  >
                    {{ item.label }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </el-form>

    <el-dialog
      v-model="dialogDatas.visible"
      :show-close="false"
      width="500"
      align-center
    >
      <template #header="{ titleId, titleClass }">
        <div class="my-header">
          <h4 :id="titleId" :class="titleClass" style="margin: 5px 0">
            {{ dialogDatas.title }}
            <el-text type="danger">
              {{ dialogDatas.info }}
            </el-text>
          </h4>
        </div>
      </template>
      <div
        v-for="item in dialogDatas.content"
        :key="item.index"
        style="padding: 3px 0"
      >
        {{ item }}
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel()">
            取消
          </el-button>
          <el-button type="primary" @click="fileUpload(2)">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <input
      ref="uploadRef"
      type="file"
      style="display: none"
      accept=".xlsx, .xls, .csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, text/csv"
      @change="handleImportSure"
    >

    <el-dialog
      v-model="supplementaryOrderDatas.visible"
      :title="supplementaryOrderDatas.title"
      width="500"
    >
      <el-form>
        <el-form-item>
          <el-input
            v-model="supplementaryOrderDatas.shopIds"
            autocomplete="off"
            :placeholder="supplementaryOrderDatas.info"
            type="textarea"
            :rows="15"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleSupplementaryOrderCancel()">
            取消
          </el-button>
          <el-button type="primary" @click="handleSupplementaryOrderSure()">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
// 对账单搜索条件
import { ElMessage } from 'element-plus'

import { compute, importBill, supplyOrder } from '@/api/channelBilling/index.js'
import { dzdOptionsList } from '@/utils/constants.js'
import { download2 } from '@/utils/request'
const props = defineProps({
  formInterface: Object, // 对账单类型
  queryFormDatas: Object // 对账单参数
})

const emit = defineEmits(['searchForm'])
const queryForm = props.queryFormDatas

// 重置搜索表单
const resetForm = () => {
  let arr = ['shopId', 'fieldNo', 'bookNo', 'channelNo']
  arr.forEach((item) => {
    queryForm[item] = ''
  })
  // 遍历清空对象
  // Object.assign(queryForm, props.formInterface) // 重置表单数据
}
// 导出
const handleExport = () => {

  if(queryForm.tabPosition === 2){
    ElMessage.info('暂未实现.')
    return false
  }

  // 此处需要通过类型判断现在地址
  queryForm.shopName = queryForm.shopId
  // console.warn('此处需要通过类型判断现在地址', queryForm)
  // 账单详情-订单明细列表导出
  download2(
    '/shop/crs/bill/exportDetail',
    queryForm,
    `订单明细列表_${new Date().getTime()}.xlsx`
  )
}

const dialogDatas = ref({
  visible: false,
  title: '导入说明',
  info: '审核确认后，次日会进入清分账户',
  content: [
    '1、建立空白账单:正常输入账单期间及对应渠道后保存',
    '2.1、将订单明细设置为sheet1位置，如有多个sheet页，请分别操作后多次导入',
    '2.2、订单明细中应包含关键列信息，其中订单号、结算金额为必有项',
    '订单号:[订单号][渠道单号0美团订单号]',
    '房费:[房费]结算房费]',
    '杂项:[杂项][结算杂项]',
    '退款金额:[退款金额[调整金额]',
    '佣金:[佣金][实收佣金][服务费][手续费][技术服务费]',
    '退服务费:[退服务费][退手续费][退技术服务费]',
    '返现金额:[返现金额][优惠金额]',
    '优惠券:[优惠券][优惠券码]',
    '结算金额:[实结金额][实退金额][结算金额]'
  ]
})

// 上传组件
const uploadRef = ref()

// 导入事件
const handleImportChange = async (val) => {
  console.log(val)
  // 渠道单号模糊导入会弹窗
  if (val === 0) {
    dialogDatas.value.visible = true
    return false
  }
  fileUpload(val)
}
let propId = ''
const fileUpload = (id) => {
  propId = id
  // console.log('fileUpload:', id)
  if (uploadRef.value) {
    uploadRef.value.click()
  }
}

// 账单重算
const handleRecalculate = () => {
  console.log('账单重算:', queryForm)
  compute(queryForm).then((res) => {
    if(res.code === 200){
      ElMessage.success(res.msg)
    }
  })
}
// 补结订单
const supplementaryOrderDatas = ref({
  visible: false,
  title: '',
  info: '',
  shopIds: '',
  data: '' // 存储中转数据
})
// 显示补结订单弹窗
const handleSupplementaryOrder = () => {
  console.log(queryForm)
  // 补结订单
  supplementaryOrderDatas.value.visible = true
  supplementaryOrderDatas.value.title = '请输入补结订单号，换行分隔'
  supplementaryOrderDatas.value.info = '请输入补结订单号，换行分隔'
  supplementaryOrderDatas.value.shopIds = ''
}

// 下载方法
const handleDownload = (args) => {
  ElMessage.info('暂未实现.')
  const params = {
    billNo: queryForm.billNo,
    memo: '',
    shopIdList: [],
    beginDate: '',
    endDate: ''
  }
  if(supplementaryOrderDatas.value.shopIds){
    params.shopIdList = supplementaryOrderDatas.value.shopIds.split('\n')
  }
  console.log('下载参数:', args, params)
  // download(params).then((res) => {
  //   // console.log(res)
  //   ElMessage.success(res.msg)
  // })
  handleSupplementaryOrderCancel()
}

// 补结订单确定
const handleSupplementaryOrderSure = () => {
  if(!supplementaryOrderDatas.value.shopIds){
    ElMessage.warning('请填写订单号')
    return 
  }
  if(supplementaryOrderDatas.value.data.type === 'download'){
    // console.log('调用下载方法:')
    handleDownload(supplementaryOrderDatas.value.data)
  }else{
    let params = {
      billNo: queryForm.billNo,
      bookNos: supplementaryOrderDatas.value.shopIds.replace(/\n/g, ',').split(',')
    }
    supplyOrder(params).then((res) => {
      ElMessage.success(res.msg)
      handleSupplementaryOrderCancel()
      if(res.code === 200) emit('searchForm', queryForm)
    })
  }
}
// 补结订单取消
const handleSupplementaryOrderCancel = () => {
  supplementaryOrderDatas.value.data = ''
  supplementaryOrderDatas.value.visible = false
}
// 订单处理
const handleOrderManagement = (args) => {
  // console.log(args)
  supplementaryOrderDatas.value.data = args
  supplementaryOrderDatas.value.shopIds = ''
  if (args.type === 'id') {
    supplementaryOrderDatas.value.title = '请输入门店ID，换行分隔门店ID'
    supplementaryOrderDatas.value.info = '请输入门店ID'
    supplementaryOrderDatas.value.visible = true
    return false
  }
  if (args.type === 'jd') {
    supplementaryOrderDatas.value.title = '请输入jd单号，换行分隔JD单号'
    supplementaryOrderDatas.value.info = '请输入jd单号'
    supplementaryOrderDatas.value.visible = true
    return false
  }
  if(args.type === 'download'){
    // console.log('调用下载方法:', args)
    handleDownload(args)
    return false
  }
  handleSupplementaryOrderSure(args)
}

// 导入弹窗取消按钮
const handleCancel = () => {
  dialogDatas.value.visible = false
}
// // 确定按钮
// const handleSure = () => {
//   const id = 2;
//   uploadRef.value.click();
//   // handleCancel();
// };
// 线下跨店订单
const handleOfflineCrossStoreOrders = () => {
  // 输出"线下跨店订单"
  console.log('线下跨店订单')
  supplementaryOrderDatas.value.title = '请输入门店ID，换行分隔门店ID'
  supplementaryOrderDatas.value.info = '请输入门店ID'
  supplementaryOrderDatas.value.shopIds = ''
  supplementaryOrderDatas.value.data = {
    label: '线下跨店订单',
    type: 'download',
    branch: 'cross'
  }
  supplementaryOrderDatas.value.visible = true
}
// 上传表格
const handleImportSure = (file) => {
  const params = {}
  params.file = file.target.files[0]
  params.scene = propId
  params.billNo = queryForm.billNo
  importBill(params)
    .then((res) => {
      ElMessage.success(res.msg)
    })
    .finally(() => {
      dialogDatas.value.visible = false
      uploadRef.value.value = '' // input
    })
}
watchEffect(() => {
  if(queryForm.tabPosition){
    resetForm()
  }
})
</script>
