<!-- eslint-disable vue/require-default-prop -->
<template>
  <div>
    <el-table
      ref="tableContainer"
      v-loading="props.tableLoading"
      :data="props.tableData"
      class="mt-15"
      style="width: 100%"
      :height="tableHeight"
    >
      <el-table-column prop="no" label="序号" min-width="50">
        <template #default="scope">
          <div style="text-wrap: nowrap">
            {{
              (paginationInfo.currentPage - 1) * paginationInfo.pageSize +
                scope.$index +
                1
            }}
          </div>
        </template>
      </el-table-column>
      <template v-for="item in tableHead" :key="item.index">
        <el-table-column
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
        >
          <template #default="scope">
            <div v-if="item.exp">
              {{ scope.row[item.prop] ? '是' : '否' }}
            </div>
            <div v-else>
              {{ scope.row[item.prop] }}
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <!-- 分页 右对齐 -->
    <el-pagination
      v-if="paginationInfo.totalCount > 0"
      class="flex flex-jus-end mt-15"
      background
      :current-page="paginationInfo.currentPage"
      :page-sizes="paginationInfo.pageSizes"
      :page-size="paginationInfo.pageSize"
      :layout="paginationInfo.layout"
      :total="paginationInfo.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
  </div>
</template>
<script setup>
// 对账单表格
const props = defineProps({
  queryFormDatas: Object,
  tableData: Array,
  tableLoading: Boolean,
  paginationInfo: Object
})
const emit = defineEmits(['searchForm'])
const queryForm = props.queryFormDatas
const paginationInfo = props.paginationInfo

const tableContainer = ref(null)
let { tableHeight, resetTableHeight } = useTableHeight(tableContainer)

const handleSizeChange = (val) => {
  paginationInfo.pageSize = val
  emit('searchForm', queryForm)
}
const handleCurrentChange = (val) => {
  paginationInfo.currentPage = val
  emit('searchForm', queryForm)
}
let tableHead = ref([])
// 监控queryForm内元素的变化
watchEffect(() => {
  if(queryForm.tabPosition){
    setTableHead()
    resetTableHeight()
  }
})

function setTableHead (){
  if(queryForm.tabPosition === 1){
    tableHead.value = [
      {label:'预订渠道', prop:'bookChannel', width:'150'},
      {label:'渠道单号', prop:'channelNo', width:'150'},
      {label:'预订单号', prop:'bookNo', width:'250'},
      {label:'预订时间', prop:'bookTime', width:'200'},
      {label:'订单状态', prop:'status', width:'120'},
      {label:'入住单号', prop:'fieldNo', width:'250'},
      {label:'入住时间', prop:'chkinTime', width:'200'},
      {label:'结算单号', prop:'payNo', width:'250'},
      {label:'离店时间', prop:'chkoutTime', width:'200'},
      {label:'门店ID', prop:'shopId', width:'120'},
      {label:'门店名称', prop:'shopName', width:'250'},
      {label:'大区', prop:'regionName', width:'200'},
      {label:'城区', prop:'eareName', width:'150'},
      {label:'入住房型', prop:'roomType', width:'150'},
      {label:'房号', prop:'roomNo', width:'100'},
      {label:'首日价', prop:'roomPrice', width:'150'},
      {label:'预订天数', prop:'bookDays', width:'100'},
      {label:'预订金额', prop:'p11', width:'200'},
      {label:'是否跨店', prop:'otherShop', width:'100', exp: true},
      {label:'售卡门店', prop:'cardShopId', width:'100'},
      {label:'会员卡号', prop:'cardNo', width:'150'},
      {label:'客人姓名', prop:'guestName', width:'150'},
      {label:'手机号', prop:'mobile', width:'150'},
      {label:'结算房费', prop:'roomFee', width:'150'},
      {label:'结算杂项', prop:'otherFee', width:'200'},
      {label:'订单金额', prop:'shiFee', width:'150'},
      {label:'入住间夜', prop:'stayDays', width:'150'},
      {label:'实际间夜', prop:'shiDays', width:'150'},
      {label:'应结金额', prop:'mayPay', width:'150'},
      {label:'佣金', prop:'yongJin', width:'150'},
      {label:'佣金差', prop:'yongJinCha', width:'150'},
      {label:'抵现/补贴', prop:'yhMoney', width:'150'},
      {label:'实结金额', prop:'shiPay', width:'150'},
      {label:'优惠券号', prop:'yhNo', width:'200'}
    ]
  }else{
    tableHead.value = [
      {label:'门店ID', prop:'shopId', width:'100'},
      {label:'门店名称', prop:'shopName', width:'300'},
      {label:'订单数', prop:'bookCount', width:'150'},
      {label:'结算房费', prop:'roomFee', width:'150'},
      {label:'结算杂项', prop:'otherFee', width:'200'},
      {label:'订单金额', prop:'shiFee', width:'150'},
      {label:'入住间夜', prop:'stayDays', width:'150'},
      {label:'实际间夜', prop:'shiDays', width:'150'},
      {label:'应结金额', prop:'mayPay', width:'150'},
      {label:'佣金', prop:'yongJin', width:'150'},
      {label:'佣金差', prop:'yongJinCha', width:'150'},
      {label:'抵现/补贴', prop:'yhMoney', width:'150'},
      {label:'实结金额', prop:'shiPay', width:'150'},
      {label:'生成时间', prop:'createTime', width:'200'},
      {label:'备注', prop:'memo', width:'150'}
    ]
  }
}
setTableHead()
</script>
