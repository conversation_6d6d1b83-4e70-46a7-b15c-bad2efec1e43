import request from '@/utils/request'

// 舞弊门店账单推送列表
export function getCheatSendList(query) {
  return request({
    url: '/shop/cheat/getCheatBillList',
    method: 'get',
    params: query
  })
}

// 舞弊账单推送费用明细汇总接口
export function getCheatBalance(query) {
  return request({
    url: '/shop/cheat/getCheatBalance',
    method: 'get',
    params: query
  })
}

// 舞弊账单推送费用明细表接口
export function getCheatChargeInfo(query) {
  return request({
    url: '/shop/cheat/getCheatChargeInfo',
    method: 'get',
    params: query
  })
}

// 舞弊账单详情
export function getBillDetail(shopId, query) {
  return request({
    url: '/shop/cheat/bill/' + shopId,
    method: 'get',
    params: query
  })
}

// 舞弊账单下载任务
export function addExportTask(params) {
  return request({
    url: `/shop/cheat/bill-pdf-task`,
    method: 'post',
    data: params
  })
}

// 舞弊账单文件生成记录分页
export function getFileList(query) {
  return request({
    url: '/shop/cheat/bill-pdf-task/list',
    method: 'get',
    params: query
  })
}

// 下载文件
export function downloadFile(fileId) {
  return request({
    url: '/shop/file/get/' + fileId,
    method: 'get',
    responseType: 'blob'
  })
}
