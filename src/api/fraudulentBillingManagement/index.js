import request from '@/utils/request'
import { paramsConcat } from '@/utils/script.js'
// 舞弊账单

// 舞弊门店列表查询接口
export function getCheatList(query) {
  let str = paramsConcat(query)
  return request({
    url: `/shop/cheat/getCheatList?${str}`,
    method: 'get'
  })
}
// 编辑舞弊闭环信息
export function editStatus(query) {
  let str = paramsConcat(query)
  return request({
    url: `/shop/cheat/editStatus?${str}`,
    method: 'get'
  })
}
// 舞弊规则详情查询接口
export function getCheatDetail(id) {
  return request({
    url: `/shop/cheat/getCheatDetail?id=${id}`,
    method: 'get'
  })
}
// 舞弊规则编辑记录查询接口
export function getCheatEditList(query) {
  let str = paramsConcat(query)
  return request({
    url: `/shop/cheat/getCheatEditList?${str}`,
    method: 'get'
  })
}
// 编辑舞弊规则保存接口
export function editDesc(params) {
  return request({
    url: `/shop/cheat/editDesc`,
    method: 'post',
    data: params
  })
}
// 图片上传接口
export function uploadImg(params) {
  return request({
    url: `/shop/file/uploadImg`,
    method: 'post',
    data: params,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 图片下载接口
export function downloadImg(params) {
  return request({
    url: `/shop/file/downloadImg?fileName=${params}`,
    method: 'get',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    responseType: 'blob',
    timeout: 70000
  })
}

// 生成舞弊账单
export function makeCheatBill(params) {
  return request({
    url: `/shop/cheat/makeCheatBill`,
    method: 'post',
    timeout: 60000,
    data:params,
    custom:{
      showLoading:true
    }
  })
}

// 舞弊门店导出
export function exportCheat(params) {
  return request({
    url: `/shop/cheat/export`,
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}
