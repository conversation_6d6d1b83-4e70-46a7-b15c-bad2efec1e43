import request from '@/utils/request'
import { paramsConcat } from '@/utils/script.js'
// 前期结余

// 列表
export function getPreBalanceList(params) {
  let str = paramsConcat(params)
  return request({
    url: `/shop/bill/getPreBalanceList?${str}`,
    method: 'get',
  })
}

// 明细 
export function getPreBalance(params) {
  let str = paramsConcat(params)
  return request({
    url: `/shop/bill/getPreBalance?${str}`,
    method: 'get'
  })
}
