import request from '@/utils/request'
import { paramsConcat } from '@/utils/script.js'
// 计费基数导入

// 列表
export function queryList(atime) {
  return request({
    url: `/shop/baseImport/queryList?atime=${atime}`,
    method: 'get',
  })
}
// 导入
export function upload(params) {
  // 发起HTTP请求
  return request({
    url: `/shop/baseImport/upload`,
    method: 'post',
    data: params,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 下载
export function downloadExport(params) {
  let str = paramsConcat(params)
  return request({
    url: `/shop/baseImport/export?${str}`,
    method: 'get'
  })
}
