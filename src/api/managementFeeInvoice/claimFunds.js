import request from '@/utils/request'
import { paramsConcat } from '@/utils/script.js'
// 款项认领

// 列表
export function getPageList(params) {
  let str = paramsConcat(params)
  return request({
    url: `/shop/claim/page?${str}`,
    method: 'get'
  })
}
// 认领操作
export function claimOrder(params) {
  return request({
    url: `/shop/claim/claimOrder?`,
    method: 'post',
    data: params
  })
}

// 反认领
export function unClaimOrder(params) {
  let str = paramsConcat(params)
  return request({
    url: `/shop/claim/unClaimOrder?${str}`,
    method: 'get'
  })
}
// 查询门店舞弊状态 0 未闭环，1已闭环
export function getPreBalance(params) {
  let str = paramsConcat(params)
  return request({
    url: `/shop/claim/getCheatStatus?${str}`,
    method: 'get'
  })
}
// 银行下拉列表查询
export function getBankInfo() {
  return request({
    url: `/shop/claim/getBankInfo`,
    method: 'get'
  })
}
