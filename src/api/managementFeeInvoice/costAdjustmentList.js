import request from '@/utils/request'

// oa费用调整查询列表
export function getFeeAdjustOaList(query) {
  return request({
    url: '/shop/feeAdjustOa/page',
    method: 'get',
    params: query
  })
}

// oa费用调整详情查询
export function getAdjustOaDetail(query) {
  return request({
    url: '/shop/feeAdjustOa/getAdjustOaDetail',
    method: 'get',
    params: query
  })
}

// oa 确认费用调整
export function confrimAdjust(query) {
  return request({
    url: '/shop/feeAdjustOa/confrimAdjust',
    method: 'get',
    params: query
  })
}