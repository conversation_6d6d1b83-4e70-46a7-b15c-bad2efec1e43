import request from '@/utils/request'
import { paramsConcat } from '@/utils/script.js'
// 门店账单列表

// 查询门店账单数据
export function getList(query) {
  return request({
    url: '/shop/bill/getList',
    method: 'post',
    data: query
  })
}
// 查询计费标准
export function getChargeDescList(query) {
  return request({
    url: '/bill/getChargeDescList',
    method: 'post',
    data: query
  })
}
// 查询费用基数
export function getChargeBasicList(query) {
  return request({
    url: '/bill/getChargeBasicList',
    method: 'post',
    data: query
  })
}
// 查询计费明细
export function getChargeInfoList(query) {
  return request({
    url: '/bill/getChargeInfoList',
    method: 'post',
    data: query
  })
}
// 修改审核反审核
export function updateAudit(query) {
  return request({
    url: '/shop/bill/updateAudit',
    method: 'post',
    data: query
  })
}
// 费用重新计算
export function updateCalculate(query) {
  return request({
    url: '/shop/bill/updateCalculate',
    method: 'post',
    data: query
  })
}

// 应收审核弹框信息查询接口 
export function auditConfirm(query) {
  return request({
    url: '/shop/bill/auditConfirm',
    method: 'post',
    data: query
  })
}

// 查询账单备注 
export function getBillMemo(query) {
  return request({
    url: '/bill/getBillMemo',
    method: 'post',
    data: query
  })
}
// 修改账单备注 
export function updateBillMemo(query) {
  return request({
    url: '/bill/updateBillMemo',
    method: 'post',
    data: query
  })
}
// 查询门店是否参与折扣
export function getDeductStatus(query) {
  return request({
    url: '/bill/getDeductStatus',
    method: 'post',
    data: query
  })
}
// 查询门店签约公司
export function getCorpList(query) {
  return request({
    url: '/bill/getCorpList',
    method: 'post',
    data: query
  })
}
// 费用调整-查询费用项
export function getAdjustChargeList(query) {
  return request({
    url: '/bill/getAdjustChargeList',
    method: 'post',
    data: query
  })
}
// 费用调整-费用调整更新
export function updateAdjustCharge(query) {
  return request({
    url: '/bill/updateAdjustCharge',
    method: 'post',
    data: query
  })
}
// 查询费用调整记录
export function getFeeAdjustRecord(query) {
  let str = paramsConcat(query)
  return request({
    url: `/shop/bill/getFeeAdjustRecord?${str}`,
    method: 'get',
  })
}
// 删除费用调整记录
export function deleteTz(query) {
  return request({
    url: `shop/bill/deleteTz?pid=${query.pid}`,
    method: 'post',
    data: query
  })
}



// 查询门店的memo备注
export function getShopMemo(query) {
  return request({
    url: '/bill/getShopMemo',
    method: 'get',
  })
}
// 修改门店memo 备注
export function updateShopMemo(query) {
  return request({
    url: '/bill/bill/updateShopMemo',
    method: 'post',
    data: query
  })
}

// 发送清分账单V2
export function deductBalance(query) {
  return request({
    url: '/shop/bill/deductBalance',
    method: 'post',
    data: query
  })
}
// 预发送清分账单
export function preDeductBalance(query) {
  return request({
    url: '/shop/bill/preDeductBalance',
    method: 'post',
    data: query
  })
}