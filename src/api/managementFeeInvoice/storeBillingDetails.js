import request from '@/utils/request'

// 查询费用基数
export function getChargeBasicList (query) {
  return request({
    url: '/shop/bill/getChargeBasicList',
    method: 'post',
    data: query
  })
}

// 查询计费明细
export function getChargeInfoList (query) {
  return request({
    url: '/shop/bill/getChargeInfoList',
    method: 'post',
    data: query
  })
}

// 查询备注
export function getBillMemo (query) {
  return request({
    url: '/shop/bill/getShopMemo',
    method: 'get',
    params: query
  })
}

// 修改备注
export function updateBillMemo (query) {
  return request({
    url: '/shop/bill/updateShopMemo',
    method: 'post',
    data: query
  })
}

// 查询门店签约公司
export function getCorpList (query) {
  return request({
    url: '/shop/bill/getCorpList',
    method: 'post',
    data: query
  })
}

// 费用调整-手动费用调整
export function updateAdjustCharge (query) {
  return request({
    url: '/shop/bill/updateAdjustCharge',
    method: 'post',
    data: query
  })
}
