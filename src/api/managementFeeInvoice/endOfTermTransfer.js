import request from '@/utils/request'
import {paramsConcat} from '@/utils/script.js'
// 期末结转

// 期末结转列表查询接口
export function operateList(params) {
  let str = paramsConcat(params)
  return request({
    url: `/shop/billRecord/operateList?${str}`,
    method: 'get'
  })
}
// 结转按钮接口 
export function carryBill() {
  return request({
    url: `/shop/billRecord/carryBill`,
    method: 'get'
  })
}
// 锁账按钮接口 /billRecord/blockBill
export function blockBill() {
  return request({
    url: `/shop/billRecord/blockBill`,
    method: 'get'
  })
}
// 锁账弹框详情接口 /billRecord/blockBillDetail
export function blockBillDetail() {
  return request({
    url: `/shop/billRecord/blockBillDetail`,
    method: 'get'
  })
}

// 查看当月是否锁账接口 /billRecord/hasBlockBill
export function hasBlockBill() {
  return request({
    url: `/shop/billRecord/hasBlockBill`,
    method: 'get'
  })
}