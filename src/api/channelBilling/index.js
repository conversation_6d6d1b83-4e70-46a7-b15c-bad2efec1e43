import request from '@/utils/request'
// 渠道账单
// 新增账单
export function addBill(data) {
  return request({
    url: '/shop/crs/bill/new',
    method: 'post',
    data: data
  })
}
// 查询所有渠道
export function getBillChannel() {
  return request({
    url: '/shop/crs/bill/channel',
    method: 'get'
  })
}

// 查询所有发票抬头
export function getBillInvoice() {
  return request({
    url: '/shop/crs/bill/invoice',
    method: 'get'
  })
}
// 查询渠道账单分页
export function getBillPage(query) {
  return request({
    url: '/shop/crs/bill/page',
    method: 'get',
    params: query
  })
}
// 删除渠道账单
export function deleteBill(query) {
  return request({
    url: '/shop/crs/del',
    method: 'get',
    params: query
  })
}

// 批量删除渠道账单
export function deleteBillBatch(data) {
  return request({
    url: '/shop/crs/bill/delBatch',
    method: 'delete',
    data: data
  })
}

// 对账单操作审核/反审核
export function auditBill(id, data) {
  return request({
    url: '/shop/crs/bill/audit/' + id,
    method: 'put',
    data: data
  })
}

// OTA对账单导入
export function importBill(params) {
  return request({
    url: '/shop/crs/bill/import/' + params.billNo,
    method: 'post',
    data: params,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 分页查询渠道账单中的分店汇总
export function shopList(query) {
  return request({
    url: `/shop/crs/bill/shop/${query.billNo}`,
    method: 'get',
    params: query
  })
}
// 账单详情-订单明细列表分页查询接口
export function shopDetailList(query) {
  return request({
    url: '/shop/crs/bill/detail',
    method: 'get',
    params: query
  })
}

// 对账单重新计算
export function compute(params) {
  return request({
    url: '/shop/crs/bill/compute/' + params.billNo,
    method: 'post',
    data: params
  })
}

// 订单过滤
export function crsBillMx(query) {
  return request({
    url: `/shop/crsBillMx/${query.branch}`,
    method: 'get',
    params: query
  })
}

// 线下跨店订单 下载全量订单 下载400订单
export function download(params) {
  return request({
    url: `/shop/crs/bill/download/${params.url}`,
    method: 'post',
    data: params
  })
}

// 补结订单
export function supplyOrder(params) {
  return request({
    url: '/shop/crs/bill/supplyOrder',
    method: 'post',
    data: params
  })
}

// 企业商旅开票查询
export function getBillInvoicePage(query) {
  return request({
    url: `/shop/crs/bill/invoice/page`,
    method: 'get',
    params: query
  })
}

// 企业商旅开票新增/修改
export function saveBillInvoice(params) {
  return request({
    url: '/shop/crs/bill/invoice/save',
    method: 'post',
    data: params
  })
}

// 分店规则查询
export function getBillBaseStore(query) {
  return request({
    url: `/shop/crs/bill/getBaseStore`,
    method: 'get',
    params: query
  })
}

// 修改分店规则
export function updateBillBaseStore(params) {
  return request({
    url: `/shop/crs/bill/updateBaseStore`,
    method: 'post',
    data: params
  })
}

// 渠道规则查询
export function getBillChannels(query) {
  return request({
    url: `/shop/crs/bill/getChannels`,
    method: 'get',
    params: query
  })
}

// 修改渠道规则
export function updateBillChannels(params) {
  return request({
    url: `/shop/crs/bill/updateChannel`,
    method: 'post',
    data: params
  })
}
