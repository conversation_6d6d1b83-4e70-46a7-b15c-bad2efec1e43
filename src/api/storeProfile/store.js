import request from '@/utils/request'
import { exportFn } from '@/utils/request'

// 查询门店档案列表
export function getShopInfoList (query) {
  return request({
    url: '/shop/basis/getShopInfoList',
    method: 'get',
    params: query
  })
}

// 查询门店档案详情
export function getShopInfoDetail (query) {
  return request({
    url: '/shop/basis/getShopInfoDetail',
    method: 'get',
    params: query
  })
}

// 更新门店账单邮箱
export function updateShopEmail (query) {
  return request({
    url: '/shop/basis/updateShopEmail',
    method: 'get',
    params: query
  })
}

// 查询门店账单计费规则
export function getBillDescList (query) {
  return request({
    url: '/shop/basis/getBillDescList',
    method: 'get',
    params: query
  })
}

// 查询门店停业复业记录
export function getShopStopList (query) {
  return request({
    url: '/shop/basis/getShopStopList',
    method: 'get',
    params: query
  })
}

// 门店档案列表导出
export function exportShopInfoList (query, fileName) {
  exportFn('/shop/basis/exportShopInfoList', query, fileName)
}

// 门店档案店长营销经理公司导入模版下载
export function downloadTempFile (query, fileName) {
  exportFn('/shop/file/download', query, fileName)
}

// 店长营销经理公司数据导入
export function importCompany (query) {
  return request({
    url: '/shop/bill/importCompany',
    method: 'post',
    transformRequest: [function (data, headers) {
      delete headers.post['Content-Type']
      return data
    }],
    data: query
  })
}

// 更新门店档案信息
export function updateShopBase (query) {
  return request({
    url: '/shop/basis/updateShopBase',
    method: 'get',
    params: query
  })
}
