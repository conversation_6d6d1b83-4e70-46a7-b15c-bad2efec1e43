import request from '@/utils/request'
// 门店收费管理接口

// 查询门店运营状态值列表
export function getShopStatusList() {
  return request({
    url: '/shop/getShopStatusList',
    method: 'post'
  })
}
// 获取地区总部
export function getShopHeadOfficeList() {
  return request({
    url: '/shop/getShopHeadOfficeList',
    method: 'post'
  })
}
// 查询门店大区列表
export function getShopDqList(query) {
  return request({
    url: '/shop/getShopDqList',
    method: 'post',
    data: query
  })
}
// 查询门店城区列表
export function getShopCqList(query) {
  return request({
    url: '/shop/getShopCqList',
    method: 'post',
    data: query
  })
}
// 查询门店列表
export function getBasisShopList(query) {
  return request({
    url: '/shop/basis/getShopList',
    method: 'post',
    data: query
  })
}
// 查询门店列表
export function getShopList(query) {
  return request({
    url: '/shop/getShopList',
    method: 'post',
    data: query
  })
}
// 门店收费管理导出
export function download(query) {
  return request({
    url: '/shop/exportExcel',
    method: 'post',
    data: query
  })
}

// 核验公式
export function verificationFormulaList(query) {
  return request({
    url: '/shop/charge/checkBill',
    method: 'post',
    data: query
  })
}
