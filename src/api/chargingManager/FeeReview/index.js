import request from '@/utils/request'
// 门店收费审核

// 查询-门店计费规则审核
export function queryList(data) {
  let str = ''
  for(let item in data){
    if(data[item]){
      str += item + '=' + data[item] + '&'
    }
  }
  str = str.substring(0, str.length - 1)
  // console.log(str)
  return request({
    url: `/shop/feeAudit/queryList?${str}`,
    method: 'get'
  })
}

// 查看审核明细
export function queryListByAuditId(id) {
  return request({
    url: `/shop/feeAudit/queryListByAuditId?auditId=${id}`,
    method: 'get'
  })
}
// 审核通过or退回
export function feeAudit(data) {
  return request({
    url: '/shop/feeAudit/updateAudit', // /shop/feeAudit
    method: 'post',
    data: data
  })
}