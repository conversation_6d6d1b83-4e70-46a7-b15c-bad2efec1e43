import request from '@/utils/request'

// 查询
export function queryList(query) {
  return request({
    url: '/shop/groupPolicy/getGroupPolicy',
    method: 'get',
    params: query
  })
}

// 保存接口
export function saveData(query) {
  return request({
    url: '/shop/groupPolicy/save',
    method: 'post',
    data: query
  })
}

// 获取品牌列表
export function getBrandList(query) {
  return request({
    url: '/shop/groupPolicy/getBrandList',
    method: 'get',
    params: query
  })
}
