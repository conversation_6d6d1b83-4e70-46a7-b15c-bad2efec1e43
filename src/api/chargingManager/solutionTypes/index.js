import request, { download2, exportFn } from '@/utils/request'

// 查询 -输送客源费方案
export function getListData (query) {
  return request({
    url: '/shop/customerSourceScheme/page',
    method: 'get',
    params: query
  })
}

// 导入-输送客源方案
export function importCustomerSourceScheme (data) {
  return request({
    url: '/shop/customerSourceScheme/import',
    method: 'post',
    timeout: 120000,
    transformRequest: [function (data, headers) {
      delete headers.post['Content-Type']
      return data
    }],
    data
  })
}

// 导出-输送客源方案
export function exportCustomerSourceScheme (query, fileName) {
  download2('/shop/customerSourceScheme/export', query, fileName)
}

// 编辑-输送客源费
export function editCustomerSourceScheme (query) {
  return request({
    url: '/shop/customerSourceScheme',
    method: 'put',
    data: query
  })
}

// 下载模板
export function downloadTempFile (query, fileName) {
  exportFn('/shop/file/download', query, fileName)
}
