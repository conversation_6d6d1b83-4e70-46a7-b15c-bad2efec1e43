import request from '@/utils/request'
// 门店计费规则设置接口
// 查询门店信息列表
export function getSearchShopList (data) {
  return request({
    url: '/shop/getSearchShopList',
    method: 'post',
    data: data,
    timeout: 20000
  })
}

// 新增收费项目信息
export function addCharge (data) {
  return request({
    url: '/shop/charge/add',
    method: 'post',
    data: data
  })
}
// 查询列表
export function getChargeList (query) {
  return request({
    url: '/shop/charge/getList',
    method: 'post',
    data: query
  })
}

// 删除
export function delCharge (query) {
  return request({
    url: '/shop/charge/delete',
    data: query,
    method: 'post'
  })
}
// 查询详细
export function getChargeInfo (id) {
  return request({
    url: '/shop/charge/getInfo/' + id,
    method: 'get'
  })
}

// 单行修改批次号
export function updateBatchNumber (data) {
  return request({
    url: '/shop/charge/updateBatchNumber',
    method: 'post',
    data: data
  })
}

// 获取操作日志
export function getLogList (data) {
  return request({
    url: '/shop/charge/getLogList',
    method: 'post',
    data: data
  })
}

// 查询门店合同台账
export function getBasisShopFee (data) {
  return request({
    url: '/shop/basis/getShopFee',
    method: 'post',
    data: data
  })
}
// 停止计费
export function stopBasisCharge (data) {
  return request({
    url: '/shop/basis/stopCharge',
    method: 'post',
    data: data
  })
}

// 查询是否有待审核计费规则
export function getCheckAudit (data) {
  return request({
    url: '/shop/basis/getCheckAudit',
    method: 'post',
    data: data
  })
}
// 门店计费规则提交审核
export function submitAudit (data) {
  return request({
    url: '/shop/basis/submitAudit',
    method: 'post',
    data: data
  })
}