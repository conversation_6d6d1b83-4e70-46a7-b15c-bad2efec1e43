<template>
  <div class="app-container">
    <el-form ref="queryRef" :model="queryParams" :inline="true" label-width="110px">
      <el-form-item label="账单期间" prop="atime">
        <el-date-picker
          v-model="queryParams.atime"
          type="months"
          :clearable="false"
          placeholder="请选择账单期间"
          style="width: 240px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="门店名称" prop="shopName">
        <el-input
          v-model="queryParams.shopName"
          placeholder="请输入门店编码/门店名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="地区总部" prop="headOffice">
        <el-select
          v-model="queryParams.headOffice"
          placeholder="请选择地区总部"
          clearable
          style="width: 240px"
          @change="setBusiness"
        >
          <el-option
            v-for="item in businessList"
            :key="item.freeString"
            :label="item.freeString"
            :value="item.freeString"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="大区" prop="dq">
        <el-select
          v-model="queryParams.dq"
          placeholder="请选择大区"
          clearable
          style="width: 240px"
          @change="setRegion"
        >
          <el-option
            v-for="item in regionList"
            :key="item.eareName"
            :label="item.eareName"
            :value="item.eareName"
          />
        </el-select>
      </el-form-item> 
      <el-form-item label="城区" prop="cq">
        <el-select
          v-model="queryParams.cq"
          placeholder="请选择城区"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="item in urbanList"
            :key="item.eareName"
            :label="item.eareName"
            :value="item.eareName"
          />
        </el-select>
      </el-form-item>
       
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">
          查询
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">
          重置
        </el-button>
        <el-button type="warning" icon="Download" @click="handleExport">
          导出
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :search="false" :show-refresh="false" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="budgetChangeList">
      <el-table-column type="index" label="序号" width="50" align="left" />
      <template v-for="item in columns">
        <el-table-column
          v-if="item.visible"
          :key="item.prop" :label="item.label" :align="item.align || 'left'" :prop="item.prop" :width="item.width"
        >
          <template v-if="item.prop === 'shopId'" #default="scope">
            <el-link type="primary" underline="always" @click="toDetail(scope.row)">
              {{ scope.row.shopId }}
            </el-link>
          </template>
          <template v-else-if="item.prop === 'atime'" #default="scope">
            {{ scope.row.atime?.slice(0, 7) }}
          </template>
          <template v-else-if="item.prop === 'adjustStatus'" #default="scope">
            {{ scope.row.adjustStatus == 0 ? '待确认' : '已调整' }}
          </template>
          <template v-else #default="scope">
            {{ scope.row[item.prop] }}
          </template>
        </el-table-column>          
      </template>
      <el-table-column label="操作" align="center" width="120">
        <template #default="scope">
          <el-button v-if="Number(scope.row.adjustStatus) === 0" link type="primary" @click="handleEdit(scope.row)">
            费用明细
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="CostAdjustmentList">
import { getFeeAdjustOaList } from '@/api/managementFeeInvoice/costAdjustmentList'
import { getFirstDayOfMonth, formatSimpleDate } from '@/utils'

// 列信息
const columns = ref([
  { key: 0, label: `门店ID`, visible: true, prop: 'shopId', width: '160' },
  { key: 1, label: `门店名称`, visible: true, prop: 'shopName', width: '160' },
  { key: 2, label: `地区总部`, visible: true, prop: 'headOffice', width: '160' },
  { key: 3, label: `大区`, visible: true, prop: 'dq', width: '160' },
  { key: 4, label: `城区`, visible: true, prop: 'cq', width: '160' },
  { key: 5, label: `城区总`, visible: true, prop: 'manager', width: '160' },
  { key: 6, label: `是否出具舞弊账单`, visible: true, prop: 'archiveTime', width: '160' },
  { key: 7, label: `前期余额`, visible: true, prop: 'endMoney', width: '160' },
  { key: 8, label: `本期应收`, visible: true, prop: 'thisMoney', width: '160' },
  { key: 9, label: `费用调整`, visible: true, prop: 'adjustMoney', width: '160' }
])

const { proxy } = getCurrentInstance()
const dateRange = ref([])
const budgetChangeList = ref([])
const loading = ref(false)
const total = ref(0)

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    shopName: undefined,
    atime: getFirstDayOfMonth(),
    adjustStatus: undefined,
    archiveTimeStart: undefined,
    archiveTimeEnd: undefined
  }
})

const { queryParams } = toRefs(data)

/** 查询用户列表 */
const getList = () => {
  loading.value = true
  const params = {
    ...queryParams.value,
    archiveTimeStart: dateRange.value?.[0] || undefined,
    archiveTimeEnd: dateRange.value?.[1] || undefined
  }
  params.atime = formatSimpleDate(params.atime, true)

  getFeeAdjustOaList(params).then((res) => {
    budgetChangeList.value = res.rows
    total.value = res.total
  }).finally(() => {
    loading.value = false
  })
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = []
  proxy.resetForm('queryRef')
  handleQuery()
}
/** 导出按钮操作 */
const handleExport = () => {
  proxy.$modal.confirm('确认导出此报表吗？').then(function () {
    const params = {
      ...queryParams.value,
      archiveTimeStart: dateRange.value?.[0] || undefined,
      archiveTimeEnd: dateRange.value?.[1] || undefined
    }
    params.atime = formatSimpleDate(params.atime, true)
    // exportBudgetDate(params).then((res) => {
    //   if (res.code === 200) {
    //     proxy.$modal.alertSuccess('文件导出中，请前往【导出管理】-【文件下载】列表下载文件。')
    //   } else {
    //     proxy.$modal.msgSuccess(res.msg)
    //   }
    // })
  }).catch(() => {})
}

const toDetail = (row) => {
  proxy.$router.push({
    path: '/managementFeeInvoice/storeBillingDetails',
    query: {
      id: row.id,
      name: row.shopName,
      atime: row.atime
    }
  })
}

getList()
</script>
<style scoped>
.el-form--inline .el-form-item {
    margin-right: 15px!important;
}
.el-form-item--default {
    margin-bottom: 10px!important;
}

</style>
