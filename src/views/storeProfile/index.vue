<template>
  <div class="app-container">
    <el-form ref="queryRef" :model="queryParams" :inline="true" label-width="110px">
      <el-form-item label="门店" prop="shopName">
        <el-input
          v-model="queryParams.shopName"
          placeholder="请输入门店编码/门店名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="营业状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择营业状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="item in statusList"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="地区总部" prop="headOffice">
        <el-select
          v-model="queryParams.headOffice"
          placeholder="请选择地区总部"
          clearable
          style="width: 240px"
          @change="setBusiness"
        >
          <el-option
            v-for="item in businessList"
            :key="item.freeString"
            :label="item.freeString"
            :value="item.freeString"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="大区" prop="dq">
        <el-select
          v-model="queryParams.dq"
          placeholder="请选择大区"
          clearable
          style="width: 240px"
          @change="setRegion"
        >
          <el-option
            v-for="item in regionList"
            :key="item.eareName"
            :label="item.eareName"
            :value="item.eareName"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="城区" prop="cq">
        <el-select v-model="queryParams.cq" placeholder="请选择城区" clearable style="width: 240px">
          <el-option
            v-for="item in urbanList"
            :key="item.eareName"
            :label="item.eareName"
            :value="item.eareName"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否封存" prop="sealUpStatus">
        <el-select
          v-model="queryParams.sealUpStatus"
          placeholder="请选择是否封存"
          clearable
          style="width: 240px"
        >
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
        <el-button type="info" icon="Upload" plain @click="handleUpload"> 导入 </el-button>
        <el-button type="warning" plain icon="Download" @click="handleExport"> 导出 </el-button>
      </el-form-item>
    </el-form>
    <el-table
      ref="tableContainer"
      v-loading="loading"
      :data="shopInfoList"
      class="mt-15"
      :height="tableHeight"
    >
      <el-table-column type="index" label="序号" width="50" align="left" />
      <template v-for="item in columns">
        <el-table-column
          v-if="item.prop === 'shopId'"
          :key="`shopId-${item.prop}`"
          :label="item.label"
          :align="item.align || 'left'"
          :prop="item.prop"
          :width="item.width"
        >
          <template #default="scope">
            <el-button link type="primary" @click="handleDetail(scope.row)">
              {{ scope.row[item.prop] }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.prop === 'sealUpStatus'"
          :key="`sealUpStatus-${item.prop}`"
          :label="item.label"
          :align="item.align || 'left'"
          :prop="item.prop"
          :width="item.width"
        >
          <template #default="scope">
            {{ scope.row[item.prop] == 1 ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column
          v-else
          :key="`default-${item.prop}`"
          :label="item.label"
          :align="item.align || 'left'"
          :prop="item.prop"
          :width="item.width"
        />
      </template>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <CommonUpload
      v-model:open="open"
      v-model:upload-loading="uploadLoading"
      @download-temp="downloadTemp"
      @upload="uploadFile"
    ></CommonUpload>
  </div>
</template>

<script setup name="CostAdjustmentList">
import {
  getShopCqList, // 查询门店城区列表
  // 查询门店地区总部列表
  getShopDqList, // 门店营业状态
  getShopHeadOfficeList,
  getShopStatusList
} from '@/api/chargingManager/search'
import {
  downloadTempFile, // 门店信息列表
  exportShopInfoList,
  getShopInfoList,
  importCompany
} from '@/api/storeProfile/store'
import CommonUpload from '@/components/CommonUpload'
import { ElMessage } from 'element-plus'

const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)
// 列信息
const columns = ref([
  { key: 0, label: `门店ID`, visible: true, prop: 'shopId', width: '160' },
  { key: 1, label: `门店名称`, visible: true, prop: 'shopName', width: '160' },
  { key: 2, label: `门店状态`, visible: true, prop: 'status', width: '160' },
  { key: 3, label: `酒店品牌`, visible: true, prop: 'brandName', width: '160' },
  { key: 4, label: `签约日期`, visible: true, prop: 'signDate', width: '160' },
  { key: 5, label: `开业日期`, visible: true, prop: 'startDate', width: '160' },
  { key: 6, label: `解约日期`, visible: true, prop: 'overDate', width: '160' },
  { key: 7, label: `是否封存`, visible: true, prop: 'sealUpStatus', width: '160' },
  { key: 8, label: `地区总部`, visible: true, prop: 'headOffice', width: '160' },
  { key: 9, label: `大区`, visible: true, prop: 'dq', width: '160' },
  { key: 10, label: `城区`, visible: true, prop: 'cq', width: '160' },
  { key: 11, label: `城区总`, visible: true, prop: 'areaUserName', width: '160' }
])

const { proxy } = getCurrentInstance()
const statusList = ref([]) // 门店营业状态
const businessList = ref([]) // 地区总部
const regionList = ref([]) // 大区
const urbanList = ref([]) // 城区

const getStatusList = () => {
  getShopStatusList().then((res) => {
    if (res.code === 200) {
      statusList.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}
// 地区总部
function getBusinessList() {
  getShopHeadOfficeList().then((res) => {
    if (res.code === 200) {
      businessList.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}

// 大区
const getRegionList = (params) => {
  getShopDqList(params).then((res) => {
    if (res.code === 200) {
      regionList.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}

// 城区
const getUrbanList = (params) => {
  getShopCqList(params).then((res) => {
    if (res.code === 200) {
      urbanList.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}

// 设置地区总部
function setBusiness(val) {
  if (val) {
    getRegionList({ freeString: val })
  }
  regionList.value = []
  queryParams.value.dq = undefined
  urbanList.value = []
  queryParams.value.cq = undefined
}

// 设置大区
function setRegion(val) {
  if (val) {
    const eareId = regionList.value.find((item) => item.eareName === val).eareId
    getUrbanList({ eareId })
  } else {
    urbanList.value = []
    queryParams.value.cq = undefined
  }
}

const dateRange = ref([])
const shopInfoList = ref([])
const loading = ref(false)
const total = ref(0)

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    headOffice: undefined,
    dq: undefined,
    cq: undefined,
    shopName: '',
    status: undefined,
    sealUpStatus: undefined
  }
})

const { queryParams } = toRefs(data)

/** 查询用户列表 */
const getList = () => {
  loading.value = true
  const params = {
    ...queryParams.value
  }

  getShopInfoList(params)
    .then((res) => {
      shopInfoList.value = res.rows
      total.value = res.total
    })
    .finally(() => {
      loading.value = false
    })
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = []
  proxy.resetForm('queryRef')
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy.$modal
    .confirm('确认导出此报表吗？')
    .then(function () {
      const params = {
        ...queryParams.value
      }

      exportShopInfoList(params, '门店档案.xls')
    })
    .catch(() => {})
}

const handleDetail = (row) => {
  proxy.$router.push({
    path: '/storeProfile/detail',
    query: {
      shopId: row.shopId
    }
  })
}

// 导入相关
const open = ref(false)
const uploadLoading = ref(false)
const handleUpload = () => {
  open.value = true
}

const downloadTemp = () => {
  downloadTempFile(
    {
      saveFileName: '店长及营销经理公司导入模版.xlsx',
      fileName: '店长及营销经理公司导入模版.xlsx'
    },
    '门店档案店长营销经理公司导入模版下载.xlsx'
  )
}

const uploadFile = (fileList) => {
  uploadLoading.value = true
  let formData = new FormData()
  fileList.forEach((item) => {
    formData.append('file', item.raw)
  })
  importCompany(formData)
    .then(() => {
      proxy.$modal.msgSuccess('上传成功')
      open.value = false
      resetQuery()
    })
    .finally(() => {
      uploadLoading.value = false
    })
}

getStatusList()
getBusinessList()
getList()
</script>

<style lang="scss" scoped>
.app-container {
  overflow: auto;

  .click-item {
    color: #409eff;
    text-decoration: underline;
  }
}
</style>
