<template>
  <div class="app-container">
    <div class="content-item">
      <div class="item-title">
        <span class="title-text">基本信息</span>
        <div style="flex: 1; display: flex; justify-content: flex-end;">
          <el-button type="primary" @click="handleEdit">
            {{ isEdit ? '保存' : '修改' }}
          </el-button>
        </div>
      </div>

      <div class="item-content">
        <div v-for="item in baseInfo" :key="item.key" class="item-line">
          <span class="line-label">{{ item.label }}：</span>
          <span v-if="!item.isEdit || !isEdit" class="line-value">{{ item.key === 'thirdPartyTransfer' ? detailData[item.key] === 1 ? '是' : '否' : item.key === 'schemeType' ? scheme_type[detailData[item.key]]?.label : detailData[item.key] }}</span>
          <el-select
            v-else
            v-model="detailData[item.key]"
            placeholder="请选择"
            clearable
            style="width: 235px"
          >
            <el-option
              v-for="dict in company_type_list"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select> 
        </div>
      </div>
    </div>

    <div class="content-item">
      <div class="item-title">
        <span class="title-text">开票信息</span>
      </div>

      <div class="item-content">
        <div v-for="item in invoiceInfo" :key="item.key" class="item-line">
          <span class="line-label">{{ item.label }}：</span>
          <span v-if="!item.isEdit || !isEdit" class="line-value">{{ item.key === 'invoiceType' ? getInvoiceType(detailData[item.key]) : detailData[item.key] }}</span>
          <el-input
            v-else
            v-model="detailData[item.key]"
            placeholder="请输入"
            clearable
            style="width: 235px"
          />
        </div>
      </div>
    </div>

    <div class="content-item">
      <div class="item-title">
        <span class="title-text">计费规则</span>
        <div class="title-select">
          <span class="select-label">账单周期</span>
          <el-date-picker
            v-model="atime"
            type="month"
            placeholder="请选择账单周期"
            @change="getBillDesc"
          />
        </div>
      </div>

      <div class="item-content">
        <el-table :data="billDescList">
          <template v-for="item in billDescColumns">
            <el-table-column
              v-if="item.visible"
              :key="item.prop" :label="item.label" :align="item.align || 'left'" :prop="item.prop" :width="item.width"
            />
          </template>
        </el-table>
      </div>
    </div>

    <div class="content-item">
      <div class="item-title">
        <span class="title-text">停业-恢复营业记录</span>
      </div>

      <div class="item-content">
        <el-table :data="shopStopList">
          <template v-for="item in shopStopColumns">
            <el-table-column
              v-if="item.visible"
              :key="item.prop" :label="item.label" :align="item.align || 'left'" :prop="item.prop" :width="item.width"
            />
          </template>
        </el-table>
      </div>
    </div>

    <!-- <el-dialog
      v-model="emailShow"
      title="修改发票邮箱"
      width="500"
    >
      <el-form ref="queryRef" :model="queryParams" :rules="rules" :inline="true" label-width="110px">
        <el-form-item label="邮箱地址" prop="email">
          <el-input
            v-model="queryParams.email"
            placeholder="请输入发票邮箱"
            clearable
            style="width: 240px"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">
            取消
          </el-button>
          <el-button type="primary" @click="handleConfirm">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog> -->
  </div>
</template>

<script setup>
import {
  getShopInfoDetail, // 门店信息详情
  getShopStopList, // 门店停用列表
  getBillDescList, // 账单描述列表
  updateShopBase // 更新邮箱
} from '@/api/storeProfile/store'
import { formatSimpleDate } from '@/utils/index'
import { ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance()
const { scheme_type } = proxy.useDict('scheme_type')
const { company_type_list } = proxy.useDict('company_type_list')
const route = useRoute()

const baseInfo = ref([
  { label: '门店ID', key: 'shopId'},
  { label: '门店名称', key: 'shopName'},
  { label: '城区', key: 'cq'},
  { label: '大区', key: 'dq'},
  { label: '地区总部', key: 'headOffice'},
  { label: '城区总', key: 'areaUserName'},
  { label: '门店状态', key: 'status'},
  { label: '业主名称', key: 'ownerName'},
  { label: '是否三方转移', key: 'thirdPartyTransfer'},
  { label: '企业性质', key: 'enterpriseType'},
  { label: '合规打款人', key: 'compliantPayer'},
  { label: '授权打款人', key: 'authorPayer'},
  { label: '输送客源费方案类型', key: 'schemeType'},
  { label: '我方签约公司', key: 'qyzt'},
  { label: '店长公司', key: 'dzgs', isEdit: true},
  { label: '营销经理公司', key: 'yxjlgs', isEdit: true}
])

const invoiceInfo = ref([
  { label: '发票类型', key: 'invoiceType'},
  { label: '户名', key: 'accountName'},
  { label: '纳税识别号', key: 'taxId'},
  { label: '开户行', key: 'bankName'},
  { label: '发票邮箱', key: 'email', isEdit: true},
  { label: '邮箱签名', key: 'emailSign', isEdit: true}
])

// const rules = ref({
//   email: [
//     { required: true, message: '请输入邮箱地址', trigger: 'blur' },
//     { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
//   ]
// })
// const queryParams = ref({
//   email: ''
// })
// const emailShow = ref(false)

const atime = ref(formatSimpleDate(new Date().setDate(1), true)) // 账单期间
const billDescList = ref([]) // 计费规则列表
// 列信息
const billDescColumns = ref([
  { key: 0, label: `账单期间`, visible: true, prop: 'atime', width: '160' },
  { key: 1, label: `商标使用费`, visible: true, prop: 'f02', width: '160' },
  { key: 2, label: `会员管理费`, visible: true, prop: 'f04', width: '160' },
  { key: 3, label: `输送客源费`, visible: true, prop: 'f06', width: '160' },
  { key: 4, label: `AI经营管家服务费`, visible: true, prop: 'f08', width: '160' },
  { key: 5, label: `运营品质检查费`, visible: true, prop: 'f07', width: '160' },
  { key: 6, label: `品质督导费`, visible: true, prop: 'f20', width: '160' },
  { key: 7, label: `市场营销费`, visible: true, prop: 'f18', width: '160' },
  { key: 8, label: `财务咨询费`, visible: true, prop: 'f27', width: '160' },
  { key: 9, label: `店长工资`, visible: true, prop: 'f19', width: '160' },
  { key: 10, label: `营销经理工资`, visible: true, prop: 'f39', width: '160' },
  { key: 11, label: `输送客源费减免`, visible: true, prop: 'f29', width: '160' },
  { key: 12, label: `会员管理费减免`, visible: true, prop: 'f30', width: '160' }
])

const getInvoiceType = (val) => {
  switch (val) {
    case '0':
      return '专票'
    case '1':
      return '普票'
    case '2':
      return '电子普票'
    default:
      return ''
  }
}

const getBillDesc = (val) => {
  if (val) atime.value = formatSimpleDate(val, true)
  getBillDescList({ shopId: route.query.shopId, atime: atime.value }).then((res) => {
    if(res.code === 200){
      billDescList.value = res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
}

const shopStopList = ref([]) // 停业-恢复营业记录
const shopStopColumns = ref([
  { key: 0, label: `停业日期`, visible: true, prop: 'startDate' },
  { key: 1, label: `恢复营业日期`, visible: true, prop: 'endDate' },
  { key: 2, label: `流程链接`, visible: true, prop: 'processCode' }
])
const getShopStop = () => {
  getShopStopList({ shopId: route.query.shopId }).then((res) => {
    if(res.code === 200){
      shopStopList.value = res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
}

const detailData = ref({})
const getDetail = () => {
  getShopInfoDetail({ shopId: route.query.shopId }).then((res) => {
    if(res.code === 200){
      detailData.value = res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
}

const isEdit = ref(false)
const handleEdit = () => {
  console.log('isEdit', isEdit.value)
  if (isEdit.value) {
    const params = {
      shopId: detailData.value.shopId,
      email: detailData.value.email,
      dzgs: detailData.value.dzgs,
      yxjlgs: detailData.value.yxjlgs,
      emailSign: detailData.value.emailSign || ''
    }
    updateShopBase(params).then((res) => {
      if(res.code === 200){
        ElMessage.success('修改成功')
        getDetail()
      }else{
        ElMessage.error(res.msg)
      }
    })
  }
  isEdit.value = !isEdit.value
}

// const changeEmail = () => {
//   emailShow.value = true
//   queryParams.value.email = detailData.value.email
// }

// const handleCancel = () => {
//   emailShow.value = false
//   proxy.$refs.queryRef.resetFields()
// }

// const handleConfirm = () => {
//   proxy.$refs.queryRef.validate((valid) => {
//     if (valid) {
//       emailShow.value = false
//       updateShopEmail({ shopId: route.query.shopId, email: queryParams.value.email }).then((res) => {
//         if(res.code === 200){
//           ElMessage.success('修改成功')
//           getDetail()
//         }else{
//           ElMessage.error(res.msg)
//         }
//       })
//     }
//   })
// }

getDetail()
getBillDesc()
getShopStop()
</script>

<style lang="scss" scoped>
.content-item{
  .item-title {
    height: 42px;
    display: flex;
    align-items: center;

    .title-text {
      padding-right: 12px;
      font-size: 16px;
      font-weight: 600;
    }

    .title-select {
      .select-label {
        font-size: 14px;
        color: #666;
        padding-right: 8px;
      }
    }
  }

  .item-content {
    width: 100%;
    padding: 18px 20px 6px 20px;
    margin-bottom: 10px;
    border-radius: 4px;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    display: flex;
    flex-wrap: wrap;

    .item-line {
      width: 400px;
      margin: 0 0 12px 0;
      font-size: 15px;

      .line-label {
        display: inline-block;
        width: 165px;
        position: relative;
        text-align: right;
        color: #444;
      }

      .line-value {
        display: inline-block;
        padding-right: 12px;
        width: 235px;
        color: #666;
      }
    }
  }
}
</style>