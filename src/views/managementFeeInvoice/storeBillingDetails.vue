<template>
  <div class="app-container store-billing-details">
    <div class="detail-title">
      <span class="shop-name">{{ params.shopId }}-{{ params.shopName }}</span>
      <span class="detail-status">{{ deductStatus === '1' ? '参与抵扣' : '未参与抵扣' }}</span>
      <el-date-picker
        v-model="params.atime"
        type="month"
        placeholder="请选择账单期间"
        :clearable="false"
        style="width: 240px"
        @change="getDetails"
      />
    </div>

    <el-button-group class="btn-group">
      <el-button :type="!btnType ? 'primary' : 'default'" @click="handleBtnClick('')">
        全部
      </el-button>
      <el-button v-for="item in corpList" :key="item.shortName" :type="btnType === item.shortName ? 'primary' : 'default'" @click="handleBtnClick(item.shortName)">
        {{ item.accountInfo }}
      </el-button>
    </el-button-group>

    <div class="detail-content">
      <div class="detail-content-left">
        <p class="content-title">
          计费基数
        </p>

        <el-table :data="chargeBasicList" style="width: 340px">
          <el-table-column type="index" label="序号" width="60" align="left" />
          <el-table-column key="feeTitle" label="费用项" align="left" prop="feeTitle" width="180" />
          <el-table-column key="feeMoney" label="金额" align="left" prop="feeMoney" width="120" />
        </el-table>
      </div>

      <div class="detail-content-right">
        <div class="content-title">
          <div class="title-left">
            <span class="title-text">计费明细</span>
            <span class="title-info">欠款为正数，结余为负数</span>
          </div>
          <div v-if="showBtn" class="title-right">
            <el-button type="primary" plain style="margin-right: 10px;" @click="toFeeManager">
              修改标准
            </el-button>
            <el-dropdown style="margin-right: 10px;">
              <el-button type="primary" plain>
                费用调整<el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="toCostAdjustmentRecord">
                    调整记录
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleAdjust">
                    费用减免
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <!-- <el-button type="primary" plain @click="handleRefund">
              退款调整
            </el-button> -->
          </div>
        </div>

        <el-table :data="chargeInfoList" :span-method="arraySpanMethod">
          <el-table-column type="index" label="序号" width="60" align="left" />
          <el-table-column key="feeTitle" label="费用项" align="left" prop="feeTitle" width="160" />
          <el-table-column key="proMoney" label="前期余额" align="left" prop="proMoney" width="100" />
          <el-table-column key="thisMoney" label="本期应收" align="center" prop="thisMoney" width="400">
            <el-table-column key="csyyt" label="冲上月预提" align="center" prop="csyyt" width="100" />
            <el-table-column key="jsysj" label="记上月实际" align="center" prop="jsysj" width="100" />
            <el-table-column key="dyyt" label="当月预提" align="center" prop="dyyt" width="100" />
            <el-table-column key="jdTz" label="季度调整" align="center" prop="jdTz" width="100" />
          </el-table-column>
          <el-table-column key="clearMoney" label="本期清分" align="left" prop="clearMoney" width="100" />
          <el-table-column key="backMoney" label="本期回款" align="left" prop="backMoney" width="100" />
          <el-table-column key="adjustMoney" label="费用调整" align="left" prop="adjustMoney" width="100" />
          <el-table-column key="endBalance" label="期末余额" align="left" prop="endBalance" width="100" />
          <el-table-column key="calculateDesc" label="计费标准" align="left" prop="calculateDesc" width="100" />
        </el-table>
      </div>
    </div>

    <div class="detail-info">
      <div class="detail-info-right">
        <div class="info-title">
          <span class="title-text">备注</span>
          <el-button type="primary" plain @click="sumbitBillMemo">
            提交
          </el-button>
        </div>

        <el-input
          v-model="billMemo"
          :rows="10"
          type="textarea"
          placeholder="请输入备注"
        />
      </div>
    </div>

    <BillFeeReduction v-model:open="reductionShow" :params="params" :data="reductionData"></BillFeeReduction>
    <RefundAdjustmentDialog v-model:open="refundShow" :data="reductionData" :params="params"></RefundAdjustmentDialog>
  </div>
</template>

<script setup name="StoreBillingDetails">
import { ref } from 'vue'
import BillFeeReduction from './components/billFeeReduction.vue'
import RefundAdjustmentDialog from './components/refundAdjustmentDialog.vue'
import { getChargeBasicList, getChargeInfoList, getBillMemo, updateBillMemo, getCorpList } from '@/api/managementFeeInvoice/storeBillingDetails'
import { formatSimpleDate } from '@/utils/index'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute() 

const btnType = ref('')
const params = ref({
  shopId: route.query.shopId,
  shopName: route.query.shopName,
  atime: route.query.atime ? new Date(route.query.atime) : formatSimpleDate(new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1), true)
})
const showBtn = computed(() => {
  if (formatSimpleDate(new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1), true) > formatSimpleDate(params.value.atime, true)) {
    return false
  } else {
    return true
  }
})
const chargeBasicList = ref([]) // 计费基数
const chargeInfoList = ref([]) // 计费明细
const reductionData = ref([]) // 减免信息
const billMemo = ref('') // 备注
const deductStatus = ref(route.query.deductStatus) // 是否参与折扣
const corpList = ref([]) // 签约公司列表

const getDetails = () => {
  const data = {...params.value}
  data.atime = formatSimpleDate(data.atime, true)

  getChargeBasicList({shopId: data.shopId, atime:data.atime}).then((res) => {
    chargeBasicList.value = res.data
  })

  getChargeInfoList({ ...data, corp: btnType.value }).then((res) => {
    chargeInfoList.value = res.data
    reductionData.value = res.data
  }).catch(() => {
    chargeInfoList.value = []
    reductionData.value = []
  })

  getBillMemo({shopId: data.shopId}).then((res) => {
    billMemo.value = res.msg
  })

  getCorpList({shopId: data.shopId}).then((res) => {
    corpList.value = res.data
  })
}

const toFeeManager = () => {
  router.push({ 
    path: '/chargingManager/feeManagerAdd', 
    query: { 
      ...route.query
    } 
  })
}

const handleBtnClick = (type) => {
  btnType.value = type
  const data = {...params.value}
  data.atime = formatSimpleDate(data.atime, true)
  getChargeInfoList({ ...data, corp: btnType.value }).then((res) => {
    chargeInfoList.value = res.data
  })
}

const sumbitBillMemo = () => {
  updateBillMemo({shopId: params.value.shopId, memo: billMemo.value}).then(() => {
    ElMessage.success('保存成功')
  })
}

// 合并单元格
const arraySpanMethod = ({ row, columnIndex}) => {
  if (!['营销经理工资', '店长工资'].includes(row.feeTitle)) { 
    row.csyyt = row.thisMoney
    if (columnIndex === 3) {
      return [1, 4]
    } else if (columnIndex > 3 && columnIndex <= 6) {
      return [0, 0]
    }
  }
}

const reductionShow = ref(false) // 费用减免
const handleAdjust = () => {
  reductionShow.value = true
}
const toCostAdjustmentRecord = () => {
  router.push({
    path: '/managementFeeInvoice/costAdjustmentRecord',
    query: {
      id: params.value.shopId
    }
  })
}

const refundShow = ref(false)
const handleRefund = () => { // 退款调整
  refundShow.value = true
}

getDetails()
</script>
<style scoped lang="scss">
.store-billing-details {
  .detail-title {
    display: flex;
    align-items: center;
    padding-bottom: 30px;

    .shop-name {
      padding-right: 20px;
      font-size: 18px;
      font-weight: 600;
    }

    .detail-status {
      padding-right: 20px;
      font-size: 16px;
      color: #f56c6c;
    }
  }

  .detail-content {
    display: flex;
    border-radius: 12px;
    border: 1px solid #eee;
    padding: 12px;
    margin: 12px 0;
    overflow: auto;
    max-width: fit-content;

    .detail-content-left {
      padding-right: 12px;
      border-right: 1px dashed #eee;

      .content-title {
        display: inline-block;
        margin: 0px;
        line-height: 42px;
        padding-bottom: 40px;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .detail-content-right {
      padding-left: 12px;

      .content-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 42px;

        .title-left {
          .title-text {
            display: inline-block;
            font-size: 16px;
            font-weight: 600;
          }

          .title-info {
            display: inline-block;
            padding: 0 20px;
            color: #f56c6c;
          }
        }
      }
    }
  }

  .detail-info {
    display: flex;
    border-radius: 12px;
    border: 1px solid #eee;
    padding: 12px;
    margin: 12px 0;
    overflow: auto;
    max-width: 1690px;

    .detail-info-left {
      padding-right: 12px;
      border-right: 1px dashed #eee;

      .info-title {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title-text {
          display: inline-block;
          font-size: 16px;
          font-weight: 600;
        }
      }

      .info-content {
        border: 0.5px solid #f5f7fa;

        .content-item {
          font-size: 13px;
          display: flex;
          min-height: 42px;
          border: 0.5px solid #f5f7fa;
          align-items: center;
          background: #C6E9FB;

          .info-content-left {
            width: 160px;
            height: 100%;
            min-height: 42px;
            padding: 0 8px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: #333;
          }

          .info-content-right {
            width: 300px;
            height: 100%;
            min-height: 42px;
            padding: 8px;
            display: inline-flex;
            align-items: center;
            text-align: center;
            color: #333;
            background: #fff;
          }
        }
      }
    }

    .detail-info-right {
      flex: 1;
      padding-left: 12px;

      .info-title {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title-text {
          display: inline-block;
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
