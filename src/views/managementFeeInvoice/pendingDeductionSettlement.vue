<template>
  <div class="app-container">
    <el-form ref="queryRef" :model="queryParams" :inline="true" label-width="110px">
      <el-form-item label="账单期间" prop="aTime">
        <el-date-picker
          v-model="queryParams.aTime"
          type="month"
          :clearable="false"
          placeholder="请选择账单期间"
          style="width: 240px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="范围" prop="channel">
        <el-select
          v-model="queryParams.channel"
          placeholder="请选择渠道名称"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="channel in channelList"
            :key="channel.id"
            :label="channel.name"
            :value="channel.id"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <el-row :gutter="10">
      <el-col :span="1.5">
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Check" @click="handleAdd()"> 批量缴费确认 </el-button>
      </el-col>
    </el-row>
    <el-radio-group v-model="tableType" class="mt-30">
      <el-radio-button :value="0" label="全额待抵扣" />
      <el-radio-button :value="1" label="部分待抵扣" />
      <el-radio-button :value="2" label="已抵扣明细" />
    </el-radio-group>
    <el-table
      ref="tableContainer"
      v-loading="loading"
      :data="list"
      class="mt-15 w-full"
      :height="tableHeight"
    >
      <template v-for="item in columns">
        <el-table-column
          v-if="item.visible"
          :key="item.prop"
          :label="item.label"
          :align="item.align || 'left'"
          :prop="item.prop"
          :width="item.width"
          :fixed="item.fixed"
        >
          <template v-if="item.prop === 'pass'" #default="scope">
            <el-tag :type="scope.row.pass ? 'success' : 'primary'">
              {{ scope.row.pass ? '已审核' : '待审核' }}
            </el-tag>
          </template>
          <template v-else-if="item.prop === 'isInvoice'" #default="scope">
            <el-tag :type="scope.row.isInvoice ? 'success' : 'danger'">
              {{ scope.row.isInvoice ? '是' : '否' }}
            </el-tag>
          </template>
          <template v-else-if="item.prop === 'importStatus'" #default="scope">
            <el-tag v-if="scope.row.importStatus === 1" type="warning"> 导入中 </el-tag>
            <el-tag v-else-if="scope.row.importStatus === 2" type="success"> 导入成功 </el-tag>
            <el-tag v-else-if="scope.row.importStatus === 3" type="error"> 导入失败 </el-tag>
            <el-tag v-else type="primary"> 待导入 </el-tag>
          </template>
          <template v-else #default="scope">
            {{ scope.row[item.prop] }}
          </template>
        </el-table-column>
      </template>
      <!-- <el-table-column label="操作" align="right" width="220" fixed="right">
        <template #header>
          <div style="text-align: center"> 操作 </div>
        </template>
        <template #default="scope">
          <el-button
            v-if="!scope.row.pass"
            link
            :disabled="scope.row.importStatus === 1"
            icon="Edit"
            type="primary"
            @click="handleAudit(scope.row)"
          >
            审核
          </el-button>
          <el-button
            v-else-if="scope.row.auditAble && scope.row.pass"
            link
            :disabled="scope.row.importStatus === 1"
            icon="Edit"
            type="warning"
            @click="handleAuditAgain(scope.row)"
          >
            反审核
          </el-button>
          <el-button type="primary" icon="View" link @click="toDetail(scope.row)">
            查看详情
          </el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { auditBill, deleteBillBatch, getBillChannel, getBillPage } from '@/api/channelBilling/index'
import { formatSimpleDate, getFirstDayOfMonth } from '@/utils'
import { billTypeMap } from '@/utils/constants'
import { Delete, Plus } from '@element-plus/icons-vue'
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 待抵扣结算
defineOptions({ name: 'PendingDeductionSettlement' })

const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)
const otaAddDialog = ref(null)
const queryRef = ref(null)

const message = useMessage()
const router = useRouter()
// 筛选框数据
const auditStatusList = ref([
  { label: '待审核', value: false },
  { label: '已审核', value: true }
])
const isRecycleInvoices = ref([
  { label: '是', value: true },
  { label: '否', value: false }
])

const tableType = ref(0)
// 列信息
const columns = ref([
  { label: `门店ID`, visible: true, prop: 'billNo', width: '100' },
  { label: `门店名称`, visible: true, prop: 'maker', width: '100' },
  { label: `账单期间`, visible: true, prop: 'makeTime', width: '200' },
  { label: `期初余额`, visible: true, prop: 'billType', width: '160' },
  { label: `本期金额`, visible: true, prop: 'beginDate', width: '160' },
  { label: `违约金`, visible: true, prop: 'endDate', width: '200' },
  { label: `本期应收`, visible: true, prop: 'channelName', width: '360' },
  { label: `冲预收款`, visible: true, prop: 'payMoney', width: '160' },
  { label: `应缴金额`, visible: true, prop: 'bookCount', width: '160' },
  { label: `本期实收`, visible: true, prop: 'roomFee', width: '160' },
  { label: `期初预收`, visible: true, prop: 'otherFee', width: '160' },
  { label: `本期预收`, visible: true, prop: 'shiFee', width: '160' },
  { label: `期末预收`, visible: true, prop: 'stayDays', width: '160' },
  { label: `缴费期间`, visible: true, prop: 'shiDays', width: '160' },
  { label: `营业收入`, visible: true, prop: 'mayPay', width: '160' },
  { label: `商标使用费`, visible: true, prop: 'yongJin', width: '160' },
  { label: `积分产生数`, visible: true, prop: 'yongJinCha', width: '160' },
  { label: `积分款`, visible: true, prop: 'yhMoney', width: '160' },
  { label: `预定间夜数`, visible: true, prop: 'shiPay', width: '160' },
  { label: `预定佣金`, visible: true, prop: 'pass', width: '160' },
  { label: `运营质检费`, visible: true, prop: 'passer', width: '160' }
])

// 表格相关数据
const loading = ref(false)
const total = ref(0)
const list = ref([])
const selectedIds = ref([])
const selectedRows = ref([])
const selectedRow = ref({})
const queryParams = reactive({
  billType: billTypeMap[4],
  pageSize: 20,
  aTime: getFirstDayOfMonth(),
  pageNum: 1,
  channel: undefined,
  pass: undefined,
  invoice: undefined
})

// 查询渠道列表
const channelList = ref([])
const getBillChannelList = async () => {
  const res = await getBillChannel()
  channelList.value = res.data
}

/** 查询列表 */
const getList = async () => {
  queryParams.aTime = formatSimpleDate(queryParams.aTime, true).split('-').slice(0, 2).join('-')
  loading.value = true
  try {
    const res = await getBillPage(queryParams)
    list.value = res.data.list
    total.value = res.data.total
  } finally {
    loading.value = false
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryRef.value.resetFields()
  handleQuery()
}

const toDetail = (row) => {
  router.push({
    path: '/channelBilling/statementAccountDetails',
    query: {
      billNo: row.billNo,
      billType: billTypeMap[4]
    }
  })
}
// 新增按钮操作
const handleAdd = () => {
  otaAddDialog.value?.open()
}
// 删除按钮操作
const handleDelete = async () => {
  if (!selectedRows.value.length) {
    message.warning('请选择至少一条数据')
    return
  }
  let hasAudited = selectedRows.value.some((v) => v.pass)
  if (hasAudited) {
    message.error(
      selectedIds.value.length === 1
        ? '账单已审核，无法删除。'
        : '当前选中账单存在已审核账单，无法删除。'
    )
    return
  }
  await message.confirm('账单删除后不可恢复，请确认是否继续删除？')
  await deleteBillBatch({ billNos: selectedIds.value })
  message.success('删除账单成功')
  selectedIds.value = []
  selectedRows.value = []
  handleQuery()
}
// 多选框选中数据
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
  selectedIds.value = selection.map((item) => item.billNo)
}
/** 导出按钮操作 */
const handleExport = async () => {
  message.info('功能待开发...')
  // proxy.$modal.confirm('确认导出此报表吗？').then(async () => {
  //   const params = {
  //     ...queryParams.value
  //   }
  //   const res = await api(params)
  //   if (res.code === 200) {
  //     proxy.$modal.alertSuccess('文件导出中，请前往【导出管理】-【文件下载】列表下载文件。')
  //   } else {
  //     proxy.$modal.msgSuccess(res.msg)
  //   }
  // })
}

// 审核操作
const auditVisible = ref(false)
const handleAudit = (row) => {
  // 1.待审核可以去审核操作
  // 2.审核后当天可以反审核，次日后，反审核按钮消失，不允许反审核
  // 如果没有账单重算，点击审核，提醒
  if (!row.auditAble) {
    message.error('请点击账单重算后，再审核确认审核后，次日进入清分')
    return
  }
  selectedRow.value = row
  auditVisible.value = true
}

// 反审核
const handleAuditAgain = async (row) => {
  selectedRow.value = row
  await message.confirm('确认反审核此账单？', '反审核')
  await auditBill(selectedRow.value.billNo, {
    operate: 2
  })
  message.success('反审核成功')
  handleQuery()
}

onMounted(() => {
  getBillChannelList()
  getList()
})
</script>
<style lang="scss" scoped></style>
