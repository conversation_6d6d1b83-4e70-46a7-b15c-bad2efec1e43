<template>
  <div class="app-container">
    <table border class="table1">
      <tr>
        <th>账单期间</th>
        <td>{{ atime }}</td>
        <th>门店id</th>
        <td>{{ shopId }}</td>
        <th>门店名称</th>
        <td>{{ shopName }}</td>
      </tr>
    </table>

    <div v-loading="datasLoading" class="card" style="min-height: 120px;">
      <el-card v-for="(item, index) in cardDatas" :key="index" shadow="never">
        <div>{{ item.name }}</div>
        <p>
          <el-text type="primary">
            {{ item.value }}
          </el-text>
        </p>
      </el-card>
    </div>
    <el-table 
      v-loading="tableLoading" 
      :data="tableData" 
      class="mt-15" 
      style="width: 100%"
      :height="autoHeight"
    >
      <el-table-column prop="no" label="序号" min-width="50">
        <template #default="scope">
          <div style="text-wrap: nowrap;">
            {{ (paginationInfo.currentPage - 1) * paginationInfo.pageSize + scope.$index + 1 }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="atime" label="账单期间" min-width="100"></el-table-column>
      <el-table-column prop="atime" label="期初余额" min-width="100"></el-table-column>
      <el-table-column prop="atime" label="本期应收" min-width="100"></el-table-column>
      <el-table-column prop="atime" label="费用调整" min-width="100"></el-table-column>
      <el-table-column prop="atime" label="实收金额" min-width="100"></el-table-column>
      <el-table-column prop="atime" label="期末余额" min-width="100"></el-table-column>
    </el-table>
    <!-- 分页 右对齐 -->
    <el-pagination
      v-if="paginationInfo.totalCount > 0"
      class="flex flex-jus-end mt-15"
      background
      :current-page="paginationInfo.currentPage"
      :page-sizes="paginationInfo.pageSizes"
      :page-size="paginationInfo.pageSize"
      :layout="paginationInfo.layout"
      :total="paginationInfo.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
  </div>
</template>
<script setup name="PreBalanceDetail">
// 前期结余 /managementFeeInvoice/preBalanceDetail?shopId=02322&atime=2025-02-01
import { paginationInfoConstant } from '@/utils/constants.js'
import { ref } from 'vue'

import { getPreBalance, getPreBalanceList } from '@/api/managementFeeInvoice/preBalance.js'

const router = useRouter()
const route = useRoute() 

// 表格自动高度
import { autoHeightFunc } from '@/utils/script.js'
const autoHeight = ref(200)
const setHeight = () => {
  let height = 370
  autoHeight.value = autoHeightFunc(height)
}
// 监听窗口变化
window.onresize = () => {
  setHeight()
}
setHeight()

// 接收参数
const params = ref({
  shopId: route.query.shopId,
  shopName: route.query.shopName,
  atime: route.query.atime
})

const shopId = ref('')
const shopName = ref('')
const atime = ref('')

const cardDatas = ref({
  proMoney: {
    name: '期初余额',
    value: 0
  }, // 期初余额
  thisMoney: {
    name: '本期应收+费用调整',
    value: 0
  }, // 本期应收+费用调整
  clearMoney: {
    name: '本期清分抵扣',
    value: 0
  }, // 本期清分抵扣
  backMoney: {
    name: '本期回款',
    value: 0
  }, // 本期回款
  endBalance: {
    name: '期末余额',
    value: 0
  } // 期末余额
})

// 表格数据
const tableData = ref([])
const tableLoading = ref(false) // 表格加载状态
const datasLoading = ref(false) // 数据加载状态
// 分页信息
const paginationInfo = reactive({
  ...structuredClone(paginationInfoConstant)
})
// 每页条数改变
const handleSizeChange = (val) => {
  paginationInfo.pageSize = val
  searchForm()
}
// 当前页码改变
const handleCurrentChange = (val) => {
  paginationInfo.currentPage = val
  searchForm()
}
// 结余明细
const getPreBalanceFunc = () => {
  datasLoading.value = true
  getPreBalance(params.value).then((res) => {
    if(res.code == 200) {
      for(let item in cardDatas.value){
        cardDatas.value[item].value = res.data[item]
      }
    }
  }).finally(() => {
    datasLoading.value = false
  })
}
// 查询前期结余明细列表
const getPreBalanceListFunc = () => {
  tableLoading.value = true
  getPreBalanceList(params.value).then((res) => {
    if(res.code == 200) {

      tableData.value = res.data.rows
      paginationInfo.total = res.data.total

    }
  }).finally(() => {
    tableLoading.value = false
  })
}
onMounted(() => {

  // 获取门店id和名称
  shopId.value = params.value.shopId
  shopName.value = params.value.shopName
  atime.value = params.value.atime

  getPreBalanceFunc()
  getPreBalanceListFunc()
})

</script>
<style scoped lang="scss">
  .card {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    .el-card {
      width: 19%;
      div {
        font-size: 14px;
        color:#999;
        margin-bottom: 15px;
      }
      p {
        font-family: Arial, Helvetica, sans-serif;
        margin:0;
        padding:5px 0;
        text-align: center;
         * {
          font-size: 30px;
        }
      }
    }
  }
</style>
