<template>
  <div class="p-15">
    <el-form :model="queryForm" :inline="true" label-width="110px" @keydown.enter="searchForm(-1)">
      <el-form-item label="账单期间" prop="aTime">
        <el-date-picker
          v-model="queryForm.atime"
          type="month"
          placeholder="账单期间"
          value-format="YYYY-MM"
          :clearable="false"
          :disabled-date="disabledDate"
          class="w-240-important"
        />
      </el-form-item>
      <el-form-item label="门店" prop="shopName">
        <el-input
          v-model="queryForm.shopName"
          placeholder="请输入门店编号/门店名称"
          clearable
          class="w-240"
          :prefix-icon="Search"
        />
      </el-form-item>
      <el-form-item label="地区总部" prop="headOffice">
        <el-select
          v-model="queryForm.headOffice"
          placeholder="请选择地区总部"
          clearable
          filterable
          class="w-240"
          @change="handleheadOfficeChange"
        >
          <el-option
            v-for="item in queryFormList.shopHeadOfficeList"
            :key="item.freeString"
            :label="item.freeString"
            :value="item.freeString"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="大区" prop="dq">
        <el-select
          v-model="queryForm.dq"
          placeholder="请选择大区"
          clearable
          filterable
          class="w-240"
          @change="handleDqChange"
        >
          <el-option
            v-for="item in queryFormList.shopDqList"
            :key="item.eareId"
            :label="item.eareName"
            :value="item.eareId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="城区" prop="cq">
        <el-select
          v-model="queryForm.cq"
          placeholder="请选择城区"
          clearable
          filterable
          class="w-240"
        >
          <el-option
            v-for="item in queryFormList.shopCqList"
            :key="item.eareId"
            :label="item.eareName"
            :value="item.eareId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否欠费" prop="debtStatus">
        <el-select
          v-model="queryForm.debtStatus"
          placeholder="请选择是否欠费"
          clearable
          filterable
          class="w-240"
        >
          <el-option
            v-for="item in isStatus"
            :key="item.id"
            :label="item.label"
            :value="item.valueString"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否参与抵扣" prop="deductStatus">
        <el-select
          v-model="queryForm.deductStatus"
          placeholder="请选择是否参与抵扣"
          clearable
          filterable
          class="w-240"
        >
          <el-option
            v-for="item in isStatus"
            :key="item.id"
            :label="item.label"
            :value="item.valueString"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="门店状态" prop="shopStatus">
        <el-select
          v-model="queryForm.shopStatus"
          placeholder="请选择门店状态"
          clearable
          filterable
          class="w-240"
        >
          <el-option
            v-for="item in shop_status"
            :key="item.value"
            :label="item.label"
            :value="item.label"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计费审核状态" prop="calAuditStatus">
        <el-select
          v-model="queryForm.calAuditStatus"
          placeholder="请选择计费审核状态"
          clearable
          class="w-240"
          filterable
        >
          <el-option
            v-for="item in charge_audit_status"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="应收审核状态" prop="auditStatus">
        <el-select
          v-model="queryForm.auditStatus"
          placeholder="请选择应收审核状态"
          clearable
          class="w-240"
          filterable
        >
          <el-option label="未审核" value="0"></el-option>
          <el-option label="已审核" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="searchForm(-1)"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetForm()"> 重置 </el-button>
        <el-button icon="Download" type="warning" plain @click="handleExport()"> 导出 </el-button>
        <!-- 如果当前月份不是账期月份则不显示按钮 -->
        <template v-if="btnHide">
          <el-button
            type="warning"
            plain
            :disabled="selectStatus.auditStatus === '1'"
            :loading="shenheLoading"
            @click="handleResetCount()"
          >
            费用重新计算
          </el-button>
          <el-button
            v-if="selectStatus.auditStatus === '1'"
            type="warning"
            plain
            :disabled="selectStatus.lockStatus === '1'"
            class="mr-15"
            :loading="shenheLoading"
            @click="handleShenHe(0)"
          >
            应收反审核
          </el-button>
          <el-button
            v-else
            type="warning"
            plain
            :disabled="selectStatus.lockStatus === '1'"
            :loading="shenheLoading"
            class="mr-15"
            @click="handleShenHe(1)"
          >
            应收审核
          </el-button>

          <el-dropdown placement="bottom" @command="handleQingFen">
            <el-button type="warning" plain style="outline: none" :loading="shenheLoading">
              清分抵扣
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="item in qingFenStatus" :key="item.id" :command="item.id">
                  {{ item.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-form-item>
    </el-form>
    <el-table
      ref="tableContainer"
      v-loading="tableLoading"
      :data="tableData"
      class="mt-15"
      style="width: 100%"
      :height="tableHeight"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" :selectable="selectable" width="55"></el-table-column>
      <el-table-column prop="no" label="序号" min-width="50">
        <template #default="scope">
          <div style="text-wrap: nowrap">
            {{ (paginationInfo.currentPage - 1) * paginationInfo.pageSize + scope.$index + 1 }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="atime" label="账单期间" min-width="100"></el-table-column>
      <el-table-column prop="shopId" label="门店ID" width="90">
        <template #default="scope">
          <el-link type="primary" underline @click="handleBillingDetail(scope.row)">
            {{ scope.row.shopId }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="shopName" label="门店名称" min-width="250"></el-table-column>
      <el-table-column prop="headOffice" label="地区总部" min-width="200"></el-table-column>
      <el-table-column prop="dq" label="大区" min-width="200"></el-table-column>
      <el-table-column prop="cq" label="城区" min-width="200"></el-table-column>
      <el-table-column prop="manager" label="城区总" min-width="150"></el-table-column>
      <el-table-column prop="deductStatus" label="是否参与抵扣" min-width="130">
        <template #default="scope">
          <el-tag v-if="scope.row.deductStatus == 1" type="success"> 参与 </el-tag>
          <el-tag v-else type="danger"> 未参与 </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="debtStatus" label="是否欠费" min-width="130">
        <template #default="scope">
          <el-tag v-if="scope.row.debtStatus == 1" type="danger"> 是 </el-tag>
          <el-tag v-else type="success"> 否 </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="proMoney" label="上期欠款" min-width="150"></el-table-column>
      <el-table-column prop="thisMoney" label="本期应收" min-width="150"></el-table-column>
      <el-table-column prop="actualMoney" label="本期实收" min-width="150"></el-table-column>
      <el-table-column prop="adjustMoney" label="费用调整" min-width="150"></el-table-column>
      <el-table-column prop="debtMoney" label="累计欠费" min-width="150"></el-table-column>
      <el-table-column prop="auditStatus" label="应收审核状态" min-width="150">
        <template #default="scope">
          <el-tag v-if="scope.row.auditStatus == 1" type="success"> 已审核 </el-tag>
          <el-tag v-else type="danger"> 未审核 </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="lockStatus" label="锁账状态" min-width="150">
        <template #default="scope">
          <el-tag v-if="scope.row.lockStatus == 1" type="success"> 已锁账 </el-tag>
          <el-tag v-else type="danger"> 未锁账 </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="shopStatus" label="门店状态" min-width="150"></el-table-column>
      <el-table-column prop="stopCharge" label="停止计费状态" min-width="150">
        <template #default="scope">
          <el-tag v-if="scope.row.stopCharge == 1" type="danger"> 停止 </el-tag>
          <el-tag v-else type="success"> 正常 </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="endMoney" label="期末余额" min-width="150"></el-table-column>
      <el-table-column prop="calAuditStatus" label="计费审核状态" min-width="150">
        <template #default="scope">
          <template v-for="item in charge_audit_status">
            <el-tag v-if="scope.row.calAuditStatus == item.value" :key="item.value" type="primary">
              {{ item.label }}
            </el-tag>
          </template>
        </template>
      </el-table-column>

      <el-table-column prop="shopName" label="操作" width="250" fixed="right">
        <template #default="scope">
          <el-button type="primary" link class="noborder" icon="View" @click="info(scope.row)">
            档案信息
          </el-button>
          <el-button
            type="warning"
            class="noborder"
            link
            icon="View"
            @click="handlePreBalanceDetail(scope.row)"
          >
            前期结余明细
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 右对齐 -->
    <el-pagination
      v-if="paginationInfo.totalCount > 0"
      class="flex flex-jus-end mt-15"
      background
      :current-page="paginationInfo.currentPage"
      :page-sizes="paginationInfo.pageSizes"
      :page-size="paginationInfo.pageSize"
      :layout="paginationInfo.layout"
      :total="paginationInfo.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
  </div>
</template>
<script setup name="StoreBillingList">
// 门店账单列表
import router from '@/router'
import { isStatus, paginationInfoConstant } from '@/utils/constants.js'
import { download2 } from '@/utils/request'
import { Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 引入请求
import {
  getShopCqList, // 查询门店城区列表
  // 查询门店地区总部列表
  getShopDqList,
  getShopHeadOfficeList
} from '@/api/chargingManager/search'
// 引入请求
import {
  auditConfirm, // 应收审核弹框信息查询接口
  deductBalance,
  getList, // 清分抵扣
  preDeductBalance, // 是否重复查询
  // 查询门店列表
  updateAudit, // 修改审核/反审核
  updateCalculate
} from '@/api/managementFeeInvoice/search'

import { disabledDate } from '@/utils/script.js'

const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)

const { proxy } = getCurrentInstance()
const { shop_status, charge_audit_status } = proxy.useDict('shop_status', 'charge_audit_status')

// 搜索表单的引用格式
const nowDate = new Date()

// 三个按钮是否显示的状态
const btnHide = ref(false) // 按钮是否显示

const formInterface = {
  shopName: '', // 门店名称
  atime: `${nowDate.getFullYear()}-${nowDate.getMonth().toString().padStart(2, '0')}`, // 账单期间
  headOffice: '', // 总部
  dq: '', // 大区
  cq: '', // 城市
  deductStatus: '', // 是否参与抵扣 0未参与1参与
  debtStatus: '', // 是否欠费 0未欠费1欠费
  shopStatus: '', // 门店状态
  calAuditStatus: '', // 计费审核状态
  auditStatus: '' // 应收审核状态 0待审核 1已审核
}
// 搜索下拉列表选项
const queryFormList = reactive({
  shopHeadOfficeList: [], // 地区总部
  shopDqList: [], // 大区
  shopCqList: [] // 城区
})
// 搜索条件
const queryForm = reactive(Object.assign({}, formInterface))

// 重置搜索表单
const resetForm = () => {
  // 遍历清空对象
  Object.assign(queryForm, formInterface) // 重置表单数据
  // 清空大区和城区列表
  queryFormList.shopDqList = []
  queryFormList.shopCqList = []
}

// 查询门店列表参数封装
const getParams = () => {
  // 如果有大区则获取大区名称，否则为空字符串
  let dq = ''
  if (queryForm.dq.length > 0) {
    dq = queryFormList.shopDqList.filter((item) => item.eareId == queryForm.dq)[0]['eareName']
  }
  // 查询门店列表参数封装
  return {
    shopName: queryForm.shopName, // 门店名称 string
    atime: queryForm.atime.length > 0 ? `${queryForm.atime}-01` : '', // 账单期间 string
    headOffice: queryForm.headOffice, // 总部名称 string
    dq: dq, // 大区 string   queryForm.dq
    cq: queryForm.cq, // 城区 id
    deductStatus: queryForm.deductStatus, // 是否参与 1参与0未参与 number
    debtStatus: queryForm.debtStatus, // 是否欠费 1是0否 number

    shopStatus: queryForm.shopStatus, // 门店状态
    calAuditStatus: queryForm.calAuditStatus, // 计费审核状态
    auditStatus: queryForm.auditStatus // 应收审核状态 0待审核 1已审核
  }
}

// 查询按钮
const searchForm = (id) => {
  // 如果是查询按钮，则重置当前页码和每页条数
  if (id == -1) {
    paginationInfo.currentPage = 1
  }
  tableLoading.value = true // 加载状态
  const params = {
    ...getParams(),
    pageNum: paginationInfo.currentPage, // 当前页码 number
    pageSize: paginationInfo.pageSize // 每页条数 number
  }
  // 查询门店列表
  getList(params)
    .then((res) => {
      tableLoading.value = false // 加载状态
      // console.log("门店列表:",res);
      if (res.code == 200) {
        tableData.value = res.data.records // 表格数据
        paginationInfo.currentPage = res.data.pageNum // 重置当前页码
        paginationInfo.totalCount = res.data.total // 总条数

        // 如果queryForm.atime不等于上个月,则返回true
        btnHide.value =
          queryForm.atime ==
          `${nowDate.getFullYear()}-${nowDate.getMonth().toString().padStart(2, '0')}`
      } else {
        ElMessage.error(res.msg)
      }
    })
    .catch((err) => {
      tableLoading.value = false // 加载状态
    })
}
onMounted(() => {
  // 地区总部列表查询
  getShopHeadOfficeList().then((res) => {
    if (res.code == 200) {
      queryFormList.shopHeadOfficeList = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
  searchForm(-1) // 默认查询第一页数据
})
// 表格数据
const tableData = ref([])
const tableLoading = ref(false) // 表格加载状态

// 分页信息
const paginationInfo = reactive({
  ...structuredClone(paginationInfoConstant)
})
// 每页条数改变
const handleSizeChange = (val) => {
  paginationInfo.pageSize = val
  searchForm()
}
// 当前页码改变
const handleCurrentChange = (val) => {
  paginationInfo.currentPage = val
  searchForm()
}
// 切换地区总部
const handleheadOfficeChange = (val) => {
  queryForm.cq = '' // 清空城区值
  queryFormList.shopCqList = [] // 清空城区列表
  queryForm.dq = '' // 清空大区值
  queryFormList.shopDqList = [] // 清空大区列表

  if (val && val.length > 0) {
    // 查询大区列表
    getShopDqList({ freeString: val }).then((res) => {
      if (res.code == 200) {
        queryFormList.shopDqList = res.data
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}
// 切换大区
const handleDqChange = (val) => {
  queryForm.cq = '' // 清空城区值
  queryFormList.shopCqList = [] // 清空城区列表
  if (val && val.length > 0) {
    // 查询大区列表
    getShopCqList({ eareId: val }).then((res) => {
      if (res.code == 200) {
        queryFormList.shopCqList = res.data
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}

// 档案信息
const info = (row) => {
  console.log('档案信息:', row)
  router.push({
    path: '/storeProfile/detail',
    query: {
      shopId: row.shopId
    }
  })
}
// 前期结余明细
const handlePreBalanceDetail = (row) => {
  // console.log('前期结余明细:', row)
  router.push({
    path: '/managementFeeInvoice/preBalanceDetail',
    query: {
      shopId: row.shopId,
      atime: row.atime,
      shopName: row.shopName
    }
  })
}
// 账单详情
const handleBillingDetail = (row) => {
  console.log('账单详情:', row)
  router.push({
    path: '/managementFeeInvoice/storeBillingDetails',
    query: {
      shopId: row.shopId,
      shopName: row.shopName,
      deductStatus: row.deductStatus,
      atime: row.atime
    }
  })
}

// 选中的门店id
const selectShopIds = ref([])
const selectShopNames = ref([])
// 当前选中的状态
const selectStatus = ref({
  lockStatus: -1, // 锁账  0 锁账前 1 锁账后
  auditStatus: -1 // 审核  0 未审核 1 已审核
})
// 表格多选
const handleSelectionChange = (val) => {
  selectShopIds.value = [] // 清空选中的门店id
  selectShopNames.value = []
  // console.log("表格多选:", val);
  // 肖璐 3/11 14:35:37
  // 1、如果是锁账，表示所有门店都锁账了。
  // 2、在选择门店时，如果选择不同的门店状态，给个toast提醒：请选择相同审核状态的门店

  // 肖璐 3/11 14:36:55
  // 如果不选择门店，点击费用重新计算：表示把未审核的门店全部重新计算
  // 点击应收审核：把未审核的门店全部审核
  let obj = {} // 存储临时数据
  if (val.length > 0) {
    // 判断当前选中的门店状态是否一致
    for (let item of val) {
      if (
        obj['lockStatus'] &&
        (obj['lockStatus'] != item['lockStatus'] || obj['auditStatus'] != item['auditStatus'])
      ) {
        ElMessage.warning('请选择相同审核状态的门店')
        selectStatus.value.lockStatus = -1 // 锁账  0 锁账前 1 锁账后
        selectStatus.value.auditStatus = -1 // 审核  0 未审核 1 已审核
        return false
      } else {
        obj['lockStatus'] = item['lockStatus'] // 锁账  0 锁账前 1 锁账后
        obj['auditStatus'] = item['auditStatus'] // 审核  0 未审核 1 已审核
      }
    }
    selectStatus.value.lockStatus = val[0]['lockStatus'] // 锁账  0 锁账前 1 锁账后
    selectStatus.value.auditStatus = val[0]['auditStatus'] // 审核  0 未审核 1 已审核
    // 当前选中的门店
    for (let item of val) {
      selectShopIds.value.push(item['shopId']) // 选中的门店id
      selectShopNames.value.push(item['shopName']) // 选中的门店名称
    }
  } else {
    selectStatus.value.lockStatus = -1 // 锁账  0 锁账前 1 锁账后
    selectStatus.value.auditStatus = -1 // 审核  0 未审核 1 已审核
  }
}
// 数据行是否可选
const selectable = (row) => {
  return true
}

const shenheLoading = ref(false) // 加载中

// 费用重新计算
const handleResetCount = async () => {
  // console.log("费用重新计算",selectShopIds.value.join(','))
  shenheLoading.value = true // 加载中
  let params = {
    shopId: '-1', // 门店id字符串
    atime: `${queryForm.atime}-01`
  }
  if (selectShopIds.value.length > 0) {
    params['shopId'] = selectShopIds.value.join(',') // 门店id字符串
  }

  const a1 = await ElMessageBox.confirm(
    `
      选择门店中包含应收已审核门店，请反审核后重新导入数据后重新计算。
    `,
    '费用重新计算',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(
    () => true,
    () => false
  )
  if (!a1) {
    shenheLoading.value = false
    return false
  }

  // 如果选择中包含【计费规则待审核】的门店，点击【费用重新计算】时提醒：选择门店中包含计费规则未审核的门店，请确认计费规则状态
  const a2 = await ElMessageBox.confirm(
    `
      选择门店中包含计费规则未审核的门店，请确认计费规则状态。
    `,
    '费用重新计算',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(
    () => true,
    () => false
  )
  if (!a2) {
    shenheLoading.value = false
    return false
  }

  updateCalculate(params)
    .then((res) => {
      console.log('费用重新计算:', res)
      if (res.code == 200) {
        ElMessage.success(res.msg)
        searchForm()
      } else {
        ElMessage.error(res.msg)
      }
      shenheLoading.value = false
    })
    .catch((err) => {
      shenheLoading.value = false
    })
}

// 应收审核
const handleShenHe = async (auditStatus) => {
  console.log('应收审核:', auditStatus, selectShopIds.value.join(','))
  shenheLoading.value = true // 加载中
  const params = {
    shopId: '-1', // 门店id字符串
    auditStatus: auditStatus, // 审核状态 0反审核 1审核 number
    atime: `${queryForm.atime}-01`
  }
  if (selectShopIds.value.length > 0) {
    params['shopId'] = selectShopIds.value.join(',') // 门店id字符串
  }

  // 应收审核弹窗
  let args1 = {}
  //if(auditStatus == 1){ //审核反审核都需要请求
  let t = await new Promise((resolve) => {
    auditConfirm(params)
      .then((res) => {
        args1 = res.data
        console.log('应收审核弹窗:', res)
        // 一次确认
        const tishi1 = [
          { title: '反审核', content: `取消审核门店：${args1.shopCount}家` },
          { title: '应收审核', content: `确认审核门店：${args1.shopCount}家` }
        ]
        ElMessageBox.confirm(
          `
          确认审核门店：${res.data.shopCount}家
          门店计费状态待审核：${res.data.auditShopCount}家
        `,
          tishi1[auditStatus].title,
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            resolve(true)
          })
          .catch(() => {
            resolve(false)
          })
      })
      .catch((err) => {
        shenheLoading.value = false // 加载中
      })
  })
  if (!t) {
    shenheLoading.value = false // 加载中
    return false
  }
  // }

  // 二次确认
  const tishi2 = [
    { title: '反审核', content: `取消审核门店：${args1.shopCount}家` },
    { title: '审核', content: `确认审核门店：${args1.shopCount}家` }
  ]

  ElMessageBox.confirm(tishi2[auditStatus].content, tishi2[auditStatus].title, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 应收审核 反审核
      updateAudit(params)
        .then((res) => {
          console.log(tishi2[auditStatus].title, ':', res)
          if (res.code == 200) {
            ElMessage.success(res.msg)
          } else {
            ElMessage.error(res.msg)
          }
          searchForm()
          shenheLoading.value = false // 加载中
        })
        .catch((err) => {
          ElMessage.error(err)
          shenheLoading.value = false // 加载中
        })
    })
    .catch(() => {
      console.log('取消')
      shenheLoading.value = false // 加载中
    })
}

// 清分状态列表
const qingFenStatus = [
  { id: 'all', label: '全部清分抵扣' },
  { id: 'manage', label: '管理费清分抵扣' },
  { id: 'dianzhang', label: '店长工资清分抵扣' },
  { id: 'yingxiao', label: '营销经理清分抵扣' }
]

// 清分抵扣
const handleQingFen = async (val) => {
  console.log('清分抵扣:', val)
  // {
  //     "type":"all",//all- 全部清分抵扣，manage-管理费，dianzhang 店长 ，yingxiao - 营销经理
  //     "atime":"2025-05-01",//页面上显示的账期
  //     "shopIds":[]//选中的门店列表
  // }
  shenheLoading.value = true // 加载中
  // 1.如果选择全量发送，则不需要指定shopId，同时发送的是全量费用
  // 2.如果选择发送部分费用，则一定需要指定门店
  const params = {
    // shopId: "-1", // 门店id字符串
    atime: `${queryForm.atime}-01`,
    type: val
  }
  const names = selectShopNames.value.join(',')

  const status = qingFenStatus.filter((item) => item.id == val)[0]['label']

  const tishi = [
    { title: '发送清分抵扣', content: `确认发送(${names})门店,${status}.` },
    {
      title: '发送清分抵扣',
      content: `确认发送${status}项,门店${paginationInfo.totalCount}家.`
    }
  ]

  let index = 0
  if (selectShopIds.value.length <= 0) {
    index = 1
  }
  // 如果没有选择门店，则提示确认全部门店
  let a1 = await ElMessageBox.confirm(tishi[index]['content'], tishi[index]['title'], {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(
    () => true,
    () => false
  )
  // 选了否则返回
  if (!a1) {
    shenheLoading.value = false // 加载中
    return false
  }

  if (selectShopIds.value.length > 0) {
    params['shopIds'] = selectShopIds.value // 门店id字符串

    let b = await preDeductBalance(params)
    if (b.code == 500) {
      ElMessageBox.confirm('本账期已发送清分抵口,请勿重复点击.', '发送清分抵扣', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {})
        .catch(() => {})
      shenheLoading.value = false // 加载中
      return false
    }
  }

  // console.log("清分抵扣参数:",params)
  deductBalance(params)
    .then((res) => {
      console.log('清分抵扣:', res)
      if (res.code == 200) {
        ElMessage.success(res.msg)
        searchForm()
      } else {
        ElMessage.error(res.msg)
      }
      shenheLoading.value = false // 加载中
    })
    .catch((err) => {
      shenheLoading.value = false // 加载中
      console.log('清分抵扣失败:', err)
    })
}

// 导出
const handleExport = () => {
  const params = { ...getParams() }
  download2('/shop/bill/exportExcel', params, `门店账单列表_${new Date().getTime()}.xlsx`)
}
</script>
