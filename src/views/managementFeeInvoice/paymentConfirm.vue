<template>
  <div class="app-container">
    <el-form ref="queryRef" :model="queryParams" :inline="true" label-width="110px">
      <el-form-item label="门店名称" prop="shopName">
        <el-input
          v-model="queryParams.shopName"
          placeholder="请输入门店编码/门店名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账单期间" prop="aTime">
        <el-date-picker
          v-model="queryParams.aTime"
          type="month"
          :clearable="false"
          placeholder="请选择账单期间"
          style="width: 240px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
        <el-button type="primary" plain icon="View" @click="toAdvances()"> 预收款待结算 </el-button>
        <el-button type="primary" plain icon="View" @click="toDeduction()"> 待抵扣结算 </el-button>
        <el-button type="warning" plain icon="Check" @click="handleAdd()"> 缴费确认 </el-button>
      </el-form-item>
    </el-form>
    <div class="detail-title">
      <span class="area-name">地区总部-大区-城区</span>
      <div class="info">
        <span class="label">分店状态</span>
        <el-input v-model="params.atime" style="width: 240px" disabled />
      </div>
      <div class="info">
        <span class="label">开业日期</span>
        <el-date-picker v-model="params.atime" type="month" style="width: 240px" disabled />
      </div>
      <div class="info">
        <span class="label">解约日期</span>
        <el-date-picker v-model="params.atime" type="month" style="width: 240px" disabled />
      </div>
    </div>

    <div class="detail-content">
      <div class="detail-content-left">
        <p class="content-title"> 参与清分抵扣 </p>

        <el-table :data="chargeBasicList" style="width: 340px" :show-header="false">
          <el-table-column key="feeTitle" label="费用项" align="left" prop="feeTitle" width="180" />
          <el-table-column key="feeMoney" label="金额" align="left" prop="feeMoney" width="120" />
        </el-table>
      </div>

      <div class="detail-content-right">
        <div class="content-title">
          <el-checkbox>预收分项抵扣</el-checkbox>
          <!-- <span class="title-text"></span> -->
        </div>

        <div class="content-table">
          <el-table :data="chargeInfoList">
            <el-table-column type="index" label="序号" width="60" align="left" />
            <el-table-column
              key="feeTitle"
              label="费用项"
              align="left"
              prop="feeTitle"
              width="160"
            />
            <el-table-column
              key="clearMoney"
              label="前期余额"
              align="left"
              prop="clearMoney"
              width="100"
            />
            <el-table-column
              key="backMoney"
              label="本期应收"
              align="left"
              prop="backMoney"
              width="100"
            />
            <el-table-column
              key="adjustMoney"
              label="本次冲减"
              align="left"
              prop="adjustMoney"
              width="100"
            />
            <el-table-column
              key="endBalance"
              label="本次预收"
              align="left"
              prop="endBalance"
              width="100"
            />
          </el-table>

          <el-table :data="chargeInfoList" class="ml-30">
            <el-table-column
              key="feeTitle"
              label="费用日期"
              align="left"
              prop="feeTitle"
              width="160"
            />
            <el-table-column
              key="clearMoney"
              label="费用类型"
              align="left"
              prop="clearMoney"
              width="100"
            />
            <el-table-column
              key="backMoney"
              label="交易金额"
              align="left"
              prop="backMoney"
              width="100"
            />
            <el-table-column
              key="adjustMoney"
              label="最新余额"
              align="left"
              prop="adjustMoney"
              width="100"
            />
            <el-table-column
              key="endBalance"
              label="备注"
              align="left"
              prop="endBalance"
              width="100"
            />
          </el-table>
        </div>
      </div>
    </div>

    <div class="detail-info">
      <div class="detail-info-right">
        <div class="info-title">
          <span class="title-text">备注</span>
          <el-button type="primary" plain @click="sumbitBillMemo"> 提交 </el-button>
        </div>

        <el-input v-model="billMemo" :rows="10" type="textarea" placeholder="请输入备注" />
      </div>
    </div>
  </div>

  <!-- 款项认领弹窗 -->
  <claimDialog
    v-if="dialogFormVisible"
    v-model="dialogFormVisible"
    :data="dialogDatas"
    @close="claimDialogClose"
  ></claimDialog>
</template>

<script setup name="PaymentConfirm">
import claimDialog from '@/components/managementFeeInvoice/claimDialog.vue'
import { ref } from 'vue'
import {
  getChargeBasicList,
  getChargeInfoList,
  getBillMemo,
  updateBillMemo,
  getCorpList
} from '@/api/managementFeeInvoice/storeBillingDetails'

import { formatSimpleDate, getFirstDayOfMonth } from '@/utils/index'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()
const queryParams = reactive({
  shopName: undefined,
  aTime: getFirstDayOfMonth()
})
const btnType = ref('')
const params = ref({
  shopId: '00001',
  shopName: '尚客优酒店总部1',
  atime: '2025-06-01'
    ? new Date('2025-06-01')
    : formatSimpleDate(new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1), true)
})
const showBtn = computed(() => {
  if (
    formatSimpleDate(new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1), true) >
    formatSimpleDate(params.value.atime, true)
  ) {
    return false
  } else {
    return true
  }
})
const chargeBasicList = ref([]) // 计费基数
const chargeInfoList = ref([]) // 计费明细
const reductionData = ref([]) // 减免信息
const billMemo = ref('') // 备注

const handleQuery = () => {
  console.log('')
}
const getDetails = () => {
  const data = { ...params.value }
  data.atime = formatSimpleDate(data.atime, true)

  getChargeBasicList({ shopId: data.shopId, atime: data.atime }).then((res) => {
    chargeBasicList.value = res.data
  })

  getChargeInfoList({ ...data, corp: btnType.value })
    .then((res) => {
      chargeInfoList.value = res.data
      reductionData.value = res.data
    })
    .catch(() => {
      chargeInfoList.value = []
      reductionData.value = []
    })

  getBillMemo({ shopId: data.shopId }).then((res) => {
    billMemo.value = res.msg
  })

  getCorpList({ shopId: data.shopId }).then((res) => {
    corpList.value = res.data
  })
}

const toFeeManager = () => {
  router.push({
    path: '/chargingManager/feeManagerAdd',
    query: {
      ...route.query
    }
  })
}

const sumbitBillMemo = () => {
  updateBillMemo({ shopId: params.value.shopId, memo: billMemo.value }).then(() => {
    ElMessage.success('保存成功')
  })
}

// 合并单元格
const arraySpanMethod = ({ row, columnIndex }) => {
  if (!['营销经理工资', '店长工资'].includes(row.feeTitle)) {
    row.csyyt = row.thisMoney
    if (columnIndex === 3) {
      return [1, 4]
    } else if (columnIndex > 3 && columnIndex <= 6) {
      return [0, 0]
    }
  }
}

const reductionShow = ref(false) // 费用减免
const handleAdjust = () => {
  reductionShow.value = true
}
const toCostAdjustmentRecord = () => {
  router.push({
    path: '/managementFeeInvoice/costAdjustmentRecord',
    query: {
      id: params.value.shopId
    }
  })
}

const toAdvances = () => {
  router.push({
    path: '/managementFeeInvoice/advancesPendingSettlement'
    // query: {
    //   id: params.value.shopId
    // }
  })
}
const toDeduction = () => {
  router.push({
    path: '/managementFeeInvoice/pendingDeductionSettlement'
    // query: {
    //   id: params.value.shopId
    // }
  })
}

const refundShow = ref(false)
const handleRefund = () => {
  // 退款调整
  refundShow.value = true
}

const dialogFormVisible = ref(false) // 弹窗是否显示
const dialogDatas = ref({}) // 弹窗传递数据

const claimDialogClose = (params) => {
  console.log(params)
  dialogFormVisible.value = false
  // if (params.type === 'success') {
  //   router.push({
  //     path: '/managementFeeInvoice/storeBillingDetails',
  //     query: {
  //       shopId: params.shopId,
  //       shopName: params.shopName
  //     }
  //   })
  //   ElMessage.success('操作成功')
  // }
}

onMounted(() => {
  getDetails()
  if (route.query.from === 'claimFunds') {
    dialogFormVisible.value = true
    dialogDatas.value = JSON.parse(localStorage.getItem('claimFundsData'))
  }
})
</script>
<style scoped lang="scss">
.app-container {
  .detail-title {
    display: flex;
    align-items: start;
    padding-bottom: 30px;
    margin-top: 20px;

    .area-name {
      padding-right: 20px;
      font-size: 22px;
      font-weight: 600;
    }
    .info {
      display: flex;
      align-items: center;
      .label {
        font-size: 18px;
        font-weight: 600;
        margin-right: 20px;
        margin-left: 20px;
      }
    }
  }

  .detail-content {
    display: flex;
    border-radius: 12px;
    border: 1px solid #eee;
    padding: 12px;
    margin: 12px 0;
    overflow: auto;
    max-width: fit-content;

    .detail-content-left {
      padding-right: 12px;
      border-right: 1px dashed #eee;

      .content-title {
        display: inline-block;
        margin: 0px;
        line-height: 42px;
        // padding-bottom: 40px;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .detail-content-right {
      padding-left: 12px;

      .content-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 42px;

        .title-text {
          display: inline-block;
          font-size: 16px;
          font-weight: 600;
        }
      }
      .content-table {
        display: flex;
      }
    }
  }

  .detail-info {
    display: flex;
    border-radius: 12px;
    border: 1px solid #eee;
    padding: 12px;
    margin: 12px 0;
    overflow: auto;
    max-width: 1690px;

    .detail-info-left {
      padding-right: 12px;
      border-right: 1px dashed #eee;

      .info-title {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title-text {
          display: inline-block;
          font-size: 16px;
          font-weight: 600;
        }
      }

      .info-content {
        border: 0.5px solid #f5f7fa;

        .content-item {
          font-size: 13px;
          display: flex;
          min-height: 42px;
          border: 0.5px solid #f5f7fa;
          align-items: center;
          background: #c6e9fb;

          .info-content-left {
            width: 160px;
            height: 100%;
            min-height: 42px;
            padding: 0 8px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: #333;
          }

          .info-content-right {
            width: 300px;
            height: 100%;
            min-height: 42px;
            padding: 8px;
            display: inline-flex;
            align-items: center;
            text-align: center;
            color: #333;
            background: #fff;
          }
        }
      }
    }

    .detail-info-right {
      flex: 1;
      padding-left: 12px;

      .info-title {
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title-text {
          display: inline-block;
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
