<template>
  <div class="p-15">
    <el-row :gutter="10">
      <el-col :span="12" class="mb-15">
        <el-tooltip
          class="box-item"
          effect="dark"
          content="确认实收，结转上期余额，生成本期空账单"
          placement="bottom-start"
        >
          <el-button
            type="warning"
            plain
            class="noborder"
            :disabled="blockStatus"
            icon="Edit"
            @click="handleJieZhuan"
          >
            结转
          </el-button>
        </el-tooltip>
        <el-tooltip
          class="box-item"
          effect="dark"
          content="生成本期应收"
          placement="bottom-start"
        >
          <el-button
            type="warning"
            plain
            icon="Edit"
            :disabled="blockStatus"
            class="noborder"
            @click="handleSuoZhang"
          >
            锁账
          </el-button>
        </el-tooltip>
        <el-text v-if="false" type="danger" class="m-l-15">
          已锁账
        </el-text>
        <!-- <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="=="
                    placement="bottom-start"
                >
                    <el-button type="warning" plain @click="handleQingFen" class="noborder">清分</el-button>
                </el-tooltip> -->
      </el-col>
    </el-row>
    <el-table
      ref="tableContainer"
      v-loading="tableLoading"
      :data="tableData"
      style="width: 100%"
      :height="tableHeight"
    >
      <el-table-column prop="no" label="序号" min-width="50">
        <template #default="scope">
          <div style="text-wrap: nowrap">
            {{ scope.$index + 1 }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="billTime"
        label="账单期间"
        min-width="120"
      ></el-table-column>
      <el-table-column
        prop="carry"
        label="结转"
        min-width="120"
      ></el-table-column>
      <el-table-column
        prop="carryTime"
        label="结转时间"
        min-width="120"
      ></el-table-column>
      <el-table-column
        prop="block"
        label="锁账"
        min-width="120"
      ></el-table-column>
      <el-table-column
        prop="blockTime"
        label="锁账时间"
        min-width="120"
      ></el-table-column>
      <!-- <el-table-column prop="month" label="发送清分" min-width="120"></el-table-column>
            <el-table-column prop="month" label="清分发送时间" min-width="120"></el-table-column>
            <el-table-column prop="status" label="确认收入状态" min-width="120">
                <template #default="scope">
                    <el-tag v-if="scope.row.status === 1" type="success">已导入</el-tag>
                    <el-tag v-else type="danger">待导入</el-tag>
                </template>
            </el-table-column> -->
      <el-table-column
        prop="shopName"
        label="操作人"
        min-width="120"
      ></el-table-column>
    </el-table>
    <!-- 分页 右对齐 -->
    <el-pagination
      v-if="paginationInfo.totalCount > 0"
      class="flex flex-jus-end mt-15"
      background
      :current-page="paginationInfo.currentPage"
      :page-sizes="paginationInfo.pageSizes"
      :page-size="paginationInfo.pageSize"
      :layout="paginationInfo.layout"
      :total="paginationInfo.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
  </div>
</template>
<script setup name="EndOfTermTransfer">
// 期末结转
import {
  blockBill,
  blockBillDetail,
  carryBill,
  hasBlockBill,
  operateList
} from '@/api/managementFeeInvoice/endOfTermTransfer.js'
import { paginationInfoConstant } from '@/utils/constants.js'
import { ElMessage, ElMessageBox } from 'element-plus'

const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)

// 表格数据
const tableData = ref([])
const tableLoading = ref(false)

const nowMonth = ref('') // 当前月份 在结转提示中使用

// 锁账状态
const blockStatus = ref(false) // true表示锁账，false表示未锁账 默认锁账

// 锁账按钮
const handleSuoZhang = async (row) => {
  const res1 = await blockBillDetail()
  // canBlock true已经全部应收审核 false有未确定应收审核
  if (!res1.data.canBlock) {
    // 当门店【应收审核】存在未审核时，不允许锁账，弹窗
    ElMessageBox.alert(
      '有门店未确认【应收审核】，请审核全量门店后再确锁账。',
      '提示',
      {
        confirmButtonText: '确定',
        callback: (action) => {
          ElMessage({
            type: 'info',
            message: `action: ${action}`
          })
        }
      }
    )
    return false
  }

  ElMessageBox({
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    showCancelButton: true,
    type: 'warning',
    title: '锁账',
    message: h('p', null, [
      h(
        'div',
        null,
        `确认收入门店${res1.data.shopCount ?? '-'}家，本期应收金额如下`
      ),
      h('div', null, `管理费：${res1.data.manageFee ?? '-'}元`),
      h('div', null, `店长工资：${res1.data.dzSalary ?? '-'}元`),
      h('div', null, `管理费工资：${res1.data.yxSalary ?? '-'}元`),
      h('div', null, `合计：${res1.data.totalFee ?? '-'}元`)
    ]),
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = 'Loading...'
        // 锁账接口
        blockBill()
          .then((res) => {
            if (res.code == 200) {
              instance.confirmButtonLoading = false
              ElMessage.success(res.msg)
              blockStatus.value = true // 成功后更改状态
              done()
            } else {
              ElMessage.error(res.msg)
            }
            tableLoading.value = false // 加载状态
          })
          .catch((err) => {
            done()

            tableLoading.value = false // 加载状态
          })
      } else {
        done(false)
      }
    }
  })
}
// 结转按钮
const handleJieZhuan = (row) => {
  console.log('确认实收', row)
  let obj = {
    a: 123456,
    b: 456789
  }
  ElMessageBox({
    confirmButtonText: '开始结转',
    cancelButtonText: '取消',
    showCancelButton: true,
    type: 'warning',
    title: '结转说明',
    message: h('p', null, [
      h(
        'div',
        null,
        `一经结转，账期来到下一期间，当前账期（${nowMonth.value}）将无法修改；`
      ),
      h('div', null, '结转完成后生成下一期账单数据，前期余额为上期期末余额；')
    ]),
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = 'Loading...'
        // 结转接口
        carryBill()
          .then((res) => {
            if (res.code == 200) {
              ElMessage.success(res.msg)
              searchForm()
            } else {
              ElMessage.error(res.msg)
            }
            tableLoading.value = false // 加载状态
            done()
          })
          .catch((err) => {
            done()
          })
      } else {
        done()
      }
    }
  })
}
// 清分按钮
const handleQingFen = (row) => {
  console.log('清分', row)
  let obj = {
    a: 123456,
    b: 456789
  }
  ElMessageBox({
    confirmButtonText: '开始清分',
    cancelButtonText: '取消',
    showCancelButton: true,
    type: 'warning',
    title: '清分说明',
    message: h('p', null, [
      h('div', null, '一经清分，账期跳至下一期间，当前账期将无法修改；'),
      h('div', null, '结转完成后生成下一期账单数据，前期余额为上期期末余额；')
    ]),
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        instance.confirmButtonLoading = true
        instance.confirmButtonText = 'Loading...'
        setTimeout(() => {
          done()
          setTimeout(() => {
            instance.confirmButtonLoading = false
          }, 300)
        }, 1000)
      } else {
        done()
      }
    }
  })
    .then(() => {
      ElMessage.success('成功')
    })
    .catch(() => {})
}

// 分页信息
const paginationInfo = reactive({
  ...structuredClone(paginationInfoConstant)
})
// 每页条数改变
const handleSizeChange = (val) => {
  paginationInfo.pageSize = val
  searchForm()
}
// 当前页码改变
const handleCurrentChange = (val) => {
  paginationInfo.currentPage = val
  searchForm()
}

// 查询按钮
const searchForm = (id) => {
  // 如果是查询按钮，则重置当前页码和每页条数
  if (id == -1) {
    paginationInfo.currentPage = 1
  }
  tableLoading.value = true // 加载状态
  const params = {
    num: paginationInfo.currentPage, // 当前页码 number
    size: paginationInfo.pageSize // 每页条数 number
  }
  // 查询门店列表
  operateList(params)
    .then((res) => {
      tableLoading.value = false // 加载状态
      if (res.code == 200) {
        tableData.value = [] // 清空表格数据
        // 查询成功但是没有数据则提示暂无数据
        if (res.rows.length == 0) {
          ElMessage.warning('暂无数据')
          // return false;
        }
        tableData.value = res.rows // 表格数据
        nowMonth.value = res.rows[0]['billTime'] // 当前账期

        paginationInfo.totalCount = res.total // 总条数
      } else {
        ElMessage.error(res.msg)
      }
    })
    .catch((err) => {
      tableLoading.value = false // 加载状态
    })
}

const hasBlockBillFunc = () => {
  // 查看当月是否锁账接口
  hasBlockBill().then((res) => {
    if (res.code == 200) {
      // 是否锁账
      blockStatus.value = res.data // false 未锁账 true 已锁账
    } else {
      ElMessage.error(res.msg)
    }
  })
}
onMounted(() => {
  // 初始化查询
  searchForm(-1)
  hasBlockBillFunc()
})
</script>
<style scoped>
.m-l-15 {
  margin-left: 15px;
}
</style>
