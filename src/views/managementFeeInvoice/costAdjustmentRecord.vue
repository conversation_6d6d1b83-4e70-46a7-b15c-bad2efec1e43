<template>
  <div class="p-15">
    <el-form :model="queryForm" :inline="true" label-width="110px" @keydown.enter="searchForm(-1)">
      <el-form-item label="账单期间" prop="aTime">
        <el-date-picker
          v-model="queryForm.atime"
          type="month"
          placeholder="账单期间"
          style="width: 100%"
          value-format="YYYY-MM"
          :clearable="false"
          :disabled-date="disabledDate"
          class="w-240-important"
        />
      </el-form-item>
      <el-form-item label="门店" prop="storeName">
        <el-input
          v-model="queryForm.storeName"
          placeholder="请输入门店编号/门店名称"
          clearable
          :prefix-icon="Search"
          class="w-240"
        />
      </el-form-item>
      <el-form-item label="调整类型" prop="shopStatus">
        <el-select
          v-model="queryForm.shopStatus"
          placeholder="请选择调整类型"
          filterable
          class="w-240"
        >
          <el-option
            v-for="item in adjustmentTypeList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="searchForm(-1)"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetForm()"> 重置 </el-button>
      </el-form-item>
    </el-form>

    <el-table
      ref="tableContainer"
      v-loading="tableLoading"
      :data="tableData"
      class="mt-15"
      style="width: 100%"
      :height="tableHeight"
    >
      <el-table-column prop="no" label="序号" min-width="50">
        <template #default="scope">
          <div style="text-wrap: nowrap">
            {{ (paginationInfo.currentPage - 1) * paginationInfo.pageSize + scope.$index + 1 }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="atime" label="账单期间" min-width="100"></el-table-column>
      <el-table-column prop="dtime" label="调整时间" min-width="150">
        <template #default="scope">
          <el-text type="danger">
            {{ formatDateTime(scope.row.dtime) }}
          </el-text>
        </template>
      </el-table-column>
      <el-table-column prop="shopId" label="门店ID" min-width="100"></el-table-column>
      <el-table-column prop="shopName" label="门店名称" min-width="250"></el-table-column>
      <el-table-column prop="source" label="调整类型" min-width="150">
        <template #default="scope">
          <template v-for="item in adjustmentTypeList" :key="item.id">
            <template v-if="scope.row.source == item.value">
              <el-tag type="primary">
                {{ item.label }}
              </el-tag>
            </template>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="f02" label="商标使用费" min-width="130"></el-table-column>
      <el-table-column prop="f30" label="会员管理费" min-width="130"></el-table-column>
      <el-table-column prop="f29" label="输送客源费" min-width="130"></el-table-column>
      <el-table-column prop="f08" label="AI经营管理费" min-width="130"></el-table-column>
      <el-table-column prop="f18" label="市场营销费" min-width="130"></el-table-column>
      <el-table-column prop="f20" label="品质督导费" min-width="130"></el-table-column>
      <el-table-column prop="f21" label="运营顾问费" min-width="130"></el-table-column>
      <el-table-column prop="f07" label="运营质检费" min-width="130"></el-table-column>
      <el-table-column prop="f27" label="财务咨询费" min-width="130"></el-table-column>
      <el-table-column prop="f26" label="舆情系统使用费" min-width="150"></el-table-column>
      <el-table-column prop="f39" label="营销经理工资" min-width="150"></el-table-column>
      <el-table-column prop="f36" label="营销经理绩效工资" min-width="150"></el-table-column>
      <el-table-column prop="f38" label="营销经理季度调整" min-width="150"></el-table-column>
      <el-table-column prop="f19" label="店长工资" min-width="130"></el-table-column>
      <el-table-column prop="f16" label="店长绩效工资" min-width="130"></el-table-column>
      <el-table-column prop="f28" label="店长季度调整" min-width="130"></el-table-column>
      <el-table-column prop="memo" label="调整原因" min-width="200"></el-table-column>
      <el-table-column prop="inputStaff" label="操作人" min-width="130"></el-table-column>
      <el-table-column prop="fieldNo" label="流程编码" min-width="150"></el-table-column>
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="scope">
          <el-button type="danger" link icon="Delete" @click="del(scope.row)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 右对齐 -->
    <el-pagination
      v-if="paginationInfo.totalCount > 0"
      class="flex flex-jus-end mt-15"
      background
      :current-page="paginationInfo.currentPage"
      :page-sizes="paginationInfo.pageSizes"
      :page-size="paginationInfo.pageSize"
      :layout="paginationInfo.layout"
      :total="paginationInfo.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
  </div>
</template>
<script setup name="CostAdjustmentRecord">
// 费用调整记录
import { adjustmentTypeList, paginationInfoConstant } from '@/utils/constants.js'
import { Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 引入请求
import { deleteTz, getFeeAdjustRecord } from '@/api/managementFeeInvoice/search'
import { disabledDate, formatDateTime } from '@/utils/script.js'
const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)

// 搜索表单的引用格式
const nowDate = new Date()
const formInterface = {
  storeName: '', // 门店名称
  atime: `${nowDate.getFullYear()}-${nowDate.getMonth().toString().padStart(2, '0')}`, // 账单期间
  shopStatus: 1 // 门店状态
}

// 搜索条件
const queryForm = reactive(Object.assign({}, formInterface))

// 重置搜索表单
const resetForm = () => {
  // 遍历清空对象
  Object.assign(queryForm, formInterface) // 重置表单数据
}
// 查询门店列表参数封装
const getParams = () => {
  return {
    shopName: queryForm.storeName, // 门店名称 string
    atime: queryForm.atime.length > 0 ? `${queryForm.atime}-01` : '', // 账单期间 string
    source: queryForm.shopStatus // 门店状态 number
  }
}

// 查询按钮
const searchForm = (id) => {
  // 如果是查询按钮，则重置当前页码和每页条数
  if (id == -1) {
    paginationInfo.currentPage = 1
  }
  const params = {
    ...getParams(),
    pageNum: paginationInfo.currentPage, // 当前页码 number
    pageSize: paginationInfo.pageSize // 每页条数 number
  }

  // if(params.shopName.length <= 0){
  //     ElMessage.error("请输入门店编号/门店名称");
  //     return false;
  // }
  tableLoading.value = true // 加载状态
  // 查询列表
  getFeeAdjustRecord(params)
    .then((res) => {
      tableLoading.value = false // 加载状态
      // console.log("列表:",res);
      if (res.code == 200) {
        tableData.value = res.rows // 表格数据
        // paginationInfo.currentPage = res.pageNum; // 重置当前页码
        paginationInfo.totalCount = res.total // 总条数
      } else {
        ElMessage.error(res.msg)
      }
    })
    .catch((err) => {
      tableLoading.value = false // 加载状态
    })
}

onMounted(() => {
  // 默认查询一次
  searchForm(-1)
})

// 表格数据
const tableData = ref([])
const tableLoading = ref(false) // 表格加载状态

// 分页信息
const paginationInfo = reactive({
  ...structuredClone(paginationInfoConstant)
})
// 每页条数改变
const handleSizeChange = (val) => {
  paginationInfo.pageSize = val
  searchForm()
}
// 当前页码改变
const handleCurrentChange = (val) => {
  paginationInfo.currentPage = val
  searchForm()
}
// // 导出
// const handleExport = () => {
//     const params = {
//         ...getParams(),
//     }
//     // download("/bill/exportExcel",params,`门店账单列表_${new Date().getTime()}.xlsx`);
// }
// 删除
const del = (row) => {
  ElMessageBox.confirm('删除本次费用调整项', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      console.log('删除:', row, { pid: row.pid })
      deleteTz({ pid: row.pid })
        .then((res) => {
          if (res.code == 200) {
            // 删除成功
            ElMessage({
              type: 'success',
              message: res.msg
            })
            // 重新查询
            searchForm(-1)
          }
        })
        .catch((err) => {
          console.log('删除失败:', err)
        })
    })
    .catch(() => {
      console.log('取消删除')
    })
}
</script>
<style scoped></style>
