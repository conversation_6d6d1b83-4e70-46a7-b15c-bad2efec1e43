<template>
  <el-dialog v-model="show" title="退款调整" width="600px" append-to-body>
    <el-alert title="金额不允许超过该费用项的期末余额" type="warning" show-icon :closable="false" style="width: 480px; margin-bottom: 12px" />
    
    <div v-for="(item, index) in refundList" :key="item.id" class="refund-item">
      <div class="btn-group">
        <el-button type="primary" :icon="Plus" circle plain @click="handleAdd()" />
        <el-button
          v-if="index" type="danger" :icon="Delete" plain circle
          @click="handleDel(index)"
        />
      </div>
      <div class="item-line">
        <span class="item-lable">退款费用项</span>
        <el-select
          v-model="item.feeCode"
          placeholder="请选择费用项"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="fee in data"
            :key="fee.feeCode"
            :label="fee.feeTitle"
            :value="fee.feeCode"
            :disabled="getDisbaled(fee.feeCode, index)"
          />
        </el-select>
      </div>
      <div class="item-line">
        <span class="item-lable">退款金额</span>
        <el-input
          v-model="item.refund"
          placeholder="请输入金额"
          style="width: 240px"
          @blur="handleChange(item)"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleComfirm">
          确 定
        </el-button>
        <el-button @click="handleCancle">
          取 消
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { Delete, Plus } from '@element-plus/icons-vue'
import { confrimAdjust } from '@/api/managementFeeInvoice/costAdjustmentList'

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },

  data: {
    type: Array,
    default: () => [] 
  },

  params: {
    type: Object,
    default: () => {}
  }
})

const { proxy } = getCurrentInstance()
const show = computed({
  get () {
    return props.open
  },
  set () {
    emit('update:open', false)
  }
})

const emit = defineEmits(['update:show', 'updateList'])

const refundList = ref([
  {
    id: new Date().getTime(),
    feeCode: '',
    refund: ''
  }
])
const handleAdd = () => {
  refundList.value.push({
    id: new Date().getTime(),
    feeCode: '',
    refund: ''
  })
}
const handleDel = (index) => {
  refundList.value.splice(index, 1)
}
const getDisbaled = (feeCode, index) => {
  const arr = refundList.value.filter((data, i) => i !== index).map((item) => item.feeCode)
  return arr.some((item) => item.includes(feeCode))
}

const handleChange = (row) => {
  if (!row.feeCode) {
    proxy.$modal.msgError('请先选择退款费用项')
    row.refund = undefined
    return
  }
  if (!/^-?\d+(\.\d+)?$/.test(row.refund)) {
    row.refund = undefined
  }
  if (row.refund) {
    row.refund = parseInt(row.refund * 100) / 100
  }
  const item = props.data.find((item) => item.feeCode === row.feeCode)
  if (row.refund > parseFloat(item.endBalance)) {
    proxy.$modal.msgError('退款金额最大' + parseFloat(item.endBalance) + '元')
    row.refund = parseFloat(item.endBalance)
  }
}

const handleComfirm = () => {
  let adjustAmount = 0
  let confirmText = ''
  refundList.value.forEach((item) => {
    adjustAmount += parseFloat(item.refund)
    confirmText += `${ props.data.find((data) => data.feeCode === item.feeCode).feeTitle }: ${ item.refund }元; `
  })
  confirmText = `确认此次退款操作，累计退款${ adjustAmount }元。\n` + confirmText

  // TODO: 退款提交
  proxy.$modal.confirm(confirmText, '退款确认').then(function () {
    confrimAdjust({id: props.detail.id}).then(() => {
      proxy.$modal.msgSuccess('退款成功！')
      show.value = false
      emit('updateList')
    })
  }).catch(() => {})
}

const handleCancle = () => {
  show.value = false
  refundList.value = [
    {
      id: new Date().getTime(),
      feeCode: '',
      refund: ''
    }
  ]
}
</script>

<style lang="scss" scoped>
.dialog-title {
  font-size: 16px;
  font-weight: bold;
  margin: 15px 0 10px 0;
}

.refund-item {
  position: relative;
  background: #eee;
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 12px;

  .btn-group {
    position: absolute;
    right: 12px;
    top: 12px;
    padding-left: 12px;
          
    .el-button {
      width: 24px;
      height: 24px;
    }
  }

  .item-line {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .item-lable {
      width: 100px;
      text-align: right;
      margin-right: 12px;
    }
  }
}

:deep(.el-message-box__message) {
  color: red;
  p {
    word-break: normal;
    word-wrap: break-word;
    white-space: pre-wrap;
  }
}
</style>

<style lang="scss">
.el-message-box__message {
  p {
    word-break: normal;
    word-wrap: break-word;
    white-space: pre-wrap;
  }
}
</style>
