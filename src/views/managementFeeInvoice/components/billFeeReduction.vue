<template>
  <el-dialog v-model="show" title="费用调整" width="730px" append-to-body>
    <el-alert title="调减负数，调增正数" type="warning" show-icon :closable="false" style="width: 680px; margin-bottom: 12px" />
    <el-table :data="detailData" style="width: 680px" :span-method="objectSpanMethod" :header-cell-style="cells" border>
      <el-table-column label="序号" type="index" width="80" align="left" />
      <el-table-column label="费用项" align="left" prop="feeTitle" width="120" />
      <el-table-column label="子费用项" align="left" prop="subName" width="120" />
      <el-table-column label="调整前" align="left" prop="endBalance" width="120" />
      <el-table-column label="费用调整" align="left" prop="adjustAmount" width="120">
        <template #default="scope">
          <el-input
            v-model="scope.row.adjustAmount"
            placeholder="调整费用"
            clearable
            style="width: 100%"
            @blur="handleChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="调整后" align="left" prop="adjustAfter" width="120" />
    </el-table>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleComfirm">
          确 定
        </el-button>
        <el-button @click="handleCancle">
          取 消
        </el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog v-model="confirmShow" title="费用调整确认" width="400px" append-to-body>
    <p class="info">
      确认本次费用调整，调整金额合计{{ adjustParams.total }}元。
    </p>
    <el-input
      v-model="adjustParams.memo"
      :rows="4"
      type="textarea"
      placeholder="请输入调整原因"
    />

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleConfirmSubmit">
          确 定
        </el-button>
        <el-button @click="handleConfirmCancle">
          取 消
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { updateAdjustCharge } from '@/api/managementFeeInvoice/storeBillingDetails'
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },

  title: {
    type: String,
    default: ''
  },

  data: {
    type: Array,
    default: () => [] 
  },

  params: {
    type: Object,
    default: () => {}
  }
})

const { proxy } = getCurrentInstance()
const show = computed({
  get () {
    return props.open
  },
  set () {
    emit('update:open', false)
  }
})

const emit = defineEmits(['update:show'])
const detailData = ref([]) // 调整明细
const handleData = () => { 
  detailData.value = JSON.parse(JSON.stringify(props.data))
  for(let i = 0; i < detailData.value.length; i++) {
    const item = detailData.value[i]
    if (item.subFee?.length) {
      detailData.value.splice(i, 1, ...item.subFee)
      i += item.subFee.length - 1
    }
  }
}
watch(() => props.data, () => {
  handleData()
}, {immediate: true, deep: true})

const handleChange = (row) => {
  if (!/^-?\d+(\.\d+)?$/.test(row.adjustAmount)) {
    row.adjustAmount = 0
  }
  if (row.adjustAmount) {
    row.adjustAmount = parseInt(row.adjustAmount * 100) / 100
  } 
  row.adjustAfter = parseFloat(row.endBalance) + parseFloat(row.adjustAmount)
}

const confirmShow = ref(false)
const adjustParams = ref({
  ...props.params,
  memo: '',
  feeDetail: {},
  total: 0
})

const handleConfirmSubmit = () => {
  if (!adjustParams.value.memo) {
    proxy.$modal.msgError('请输入调整原因')
    return
  }
  updateAdjustCharge(adjustParams.value).then(() => {
    proxy.$modal.msgSuccess('提交成功')
    handleConfirmCancle()
    handleCancle()
  })
}

const handleConfirmCancle = () => {
  confirmShow.value = false
  adjustParams.value.memo = ''
}

const handleComfirm = () => {
  confirmShow.value = true
  detailData.value.forEach((item) => {
    if (Number(item.adjustAmount)) {
      adjustParams.value.feeDetail[item.feeCode] = item.adjustAmount
      adjustParams.value.total += Number(item.adjustAmount)
    }
  })
  adjustParams.value.total = adjustParams.value.total.toFixed(2)
}

const handleCancle = () => {
  show.value = false
}

const cells = ({row, columnIndex}) => {
  if (row[1].level === 1) {
    row[1].colSpan = 2
    row[2].colSpan = 0
    if (columnIndex === 2) {
      return { display: 'none' }
    }
  }
}

const objectSpanMethod = ({
  row,
  columnIndex
}) => {
  if (!['f36', 'f38', 'f39', 'f16', 'f19', 'f28'].includes(row.feeCode)) {
    if (columnIndex === 1) {
      return [1, 2]
    } else if (columnIndex === 2) {
      return [0, 0]
    }
  } 
  else {
    row.subName = row.feeTitle
    if (columnIndex === 1) {
      if (['f39', 'f19'].includes(row.feeCode)) {
        row.feeTitle = row.feeCode === 'f19' ? '店长工资' : '营销经理工资'
        return {
          rowspan: 3,
          colspan: 1
        }
      } else {
        return {
          rowspan: 0,
          colspan: 0
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-title {
  font-size: 16px;
  font-weight: bold;
  margin: 15px 0 10px 0;
}
</style>
