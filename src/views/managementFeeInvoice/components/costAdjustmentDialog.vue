<template>
  <el-dialog v-model="show" :title="title" width="800px" append-to-body>
    <div class="dialog-info">
      <p class="info-item">
        账单期间：{{ detail.atime }}
      </p>
      <p class="info-item">
        {{ detail.shopId }}-{{ detail.shopName }}
      </p>
    </div>

    <p class="dialog-title">
      调整明细项
    </p>
    <el-alert title="调减负数，调增正数" type="warning" show-icon :closable="false" style="width: 680px; margin-bottom: 12px" />
    <el-table :data="detailData" style="width: 680px" :span-method="objectSpanMethod" :header-cell-style="cells" border>
      <el-table-column label="序号" prop="index" width="80" align="left" />
      <el-table-column label="费用项" align="left" prop="name" width="120" />
      <el-table-column label="子费用项" align="left" prop="subName" width="120" />
      <el-table-column label="调整前" align="left" prop="adjustBefore" width="120" />
      <el-table-column label="费用调整" align="left" prop="adjustAmount" width="120" />
      <el-table-column label="调整后" align="left" prop="adjustAfter" width="120" />
    </el-table>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleComfirm">
          确 定
        </el-button>
        <el-button @click="handleCancle">
          取 消
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { getAdjustOaDetail, confrimAdjust } from '@/api/managementFeeInvoice/costAdjustmentList'
import { watch } from 'vue'

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },

  title: {
    type: String,
    default: ''
  },

  detail: {
    type: Object,
    default: () => {}
  }
})

const { proxy } = getCurrentInstance()
const show = computed({
  get () {
    return props.open
  },
  set () {
    emit('update:open', false)
  }
})

const emit = defineEmits(['update:show', 'updateList'])

const detailData = ref([]) // 调整明细
const getDetail = () => { 
  getAdjustOaDetail({ id: props.detail.id }).then((res) => {
    if (!res) return
    detailData.value = res.data
  })
}

const handleComfirm = () => {
  let adjustAmount = 0
  detailData.value.forEach((item) => {
    adjustAmount += item.adjustAmount
  })
  proxy.$modal.confirm(`确认本次费用调整，调整金额${ adjustAmount }元。`, '费用调整确认').then(function () {
    confrimAdjust({id: props.detail.id}).then(() => {
      proxy.$modal.msgSuccess('费用调整成功！')
      show.value = false
      emit('updateList')
    })
  }).catch(() => {})
}

const handleCancle = () => {
  show.value = false
}

watch(() => props.detail.id, () => {
  getDetail()
})

const cells = ({row, columnIndex}) => {
  if (row[1].level === 1) {
    row[1].colSpan = 2
    row[2].colSpan = 0
    if (columnIndex === 2) {
      return { display: 'none' }
    }
  }
}
const objectSpanMethod = ({
  row,
  rowIndex,
  columnIndex
}) => {
  if (columnIndex === 0) {
    row.index = rowIndex + 1
  }
  if (!['店长绩效工资', '店长工资', '店长工资季度调整', '营销经理绩效工资', '营销经理季度调整', '营销经理工资'].includes(row.name)) {
    if (columnIndex === 1) {
      return [1, 2]
    } else if (columnIndex === 2) {
      return [0, 0]
    }
  } 
  else {
    row.subName = row.name
    if (columnIndex === 1) {
      if (['营销经理工资', '店长工资'].includes(row.name)) {
        return {
          rowspan: 3,
          colspan: 1
        }
      } else {
        return {
          rowspan: 0,
          colspan: 0
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-title {
  font-size: 16px;
  font-weight: bold;
  margin: 15px 0 10px 0;
}

.dialog-info {
  display: flex;
  align-items: center;

  .info-item:last-child {
    margin-left: 100px;
  }
}
</style>
