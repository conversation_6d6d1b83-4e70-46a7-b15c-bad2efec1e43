<template>
  <div class="p-15">
    <el-form ref="queryRef" :inline="true" label-width="110px">
      <el-form-item label="账单期间" prop="aTime">
        <el-date-picker
          v-model="value1"
          type="month"
          placeholder="请选择账单期间"
          value-format="YYYY-MM"
          :disabled-date="disabledDate"
          class="w-240-important"
          @change="dateChange"
        />
      </el-form-item>
    </el-form>
    <el-table 
      ref="tableContainer" 
      v-loading="tableLoading" 
      :data="tableData"
      style="width: 100%"
      :height="tableHeight"
    >
      <el-table-column prop="no" label="序号" width="50">
        <template #default="scope">
          <div style="text-wrap: nowrap;">
            {{ scope.$index + 1 }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="atime" label="账单期间"></el-table-column>
      <el-table-column prop="fileTypeName" label="分类"></el-table-column>
      <el-table-column prop="updateTime" label="导入时间">
        <template #default="scope">
          <el-text type="primary">
            {{ scope.row.updateTime }}
          </el-text>
        </template>
      </el-table-column>
      <el-table-column prop="updateBy" label="操作人"></el-table-column>
      <el-table-column prop="importCount" label="导入条数"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template #default="scope">
          <el-tag 
            v-if="scope.row.status === 1"
            type="success" 
            size="small"
          >
            成功
          </el-tag>
          <el-tag 
            v-else
            type="info" 
            size="small"
          >
            待导入
          </el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="createBy" label="创建人"></el-table-column> -->
      <!-- <el-table-column prop="createTime" label="创建时间"></el-table-column> -->
           
      <el-table-column prop="shopName" label="操作" width="180" fixed="right" align="center">
        <template #default="scope">
          <!-- 锁账后导入按钮消失 -->
          <el-button 
            v-if="!blockStatus" 
            type="primary"
            link
            icon="Upload"
            @click="handleImport(scope.row)"
          >
            导入
          </el-button>
          <el-tooltip
            v-else
            class="box-item"
            effect="dark"
            content="锁账后不允许导入"
            placement="bottom"
          >
            <el-button 
              type="primary" 
              link
              icon="Upload"
              :disabled="blockStatus"
            >
              导入
            </el-button>
          </el-tooltip>
          <!-- 导入数据后允许下载 -->
          <el-button 
            v-if="scope.row.status === 1" 
            type="primary"
            link
            icon="Download"
            @click="downloadFunc(scope.row)"
          >
            下载
          </el-button>
          <el-tooltip
            v-else
            class="box-item"
            effect="dark"
            content="导入前不允许下载"
            placement="bottom"
          >
            <el-button 
              type="primary"
              link
              icon="Download"
              :disabled="scope.row.status !== 1"
            >
              下载
            </el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!-- 导入弹窗 -->
    <ImportBillingBase />
  </div>
</template>
<script setup name="ImportBillingBase">  
// 计费基数导入
import { hasBlockBill } from '@/api/managementFeeInvoice/endOfTermTransfer.js'
import { queryList } from '@/api/managementFeeInvoice/importBillingBase.js'
import { download } from '@/utils/request'
import { ElMessage } from 'element-plus'
import { storeToRefs } from 'pinia'
// 弹窗组件
import ImportBillingBase from '@/components/managementFeeInvoice/ImportBillingBase'
import useManagementFeeInvoiceStore from '@/store/modules/ManagementFeeInvoice'
import { disabledDate } from '@/utils/script.js'
import { onMounted } from 'vue'
const store = useManagementFeeInvoiceStore()
const { type } = storeToRefs(store)

const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)
// 默认上个月的月份 并格式化为 YYYY-MM
const date = new Date()
const year = date.getFullYear() // 当前年份
const month = date.getMonth() // 上个月
const value1 = ref()
value1.value = `${year}-${month.toString().padStart(2, '0')}`

// 是否锁账
const blockStatus = ref(true) // 默认锁账 true
// 表格数据
const tableData = ref([])
const tableLoading = ref(false)

const queryListFunc = () => {
        
  tableLoading.value = true // 加载状态
  // 查询列表
  queryList(value1.value+'-01').then((res) => {
    tableLoading.value = false // 加载状态
    if(res.code == 200){
      tableData.value = res.data // 表格数据
    }else{
      ElMessage.error(res.msg)
    }
  }).catch((err) => {
    tableLoading.value = false // 加载状态
    ElMessage.error(err)
  })
}

// 下载
const downloadFunc = (row) => {
  // console.log('下载',row.atime,row.fileType)
  const url = `/shop/baseImport/export?atime=${row.atime}&type=${row.fileType}`
  download(url, {}, `${row.fileTypeName}_${new Date().getTime()}.xls`)
}
   
const dateChange = (val) => {
  console.log('选择的月份', val)
  // 如果选择的月份不等于上个月 则提示
  if(val.substring(0, 7) != value1.value){
    // 其他日期皆锁账
    blockStatus.value = true
  }else{
    // 如果等于上个月则查询是否锁账
    hasBlockBillFunc()
  }
  queryListFunc()
}

// 导入
const handleImport = (row) => {
  console.log('导入', row)
  store.typeName = row.type
  store.typeId = row.fileType
  store.atime = row.atime
  store.type = true
}

const hasBlockBillFunc = () => {
  // 查看当月是否锁账接口
  hasBlockBill().then((res) => {
    if(res.code == 200){
      // 是否锁账
      blockStatus.value = res.data
    }else{
      ElMessage.error(res.msg)
    }
  }).catch((err) => {
    ElMessage.error(err)
  })
}

onMounted(() => {
  queryListFunc()
  hasBlockBillFunc()
})
// 监听弹窗状态，重置数据
watch(type, (newValue) => {
  if(!newValue){
    queryListFunc()
  }
})

</script>