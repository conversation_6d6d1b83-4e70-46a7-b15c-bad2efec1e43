<template>
  <div class="app-container">
    <el-form ref="queryRef" :model="queryParams" :inline="true" label-width="110px">
      <el-form-item label="归档日期">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          clearable
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="账单期间" prop="atime">
        <el-date-picker
          v-model="queryParams.atime"
          type="month"
          :clearable="false"
          placeholder="请选择账单期间"
          style="width: 240px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="门店名称" prop="shopName">
        <el-input
          v-model="queryParams.shopName"
          placeholder="请输入门店编码/门店名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="调整状态" prop="adjustStatus">
        <el-select
          v-model="queryParams.adjustStatus"
          clearable
          placeholder="请选择调整状态"
          style="width: 240px"
        >
          <el-option label="待确认" :value="0" />
          <el-option label="已调整" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="flex">
      <el-col style="flex: 1"> </el-col>
      <right-toolbar :search="false" :show-refresh="false" :columns="columns"></right-toolbar>
    </el-row>

    <el-table
      ref="tableContainer"
      v-loading="loading"
      class="mt-15"
      :height="tableHeight"
      :data="budgetChangeList"
    >
      <el-table-column type="index" label="序号" width="50" align="left" />
      <template v-for="item in columns">
        <el-table-column
          v-if="item.visible"
          :key="item.prop"
          :label="item.label"
          :align="item.align || 'left'"
          :prop="item.prop"
          :width="item.width"
        >
          <template v-if="item.prop === 'archiveTime'" #default="scope">
            {{ scope.row.archiveTime?.slice(0, 10) }}
          </template>
          <template v-else-if="item.prop === 'atime'" #default="scope">
            {{ scope.row.atime?.slice(0, 7) }}
          </template>
          <template v-else-if="item.prop === 'adjustStatus'" #default="scope">
            {{ scope.row.adjustStatus == 0 ? '待确认' : '已调整' }}
          </template>
          <template v-else #default="scope">
            {{ scope.row[item.prop] }}
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" align="center" width="120">
        <template #default="scope">
          <el-button
            v-if="Number(scope.row.adjustStatus) === 0"
            link
            type="primary"
            icon="Edit"
            @click="handleEdit(scope.row)"
          >
            调整
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <CostAdjustmentDialog
      v-model:open="dialogShow"
      :title="dialogTitle"
      :detail="detail"
      @update-list="handleQuery"
    ></CostAdjustmentDialog>
  </div>
</template>

<script setup name="CostAdjustmentList">
import { getFeeAdjustOaList } from '@/api/managementFeeInvoice/costAdjustmentList'
import { formatSimpleDate, getFirstDayOfMonth } from '@/utils'
import CostAdjustmentDialog from './components/costAdjustmentDialog.vue'

const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)
// 列信息
const columns = ref([
  { key: 0, label: `归档日期`, visible: true, prop: 'archiveTime', width: '160' },
  { key: 1, label: `流程编码`, visible: true, prop: 'flowCode', width: '160' },
  { key: 2, label: `门店ID`, visible: true, prop: 'shopId', width: '160' },
  { key: 3, label: `门店名称`, visible: true, prop: 'shopName', width: '160' },
  { key: 4, label: `账单期间`, visible: true, prop: 'atime', width: '160' },
  { key: 5, label: `商标使用费`, visible: true, prop: 'f02', width: '160' },
  { key: 6, label: `会员管理费`, visible: true, prop: 'f04', width: '160' },
  { key: 7, label: `输送客源费`, visible: true, prop: 'f06', width: '160' },
  { key: 8, label: `AI经营管理费`, visible: true, prop: 'f08', width: '160' },
  { key: 9, label: `市场营销费`, visible: true, prop: 'f18', width: '160' },
  { key: 10, label: `品质督导费`, visible: true, prop: 'f20', width: '160' },
  { key: 11, label: `运营顾问费`, visible: true, prop: 'f21', width: '160' },
  { key: 12, label: `运营质检费`, visible: true, prop: 'f07', width: '160' },
  { key: 13, label: `财务咨询费`, visible: true, prop: 'f27', width: '160' },
  { key: 14, label: `舆情系统使用费`, visible: true, prop: 'f26', width: '160' },
  { key: 15, label: `营销经理工资`, visible: true, prop: 'f39', width: '160' },
  { key: 16, label: `营销经理绩效工资`, visible: true, prop: 'f36', width: '160' },
  { key: 17, label: `营销经理季度调整`, visible: true, prop: 'f38', width: '160' },
  { key: 18, label: `店长工资`, visible: true, prop: 'f19', width: '160' },
  { key: 19, label: `店长绩效工资`, visible: true, prop: 'f16', width: '160' },
  { key: 20, label: `店长工资季度调整`, visible: true, prop: 'f28', width: '160' },
  // { key: 21, label: `情况说明`, visible: true, prop: '', width: '160' },
  { key: 21, label: `调整状态`, visible: true, prop: 'adjustStatus', width: '160' },
  { key: 22, label: `操作人`, visible: true, prop: 'operator', width: '160' },
  { key: 23, label: `操作时间`, visible: true, prop: 'updateTime', width: '160' }
])

const { proxy } = getCurrentInstance()
const dateRange = ref([])
const budgetChangeList = ref([])
const loading = ref(false)
const total = ref(0)

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    shopName: undefined,
    atime: getFirstDayOfMonth(),
    adjustStatus: undefined,
    archiveTimeStart: undefined,
    archiveTimeEnd: undefined
  }
})

const { queryParams } = toRefs(data)

/** 查询用户列表 */
const getList = () => {
  loading.value = true
  const params = {
    ...queryParams.value,
    archiveTimeStart: dateRange.value?.[0] || undefined,
    archiveTimeEnd: dateRange.value?.[1] || undefined
  }
  params.atime = formatSimpleDate(params.atime, true)

  getFeeAdjustOaList(params)
    .then((res) => {
      budgetChangeList.value = res.rows
      total.value = res.total
    })
    .finally(() => {
      loading.value = false
    })
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = []
  proxy.resetForm('queryRef')
  handleQuery()
}

const dialogShow = ref(false)
const dialogTitle = ref('')
const detail = ref('')
const handleEdit = (row) => {
  dialogShow.value = true
  dialogTitle.value = '费用调整'
  detail.value = {
    id: row.id,
    shopId: row.shopId,
    shopName: row.shopName,
    atime: row.atime?.slice(0, 7)
  }
}

getList()
</script>
<style scoped></style>
