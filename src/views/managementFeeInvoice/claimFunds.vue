<template>
  <div class="app-container fee-manager">
    <el-form :model="queryForm" :inline="true" label-width="110px" @keydown.enter="searchForm(-1)">
      <el-form-item label="认领状态" prop="claimStatus">
        <el-select
          v-model="queryForm.claimStatus"
          placeholder="请选择认领状态"
          clearable
          class="w-240"
        >
          <el-option
            v-for="item in claimStatusList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="起止日期" prop="dateRange">
        <el-date-picker
          v-model="queryForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          style="width: 240px !important"
        />
      </el-form-item>
      <el-form-item label="地区总部" prop="divisionId">
        <el-select
          v-model="queryForm.divisionId"
          placeholder="请选择地区总部"
          clearable
          filterable
          class="w-240"
          @change="handleHeadquarterChange"
        >
          <el-option
            v-for="item in queryFormList.shopHeadOfficeList"
            :key="item.freeString"
            :label="item.freeString"
            :value="item.freeString"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="大区" prop="shopDq">
        <el-select
          v-model="queryForm.shopDq"
          placeholder="请选择大区"
          clearable
          filterable
          class="w-240"
          @change="handleDqChange"
        >
          <el-option
            v-for="item in queryFormList.shopDqList"
            :key="item.eareId"
            :label="item.eareName"
            :value="item.eareId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="门店" prop="storeName">
        <el-input
          v-model="queryForm.storeName"
          placeholder="请输入门店编号/门店名称"
          clearable
          class="w-240"
          :prefix-icon="Search"
        />
      </el-form-item>
      <el-form-item label="打款人" prop="payerInfo">
        <el-input
          v-model="queryForm.payerInfo"
          placeholder="请输入打款人"
          clearable
          class="w-240"
        />
      </el-form-item>
      <el-form-item label="入账银行" prop="inBank">
        <el-select
          v-model="queryForm.inBank"
          placeholder="请选择入账银行"
          clearable
          filterable
          class="w-240"
        >
          <el-option
            v-for="item in bankList"
            :key="item.TYPECODE"
            :label="item.TYPENAME"
            :value="item.TYPECODE"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="金额" prop="claimAmount">
        <el-input
          v-model="queryForm.claimAmount"
          placeholder="请输入金额"
          clearable
          class="w-240"
          @input="(value) => handleAmountInput(value, queryForm, 'claimAmount')"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="searchForm(-1)">搜索</el-button>
        <el-button icon="Refresh" @click="resetForm()">重置</el-button>
      </el-form-item>
    </el-form>

    <el-alert show-icon type="warning" :closable="false">
      <template #title>
        <div>
          误认款项请先操作
          <span style="color: red">【反认领】</span>
          ，再告知
          <span style="color: red">SRM</span>
          反认领，两个系统都操作完成后，才允许对本条流水重新操作。
        </div>
      </template>
    </el-alert>
    <el-table
      ref="tableContainer"
      v-loading="tableLoading"
      :data="tableData"
      class="mt-15"
      style="width: 100%"
      :height="tableHeight"
    >
      <el-table-column prop="no" label="序号" min-width="50">
        <template #default="scope">
          <div style="text-wrap: nowrap">
            {{ (paginationInfo.currentPage - 1) * paginationInfo.pageSize + scope.$index + 1 }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="id" label="交易流水号" min-width="300"></el-table-column>
      <el-table-column prop="transactionDate" label="交易日期" min-width="200"></el-table-column>
      <el-table-column prop="shopId" label="门店ID" min-width="150"></el-table-column>
      <el-table-column prop="shopName" label="门店名称（自匹配）" min-width="250"></el-table-column>
      <el-table-column prop="accountName" label="打款人" min-width="250"></el-table-column>
      <el-table-column prop="isSame" label="打款一致性" min-width="150">
        <template #default="scope">
          <el-tag v-if="scope.row.isSame == '1'" type="success" size="small">是</el-tag>
          <el-tag v-else type="danger" size="small">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="accountNum" label="打款账号" min-width="250"></el-table-column>
      <!-- <el-table-column prop="type" label="费用类型" min-width="150">
        <template #default="scope">
          <el-text>{{ scope.row.payee }}</el-text>
        </template>
      </el-table-column> -->
      <el-table-column prop="sumMoney" label="金额" min-width="150"></el-table-column>
      <el-table-column prop="noMoney" label="剩余金额" min-width="150"></el-table-column>
      <el-table-column prop="remoark" label="打款说明" min-width="150"></el-table-column>
      <el-table-column prop="bankName" label="入账银行" min-width="300"></el-table-column>
      <el-table-column prop="bankNum" label="入账账号" min-width="250"></el-table-column>
      <el-table-column prop="createName" label="认领人" min-width="150"></el-table-column>
      <el-table-column prop="isClaim" label="认领状态" min-width="150">
        <template #default="scope">
          <el-tag v-if="scope.row.isClaim == 'Y'" type="success" size="small">已认领</el-tag>
          <el-tag v-else type="danger" size="small">待认领</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createDate" label="认领时间" min-width="200"></el-table-column>
      <el-table-column prop="divisionName" label="地区总部" min-width="200"></el-table-column>
      <el-table-column prop="regionName" label="大区" min-width="200"></el-table-column>
      <el-table-column prop="eareName" label="城区" min-width="200"></el-table-column>
      <el-table-column prop="operation" label="操作" min-width="190" fixed="right">
        <template #default="scope">
          <el-button type="success" link class="noborder" @click="handleRemark(scope.row)">
            流水备注
          </el-button>
          <el-button
            type="primary"
            :disabled="scope.row.noMoney == 0"
            link
            class="noborder"
            @click="handleClaimShow(scope.row)"
          >
            认领
          </el-button>
          <el-button
            type="warning"
            :disabled="scope.row.isClaim != 'Y'"
            link
            class="noborder"
            @click="handleClaim2Show(scope.row)"
          >
            反认领
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 右对齐 -->
    <el-pagination
      v-if="paginationInfo.totalCount > 0"
      class="flex flex-jus-end mt-15"
      background
      :current-page="paginationInfo.currentPage"
      :page-sizes="paginationInfo.pageSizes"
      :page-size="paginationInfo.pageSize"
      :layout="paginationInfo.layout"
      :total="paginationInfo.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>

    <!-- 款项认领弹窗 -->
    <claimDialog
      v-if="dialogFormVisible"
      v-model="dialogFormVisible"
      :data="dialogDatas"
      @close="claimDialogClose"
    ></claimDialog>

    <el-dialog v-model="remarkVisible" title="流水备注" width="500" @closed="handleRemarkClose">
      <el-input
        v-model="chooseItem.remark"
        :rows="4"
        type="textarea"
        placeholder="请输入流水备注"
        show-word-limit
        :maxlength="100"
      />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleRemarkClose">取消</el-button>
          <el-button type="primary" @click="handleRemarkConfirm">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
// 款项认领
import claimDialog from '@/components/managementFeeInvoice/claimDialog.vue'
import router from '@/router'
import { Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 引入请求
import {
  getShopCqList, // 查询门店城区列表
  // 查询门店地区总部列表
  getShopDqList,
  getShopHeadOfficeList
} from '@/api/chargingManager/search'
import {
  getBankInfo,
  getPageList, // 查询银行列表
  unClaimOrder
} from '@/api/managementFeeInvoice/claimFunds.js'

import { claimStatusList, paginationInfoConstant } from '@/utils/constants.js'
import { onMounted } from 'vue'
// 获取门店列表方法
import { getSearchShopListFunc } from '@/utils/getSearchShopListFunc'
getSearchShopListFunc()
// 表格自动高度
import { formatDate, handleAmountInput } from '@/utils/script.js'

const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)

// 搜索表单的引用格式
const formInterface = {
  storeName: '', // 门店名称
  divisionId: '', // 总部
  shopDq: '', // 大区
  shopCq: '', // 城市
  dateRange: '', // 时间范围
  claimStatus: '', // 认领状态
  claimAmount: '', // 认领金额
  payerInfo: '', // 打款人
  inBank: '', // 入账银行
  amount: '' // 金额
}

// 搜索条件
const queryForm = reactive(Object.assign({}, formInterface))
// 搜索下拉列表选项
const queryFormList = reactive({
  shopHeadOfficeList: [], // 地区总部
  shopDqList: [], // 大区
  shopCqList: [] // 城区
})
// 重置搜索表单
const resetForm = () => {
  // 遍历清空对象
  Object.assign(queryForm, formInterface) // 重置表单数据
  // 清空大区和城区列表
  queryFormList.shopDqList = []
  queryFormList.shopCqList = []
}
// 银行列表查询
const bankList = ref([])
onMounted(() => {
  // 地区总部列表查询
  getShopHeadOfficeList().then((res) => {
    if (res.code === 200) {
      queryFormList.shopHeadOfficeList = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
  // 银行列表查询
  getBankInfo().then((res) => {
    // console.log("银行列表", res);
    if (res.code === 200) {
      bankList.value = res.data
    }
  })

  searchForm(-1) // 默认查询第一页数据
})

// 切换地区总部
const handleHeadquarterChange = (val) => {
  queryForm.shopCq = '' // 清空城区值
  queryFormList.shopCqList = [] // 清空城区列表
  queryForm.shopDq = '' // 清空大区值
  queryFormList.shopDqList = [] // 清空大区列表
  if (val && val.length > 0) {
    // 查询大区列表
    getShopDqList({ freeString: val }).then((res) => {
      console.log('大区列表', res)
      if (res.code === 200) {
        queryFormList.shopDqList = res.data
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}
// 切换大区
const handleDqChange = (val) => {
  queryForm.shopCq = '' // 清空城区值
  queryFormList.shopCqList = [] // 清空城区列表
  if (val && val.length > 0) {
    // 查询大区列表
    getShopCqList({ eareId: val }).then((res) => {
      if (res.code === 200) {
        queryFormList.shopCqList = res.data
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}
// 查询门店列表参数封装
const getParams = () => {
  // 如果有大区则获取大区名称，否则为空字符串
  let dq = ''
  if (queryForm.shopDq.length > 0) {
    dq = queryFormList.shopDqList.filter((item) => item.eareId === queryForm.shopDq)[0]['eareName']
  }
  // 开始时间结束时间
  let startDate = ''
  if (queryForm.dateRange) {
    startDate = formatDate(queryForm.dateRange[0])
  }
  let endDate = ''
  if (queryForm.dateRange) {
    endDate = formatDate(queryForm.dateRange[1])
  }
  return {
    startDate, // 开始时间
    endDate, // 结束时间
    shopName: queryForm.storeName, // 门店名称 string
    divisionId: queryForm.divisionId, // 总部名称 string
    regionId: dq, // 大区 string   queryForm.shopDq
    eareId: queryForm.shopCq, // 城区 id
    claimStatus: queryForm.claimStatus, // 认领状态
    payerInfo: queryForm.payerInfo, // 打款人
    inBank: queryForm.inBank, // 入账银行
    amount: queryForm.amount, // 金额
    claimAmount: queryForm.claimAmount // 认领金额
  }
}

const tableLoading = ref(false) // 表格加载状态
// 查询按钮
const searchForm = (id) => {
  // 如果是查询按钮，则重置当前页码和每页条数
  if (id === -1) {
    paginationInfo.currentPage = 1
  }

  // 查询门店列表参数封装
  const params = {
    ...getParams(),
    pageNum: paginationInfo.currentPage, // 当前页码 number
    pageSize: paginationInfo.pageSize // 每页条数 number
  }
  // 如果选择了大区 则必须选择城区 否则不能查询
  // console.log(params);
  if (params.divisionId !== '' && params.eareId === '') {
    ElMessage.warning('请选择城区')
    return
  }

  tableLoading.value = true // 加载状态
  // 查询门店列表
  getPageList(params)
    .then((res) => {
      tableLoading.value = false // 加载状态
      // console.log("门店列表", res);
      if (res.code === 200) {
        // 查询成功但是没有数据则提示暂无数据
        if (res.rows.length === 0) {
          ElMessage.warning('暂无数据')
        }
        tableData.value = res.rows // 表格数据
        // paginationInfo.currentPage = res.data.pageNum; // 重置当前页码
        // paginationInfo.pageSize = res.data.size; // 重置每页条数true
        paginationInfo.totalCount = res.total // 总条数
      } else {
        ElMessage.error(res.msg)
      }
    })
    .catch((err) => {
      tableLoading.value = false // 加载状态
    })
}

// 表格数据
const tableData = ref([])
// 分页信息
const paginationInfo = reactive({
  ...structuredClone(paginationInfoConstant)
})
// 每页条数改变
const handleSizeChange = (val) => {
  paginationInfo.pageSize = val
  searchForm()
}
// 当前页码改变
const handleCurrentChange = (val) => {
  paginationInfo.currentPage = val
  searchForm()
}

const dialogFormVisible = ref(false) // 弹窗是否显示
const dialogDatas = ref({}) // 弹窗传递数据
const handleClaimShow = (row) => {
  // 打开认领弹窗
  // dialogDatas.value = row // 弹窗数据
  // dialogFormVisible.value = true
  localStorage.setItem('claimFundsData', JSON.stringify(row))
  router.push({
    path: '/managementFeeInvoice/paymentConfirm',
    query: {
      from: 'claimFunds'
    }
  })
}
const handleClaim2Show = async (row) => {
  // console.log(row);
  // 打开反认领弹窗
  let str = `取消认领金额${row.comMoney}元整。`
  let status = await ElMessageBox.confirm(str, '反认领', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(
    () => true,
    () => false
  )
  if (status) {
    // console.log("反认领成功")
    // 反认领
    const params = {
      waterOrderId: row.id, // 水单id
      inBank: row.bankNum // 入账银行
    }
    unClaimOrder(params).then((res) => {
      if (res.code === 200) {
        ElMessage.success(res.msg)
        searchForm() // 刷新表格
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}

const claimDialogClose = (params) => {
  console.log(params)
  dialogFormVisible.value = false
  if (params.type === 'success') {
    router.push({
      path: '/managementFeeInvoice/storeBillingDetails',
      query: {
        shopId: params.shopId,
        shopName: params.shopName
      }
    })
    ElMessage.success('操作成功')
  }
}

const remarkVisible = ref(false) // 流水备注弹窗
const chooseItem = ref({})
const handleRemark = (row) => {
  chooseItem.value = row // 流水备注
  remarkVisible.value = true // 显示弹窗
}

const handleRemarkClose = () => {
  remarkVisible.value = false
}

// TODO:
const handleRemarkConfirm = () => {
  remarkVisible.value = false
}
</script>
