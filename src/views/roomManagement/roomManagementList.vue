<template>
  <div class="app-container fee-manager">
    <el-form :model="queryForm" :inline="true" label-width="110px" @keydown.enter="searchForm(-1)">
      <el-form-item label="起止日期" prop="dateRange">
        <el-date-picker
          v-model="queryForm.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="w-240-important"
        />
      </el-form-item>
      <el-form-item label="门店" prop="shopName">
        <el-input
          v-model="queryForm.shopName"
          placeholder="请输入门店编号/门店名称"
          clearable
          :prefix-icon="Search"
          class="w-240"
        />
      </el-form-item>
      <el-form-item label="地区总部" prop="headquarter">
        <el-select
          v-model="queryForm.headquarter"
          placeholder="请选择地区总部"
          clearable
          filterable
          class="w-240"
          @change="handleHeadquarterChange"
        >
          <el-option
            v-for="item in queryFormList.shopHeadOfficeList"
            :key="item.freeString"
            :label="item.freeString"
            :value="item.freeString"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="大区" prop="shopDq">
        <el-select
          v-model="queryForm.shopDq"
          placeholder="请选择大区"
          clearable
          filterable
          class="w-240"
          @change="handleDqChange"
        >
          <el-option
            v-for="item in queryFormList.shopDqList"
            :key="item.eareId"
            :label="item.eareName"
            :value="item.eareId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="城区" prop="shopCq">
        <el-select
          v-model="queryForm.shopCq"
          placeholder="请选择城区"
          clearable
          filterable
          class="w-240"
        >
          <el-option
            v-for="item in queryFormList.shopCqList"
            :key="item.eareName"
            :label="item.eareName"
            :value="item.eareName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="searchForm(-1)"> 搜索 </el-button>
        <el-button icon="Refresh" plain @click="resetForm()"> 重置 </el-button>
        <el-button type="warning" plain icon="Download" @click="handleExport()"> 导出 </el-button>
      </el-form-item>
    </el-form>

    <!-- :cell-class-name="addBg" -->
    <el-table
      ref="tableContainer"
      v-loading="tableLoading"
      :data="tableData"
      class="mt-15"
      style="width: 100%"
      :height="tableHeight"
      :header-cell-class-name="addBg"
    >
      <el-table-column prop="no" label="序号" min-width="50">
        <template #default="scope">
          <div style="text-wrap: nowrap">
            {{ (paginationInfo.currentPage - 1) * paginationInfo.pageSize + scope.$index + 1 }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="shopId" label="门店ID" width="90">
        <template #default="scope">
          <el-link type="primary" underline="always" @click="handleAdd(scope.row)">
            {{ scope.row.shopId }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="shopName" label="门店名称" min-width="250"></el-table-column>
      <el-table-column prop="startDate" label="开始日期" width="150"></el-table-column>
      <el-table-column prop="endDate" label="结束日期" width="150"></el-table-column>
      <el-table-column prop="predictDays" label="预计消耗天数" width="150"></el-table-column>
      <!-- <el-table-column prop="paymentAmount" label="付款金额" width="150"></el-table-column> -->
      <el-table-column prop="disRate" label="订单折扣" width="150"></el-table-column>

      <el-table-column prop="bfAllAmount" label="包房款" width="150">
        <template #default="scope">
          {{ scope.row.bfAllAmount.amount }}
        </template>
      </el-table-column>
      <el-table-column prop="bfAllAmount" label="清分抵扣" width="150">
        <template #default="scope">
          {{ scope.row.bfAllAmount.qfAmount }}
        </template>
      </el-table-column>
      <el-table-column prop="bfAllAmount" label="剩余金额" width="150">
        <template #default="scope">
          {{ scope.row.bfAllAmount.residueAmount }}
        </template>
      </el-table-column>
      <el-table-column prop="bfAllAmount" label="完成进度" width="150">
        <template #default="scope">
          {{ scope.row.bfAllAmount.progressRate }}
        </template>
      </el-table-column>

      <el-table-column prop="principalAmount" label="本金" width="150">
        <template #default="scope">
          {{ scope.row.principalAmount.amount }}
        </template>
      </el-table-column>
      <el-table-column prop="principalAmount" label="清分抵扣-本金" width="150">
        <template #default="scope">
          {{ scope.row.principalAmount.qfAmount }}
        </template>
      </el-table-column>
      <el-table-column prop="principalAmount" label="剩余本金" width="150">
        <template #default="scope">
          {{ scope.row.principalAmount.residueAmount }}
        </template>
      </el-table-column>
      <el-table-column prop="principalAmount" label="完成进度-本金" width="150">
        <template #default="scope">
          {{ scope.row.principalAmount.progressRate }}
        </template>
      </el-table-column>

      <el-table-column prop="brokerageAmount" label="佣金" width="150">
        <template #default="scope">
          {{ scope.row.brokerageAmount.amount }}
        </template>
      </el-table-column>
      <el-table-column prop="brokerageAmount" label="清分抵扣-佣金" width="150">
        <template #default="scope">
          {{ scope.row.brokerageAmount.qfAmount }}
        </template>
      </el-table-column>
      <el-table-column prop="brokerageAmount" label="剩余佣金" width="150">
        <template #default="scope">
          {{ scope.row.brokerageAmount.residueAmount }}
        </template>
      </el-table-column>
      <el-table-column prop="brokerageAmount" label="完成进度-佣金" width="150">
        <template #default="scope">
          {{ scope.row.brokerageAmount.progressRate }}
        </template>
      </el-table-column>

      <el-table-column prop="divisionName" label="地区总部" width="200"></el-table-column>
      <el-table-column prop="regionName" label="大区" width="200"></el-table-column>
      <el-table-column prop="areaName" label="城区" width="200"></el-table-column>
      <el-table-column prop="areaUserName" label="城区总" width="150"></el-table-column>
    </el-table>
    <!-- 分页 右对齐 -->
    <el-pagination
      v-if="paginationInfo.totalCount > 0"
      class="flex flex-jus-end mt-15"
      background
      :current-page="paginationInfo.currentPage"
      :page-sizes="paginationInfo.pageSizes"
      :page-size="paginationInfo.pageSize"
      :layout="paginationInfo.layout"
      :total="paginationInfo.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
  </div>
</template>
<script setup name="RoomManagementList">
// 包房门店
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
// 引入请求
import {
  getShopCqList, // 查询门店城区列表
  // 查询门店地区总部列表
  getShopDqList,
  getShopHeadOfficeList
} from '@/api/chargingManager/search'
import { list } from '@/api/roomManagement/index.js'
import { paginationInfoConstant } from '@/utils/constants.js'
import { onMounted } from 'vue'
// 表格自动高度
import { formatDate } from '@/utils/script.js'

const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)
// 搜索表单的引用格式
const formInterface = {
  shopName: '', // 门店名称
  headquarter: '', // 总部
  shopDq: '', // 大区
  shopCq: '', // 城市
  dateRange: '' // 时间范围
}
// 选项列表
const queryFormList = reactive({
  shopHeadOfficeList: [], // 地区总部
  shopDqList: [], // 大区
  shopCqList: [] // 城区
})

// 搜索条件
const queryForm = reactive(Object.assign({}, formInterface))
// 重置搜索表单
const resetForm = () => {
  // 遍历清空对象
  Object.assign(queryForm, formInterface) // 重置表单数据
  // 清空大区和城区列表
  queryFormList.shopDqList = []
  queryFormList.shopCqList = []
}
onMounted(() => {
  // 地区总部列表查询
  getShopHeadOfficeList().then((res) => {
    if (res.code == 200) {
      queryFormList.shopHeadOfficeList = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })

  searchForm(-1) // 默认查询第一页数据
})

// 切换地区总部
const handleHeadquarterChange = (val) => {
  queryForm.shopCq = '' // 清空城区值
  queryFormList.shopCqList = [] // 清空城区列表
  queryForm.shopDq = '' // 清空大区值
  queryFormList.shopDqList = [] // 清空大区列表
  if (val && val.length > 0) {
    // 查询大区列表
    getShopDqList({ freeString: val }).then((res) => {
      console.log('大区列表', res)
      if (res.code == 200) {
        queryFormList.shopDqList = res.data
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}
// 切换大区
const handleDqChange = (val) => {
  queryForm.shopCq = '' // 清空城区值
  queryFormList.shopCqList = [] // 清空城区列表
  if (val && val.length > 0) {
    // 查询大区列表
    getShopCqList({ eareId: val }).then((res) => {
      if (res.code == 200) {
        queryFormList.shopCqList = res.data
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}
// 查询门店列表参数封装
const getParams = () => {
  // 如果有大区则获取大区名称，否则为空字符串
  let dq = ''
  if (queryForm.shopDq.length > 0) {
    dq = queryFormList.shopDqList.filter((item) => item.eareId == queryForm.shopDq)[0]['eareName']
  }
  // 开始时间结束时间
  let startDate = ''
  if (queryForm.dateRange) {
    startDate = formatDate(queryForm.dateRange[0])
  }
  let endDate = ''
  if (queryForm.dateRange) {
    endDate = formatDate(queryForm.dateRange[1])
  }
  return {
    startDate, // 开始时间
    endDate, // 结束时间
    shopName: queryForm.shopName, // 门店名称 string
    divisionName: queryForm.headquarter, // 总部名称 string
    regionName: dq, // 大区 string   queryForm.shopDq
    areaName: queryForm.shopCq // 城区 id
  }
}

const tableLoading = ref(false) // 表格加载状态
// 查询按钮
const searchForm = (id) => {
  // 如果是查询按钮，则重置当前页码和每页条数
  if (id == -1) {
    paginationInfo.currentPage = 1
  }

  // 查询门店列表参数封装
  const params = {
    ...getParams(),
    pageNum: paginationInfo.currentPage, // 当前页码 number
    pageSize: paginationInfo.pageSize // 每页条数 number
  }
  tableLoading.value = true // 加载状态
  // 查询门店列表
  list(params)
    .then((res) => {
      tableLoading.value = false // 加载状态
      // console.log("门店列表",res);
      if (res.code == 200) {
        if (res.data.list.length == 0) {
          ElMessage.warning('暂无数据')
        }
        tableData.value = res.data.list // 表格数据
        paginationInfo.totalCount = res.data.total // 总条数
      } else {
        ElMessage.error(res.msg)
      }
    })
    .catch((err) => {
      tableLoading.value = false // 加载状态
    })
}

// 表格数据
const tableData = ref([])
// 分页信息
const paginationInfo = reactive({
  ...structuredClone(paginationInfoConstant)
})
// 每页条数改变
const handleSizeChange = (val) => {
  paginationInfo.pageSize = val
  searchForm()
}
// 当前页码改变
const handleCurrentChange = (val) => {
  paginationInfo.currentPage = val
  searchForm()
}

// 表格添加背景颜色
const addBg = (row) => {
  // console.log("row",row.column.property);
  return row.column.property
}

// 包房门店详情
const handleAdd = (row) => {
  ElMessage.warning('包房门店详情暂未开放！')
  // console.log("row",row);
  // router.push({
  //   path: "/chargingManager/feeManagerAdd/",
  //   query: {
  //     id : row.shopId !== -1 ? row.shopId : -1,
  //     name: row.shopName,
  //     stopCharge:row.stopCharge
  //   }
  // });
}
// 导出
const handleExport = () => {
  ElMessage.warning('导出暂未开放！')
  // // 查询门店列表参数封装
  // const params = {...getParams()};
  // download("",params,`包房门店信息_${new Date().getTime()}.xlsx`);
}
</script>
<style>
.no,
.shopId,
.shopName,
.startDate,
.endDate,
.predictDays,
.disRate,
.divisionName,
.regionName,
.areaName,
.areaUserName {
  background-color: rgb(230.4 247.1 255) !important;
}

.bfAllAmount {
  background-color: rgb(209.4 236.7 195.9) !important;
}

.principalAmount {
  background-color: rgb(247.5 227.1 196.5) !important;
}

.brokerageAmount {
  background-color: rgb(252 210.9 210.9) !important;
}
</style>
