<template>
  <div class="app-container fee-manager">
    <el-form :model="queryForm" :inline="true" label-width="110px" @keydown.enter="searchForm(-1)">
      <el-form-item label="门店" prop="storeName">
        <el-input
          v-model="queryForm.storeName"
          placeholder="请输入门店编号/门店名称"
          clearable
          :prefix-icon="Search"
          class="w-240"
        />
      </el-form-item>
      <el-form-item label="营业状态" prop="businessStatus">
        <el-select
          v-model="queryForm.businessStatus"
          placeholder="请选择营业状态"
          clearable
          filterable
          multiple
          collapse-tags
          class="w-240"
          :max-collapse-tags="1"
        >
          <el-option
            v-for="item in queryFormList.businessStatus"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="地区总部" prop="headquarter">
        <el-select
          v-model="queryForm.headquarter"
          placeholder="请选择地区总部"
          clearable
          filterable
          class="w-240"
          @change="handleHeadquarterChange"
        >
          <el-option
            v-for="item in queryFormList.shopHeadOfficeList"
            :key="item.freeString"
            :label="item.freeString"
            :value="item.freeString"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="大区" prop="shopDq">
        <el-select
          v-model="queryForm.shopDq"
          placeholder="请选择大区"
          clearable
          filterable
          class="w-240"
          @change="handleDqChange"
        >
          <el-option
            v-for="item in queryFormList.shopDqList"
            :key="item.eareId"
            :label="item.eareName"
            :value="item.eareId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="城区" prop="shopCq">
        <el-select
          v-model="queryForm.shopCq"
          placeholder="请选择城区"
          clearable
          filterable
          class="w-240"
        >
          <el-option
            v-for="item in queryFormList.shopCqList"
            :key="item.eareId"
            :label="item.eareName"
            :value="item.eareId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="输送客源费方案" prop="schemeType">
        <el-select
          v-model="queryForm.schemeType"
          placeholder="请选择输送客源费方案类型"
          clearable
          filterable
          class="w-240"
        >
          <el-option
            v-for="item in scheme_type"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计费审核状态" prop="auditStatus">
        <el-select
          v-model="queryForm.auditStatus"
          placeholder="请选择计费审核状态"
          clearable
          filterable
          class="w-240"
        >
          <el-option
            v-for="item in charge_audit_status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否舞弊" prop="cheatStatus">
        <el-select
          v-model="queryForm.cheatStatus"
          placeholder="请选择是否舞弊"
          clearable
          filterable
          class="w-240"
        >
          <el-option
            v-for="item in cheat_status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否封存" prop="sealUpStatus">
        <el-select
          v-model="queryForm.sealUpStatus"
          placeholder="请选择是否封存"
          clearable
          filterable
          class="w-240"
        >
          <el-option
            v-for="item in seal_up_status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="searchForm(-1)"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetForm()"> 重置 </el-button>
        <el-button type="primary" icon="Download" plain @click="handleExport()"> 导出 </el-button>
        <el-button type="warning" plain @click="handleVerificationFormula()"> 核验公式 </el-button>
      </el-form-item>
    </el-form>

    <el-table
      ref="tableContainer"
      v-loading="chargingManager.table"
      :data="tableData"
      class="mt-15"
      style="width: 100%"
      :height="tableHeight"
    >
      <el-table-column prop="no" label="序号" min-width="50">
        <template #default="scope">
          <div style="text-wrap: nowrap">
            {{ (paginationInfo.currentPage - 1) * paginationInfo.pageSize + scope.$index + 1 }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="shopId" label="门店ID" width="90">
        <template #default="scope">
          <el-link type="primary" underline @click="handleAdd(scope.row)">
            {{ scope.row.shopId }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="shopName" label="门店名称" min-width="250"></el-table-column>
      <el-table-column prop="status" label="门店状态" width="100">
        <template #default="scope">
          <el-tag v-if="['解约', '作废'].includes(scope.row.status)" type="danger">
            {{ scope.row.status }}
          </el-tag>
          <el-tag v-else type="success">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="schemeType" label="输送客源费方案类型" min-width="150">
        <template #default="scope">
          <!-- 使用for循环防止找不到报错 -->
          <div v-for="item in scheme_type" :key="item.value">
            <template v-if="item.value == scope.row.schemeType">
              {{ item.label }}
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="startDate" label="开业日期" min-width="100"></el-table-column>
      <el-table-column prop="stopCharge" label="是否停止计费(不包含工资)" min-width="200">
        <template #default="scope">
          <el-tag v-if="scope.row.stopCharge == 1" type="danger"> 是 </el-tag>
          <el-tag v-else type="success"> 否 </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="cheatStatus" label="是否舞弊" min-width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.cheatStatus == 1" type="danger"> 是 </el-tag>
          <el-tag v-else type="success"> 否 </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="sealUpStatus" label="是否封存" min-width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.sealUpStatus == 1" type="danger"> 是 </el-tag>
          <el-tag v-else type="success"> 否 </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="sealUpStatus" label="计费审核状态" min-width="150">
        <template #default="scope">
          <template v-for="item in charge_audit_status" :key="item.value">
            <el-tag v-if="item.value == scope.row.auditStatus" :type="item.type">
              {{ item.label }}
            </el-tag>
          </template>
        </template>
      </el-table-column>
      <!-- 
      <el-table-column prop="createDate" label="创建日期" width="170"></el-table-column>
      <el-table-column prop="freeDate" label="签约日期" width="170"></el-table-column>
      <el-table-column prop="overDate" label="解约日期" width="170"></el-table-column>
      <el-table-column prop="freeDate" label="恢复营业日期" width="170"></el-table-column> 
      -->
      <el-table-column prop="headOffice" label="地区总部" width="120"></el-table-column>
      <el-table-column prop="dq" label="大区" width="200"></el-table-column>
      <el-table-column prop="cq" label="城区" width="200"></el-table-column>
      <el-table-column prop="manager" label="城区总" width="120"></el-table-column>
      <el-table-column prop="applyUser" label="操作人" width="200"></el-table-column>
    </el-table>
    <!-- 分页 右对齐 -->
    <el-pagination
      v-if="paginationInfo.totalCount > 0"
      class="flex flex-jus-end mt-15"
      background
      :current-page="paginationInfo.currentPage"
      :page-sizes="paginationInfo.pageSizes"
      :page-size="paginationInfo.pageSize"
      :layout="paginationInfo.layout"
      :total="paginationInfo.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
    <!-- 核验公式 -->
    <VerificationFormula />
  </div>
</template>
<script setup name="FeeManagerList">
// 计费管理-门店收费管理
import router from '@/router'
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
// 引入请求
import {
  getBasisShopList, // 查询门店列表
  // 查询门店大区列表
  getShopCqList, // 查询门店地区总部列表
  getShopDqList, // 门店状态
  getShopHeadOfficeList,
  getShopStatusList
} from '@/api/chargingManager/search'

import chargingManagerStore from '@/store/modules/ChargingManager'
import { paginationInfoConstant } from '@/utils/constants.js'
import { download2 } from '@/utils/request'
import { onMounted } from 'vue'
const chargingManager = chargingManagerStore()
// 引入组件
import VerificationFormula from '@/components/ChargingManager/VerificationFormula.vue'

const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)
const { proxy } = getCurrentInstance()
const { scheme_type, seal_up_status, cheat_status, charge_audit_status } = proxy.useDict(
  'scheme_type',
  'seal_up_status',
  'cheat_status',
  'charge_audit_status'
)

// 搜索表单的引用格式
const formInterface = {
  storeName: '', // 门店名称
  businessStatus: [], // 门店状态
  headquarter: '', // 总部
  shopDq: '', // 大区
  shopCq: '', // 城市
  schemeType: '', // 输送客源费方案类型
  auditStatus: '', // 计费状态
  cheatStatus: '', // 是否舞弊
  sealUpStatus: '' // 是否封存
}
// 选项列表
const queryFormList = reactive({
  businessStatus: [], // 门店状态
  shopHeadOfficeList: [], // 地区总部
  shopDqList: [], // 大区
  shopCqList: [] // 城区
})
// 搜索条件
const queryForm = reactive(Object.assign({}, formInterface))
// 重置搜索表单
const resetForm = () => {
  // 遍历清空对象
  Object.assign(queryForm, formInterface) // 重置表单数据
  // 清空大区和城区列表
  queryFormList.shopDqList = []
  queryFormList.shopCqList = []
}
onMounted(() => {
  // 门店状态
  getShopStatusList().then((res) => {
    // console.log("门店状态",res);
    if (res.code === 200) {
      queryFormList.businessStatus = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
  // 地区总部列表查询
  getShopHeadOfficeList().then((res) => {
    if (res.code === 200) {
      queryFormList.shopHeadOfficeList = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })

  searchForm(-1) // 默认查询第一页数据
})

// 切换地区总部
const handleHeadquarterChange = (val) => {
  queryForm.shopCq = '' // 清空城区值
  queryFormList.shopCqList = [] // 清空城区列表
  queryForm.shopDq = '' // 清空大区值
  queryFormList.shopDqList = [] // 清空大区列表
  if (val && val.length > 0) {
    // 查询大区列表
    getShopDqList({ freeString: val }).then((res) => {
      console.log('大区列表', res)
      if (res.code === 200) {
        queryFormList.shopDqList = res.data
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}
// 切换大区
const handleDqChange = (val) => {
  queryForm.shopCq = '' // 清空城区值
  queryFormList.shopCqList = [] // 清空城区列表
  if (val && val.length > 0) {
    // 查询大区列表
    getShopCqList({ eareId: val }).then((res) => {
      if (res.code === 200) {
        queryFormList.shopCqList = res.data
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}
// 查询门店列表参数封装
const getParams = () => {
  // 如果有大区则获取大区名称，否则为空字符串
  let dq = ''
  if (queryForm.shopDq.length > 0) {
    dq = queryFormList.shopDqList.filter((item) => item.eareId === queryForm.shopDq)[0]['eareName']
  }
  return {
    shopName: queryForm.storeName, // 门店名称 string
    status: queryForm.businessStatus.join(','), // 门店状态 string
    headOffice: queryForm.headquarter, // 总部名称 string
    dq: dq, // 大区 string   queryForm.shopDq
    cq: queryForm.shopCq, // 城区 id
    schemeType: queryForm.schemeType, // 输送客源费方案类型 string
    auditStatus: queryForm.auditStatus, // 计费状态 string
    cheatStatus: queryForm.cheatStatus, // 是否舞弊 string
    sealUpStatus: queryForm.sealUpStatus // 是否封存 string
  }
}

// 查询按钮
const searchForm = (id) => {
  // 如果是查询按钮，则重置当前页码和每页条数
  if (id === -1) {
    paginationInfo.currentPage = 1
  }
  chargingManager.table = true // 加载状态

  // 查询门店列表参数封装
  const params = {
    ...getParams(),
    pageNum: paginationInfo.currentPage, // 当前页码 number
    pageSize: paginationInfo.pageSize // 每页条数 number
  }

  // 查询门店列表
  getBasisShopList(params)
    .then((res) => {
      chargingManager.table = false // 加载状态
      // console.log("门店列表",res);
      if (res.code === 200) {
        tableData.value = [] // 清空表格数据

        // 查询成功但是没有数据则提示暂无数据
        if (res.data.records.length === 0) {
          ElMessage.warning('暂无数据')
          // return false;
        }
        tableData.value = res.data.records // 表格数据
        paginationInfo.currentPage = res.data.pageNum // 重置当前页码
        // paginationInfo.pageSize = res.data.size; // 重置每页条数true
        paginationInfo.totalCount = res.data.total // 总条数
      } else {
        ElMessage.error(res.msg)
      }
    })
    .catch(() => {
      chargingManager.table = false // 加载状态
    })
}

// 表格数据
const tableData = ref([])
// 分页信息
const paginationInfo = reactive({
  ...structuredClone(paginationInfoConstant)
})
// 每页条数改变
const handleSizeChange = (val) => {
  paginationInfo.pageSize = val
  searchForm()
}
// 当前页码改变
const handleCurrentChange = (val) => {
  paginationInfo.currentPage = val
  searchForm()
}

// 跳转到新增门店
const handleAdd = (row) => {
  // console.log("row",row);
  router.push({
    path: '/chargingManager/feeManagerAdd',
    query: {
      shopId: row.shopId !== -1 ? row.shopId : -1,
      shopName: row.shopName,
      stopCharge: row.stopCharge,
      schemeType: row.schemeType
    }
  })
}
// 导出
const handleExport = () => {
  // 查询门店列表参数封装
  const params = { ...getParams() }
  download2('/shop/basis/exportExcel', params, `门店信息_${new Date().getTime()}.xlsx`)
}
// 核验公式
const handleVerificationFormula = () => {
  chargingManager.verificationFormulaType = true
}
</script>
