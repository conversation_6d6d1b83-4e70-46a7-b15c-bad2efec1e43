<template>
  <div class="app-container datainput-detail">
    <el-row :gutter="10">
      <el-col
        v-if="pageType" :span="7" :lg="6" :md="7" :sm="8"
        :xs="12"
      >
        <el-select
          v-model="shopId"
          filterable
          remote
          remote-show-suffix
          reserve-keyword
          placeholder="请输入门店编号/门店名称"
          :remote-method="remoteMethod"
          :loading="chargingManager.search"
          style="width: 100%"
          @change="handleShopChange(shopId)"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-col>
      <el-col
        v-else :span="24" :lg="24" :md="24" :sm="24"
        :xs="24"
      >
        <div class="flex flex-jus-between">
          <table border class="table1">
            <tr>
              <th>门店id</th>
              <td>{{ shopId }}</td>
              <th>门店名称</th>
              <td>{{ shopName }}</td>
              <th>状态</th>
              <td>
                <el-text v-if="stopCharge != 0" type="danger" tag="b">
                  停止计费
                </el-text>
                <el-text v-else type="success" tag="b">
                  正在计费
                </el-text>
              </td>
            </tr>
          </table>
          <div>
            <el-button v-if="stopCharge != 0" type="danger" plain class="noborder" @click="stopBilling(0)">
              恢复计费
            </el-button>
            <el-button v-else type="danger" plain class="noborder" @click="stopBilling(1)">
              停止计费
            </el-button>
            <!-- <el-button type="warning" plain class="noborder" @click="saveDraft">保存草稿</el-button> -->
            <el-button type="primary" plain class="noborder" @click="submitReview">
              提交审核
            </el-button>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 表格 -->
    <el-row class="mt-10">
      <el-col :span="24">
        <!-- max-height="600px" -->
        <el-table 
          :data="tableData"
          style="width: 100%; "
          @cell-dblclick="handleCellDoubleClick"
        >
          <!-- <el-table-column type="selection" width="50" align="center" /> -->
          <!-- <el-table-column label="编号" align="center" width="100" key="userId" prop="id" /> -->
          <el-table-column
            key="userId"
            label="费用名称"
            align="center"
            width="140"
            prop="chargeName"
          />

          <el-table-column
            label="生效时间"
            align="center"
            prop="endTime"
            width="200"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              <el-tag type="primary">
                <span v-if="scope.row.endTime != '长期'">{{ scope.row.startTime }}至</span>{{ scope.row.endTime }}
              </el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column
            label="人员在店"
            align="center"
            prop="shopManagerStatus"
            width="80"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="阶梯条件"
            align="center"
            prop="matchBetType"
            :show-overflow-tooltip="true"
            min-width="150"
          /> -->
          <el-table-column
            label="公式说明"
            align="left"
            prop="calculateDesc"
            min-width="450"
          >
            <template #default="scope">
              <el-text>{{ scope.row.calculateDesc }}</el-text>
            </template>
          </el-table-column>

          <el-table-column
            label="类型"
            align="center"
            prop="calculateType"
            :show-overflow-tooltip="true"
            width="120"
          />
          <el-table-column
            key="batchNumber"
            label="批次"
            align="center"
            width="70"
            prop="batchNumber"
            :show-overflow-tooltip="true"
          >
            <template #default="{ row }">
              <el-input
                v-if="row.isEditValue"
                v-model="row.batchNumber"
                @blur="handleInputBlur(row)"
              />
              <el-tag v-else type="success">
                {{ row.batchNumber }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="备注"
            align="center"
            prop="remark"
            :show-overflow-tooltip="true"
            min-width="200"
          />

          <el-table-column
            label="操作日期"
            align="center"
            prop="createTime"
            :show-overflow-tooltip="true"
            min-width="200"
          />
          <el-table-column
            label="审核状态"
            align="center"
            prop="auditStatus"
            :show-overflow-tooltip="true"
            min-width="300"
          >
            <template #default="scope">
              <template v-for="item in charge_audit_status" :key="item.value">
                <el-tag 
                  v-if="scope.row.auditStatus == item.value" 
                  :type="['danger', 'info', 'primary', 'success', 'info'][item.value - 1]"
                >
                  {{ item.label }}
                </el-tag>
              </template>
            </template>
          </el-table-column>
          <el-table-column
            label="退回原因"
            align="center"
            prop="auditRemark"
            :show-overflow-tooltip="true"
            min-width="200"
          />
          <el-table-column
            label="审核日期"
            align="center"
            prop="auditDate"
            :show-overflow-tooltip="true"
            min-width="200"
          />

          <!-- <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column> -->
          <el-table-column
            label="操作"
            align="center"
            width="220"
            class-name="small-padding fixed-width"
            fixed="right"
          >
            <template #default="scope">
              <!-- 待审核不可编辑删除 -->
              <!-- 已通过不可删除 -->
              <el-button
                type="warning"
                class="noborder"
                link
                icon="View"
                @click="handleUpdate(scope.row,'preview')"
              >
                预览
              </el-button>
              <!-- 方案A B 输送客源费 不可编辑 不可删除 -->
              <el-button
                type="primary"
                class="noborder"
                link
                icon="Edit"
                :disabled="(scope.row.chargeName == '输送客源费' && ['1','2'].includes(schemeType)) || [2].includes(scope.row.auditStatus)"
                @click="handleUpdate(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                class="noborder"
                icon="Delete"
                link
                :disabled=" (scope.row.chargeName == '输送客源费' && ['1','2'].includes(schemeType)) || [2, 4].includes(scope.row.auditStatus)"
                @click="handleDelete(scope.row.id)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="paginationInfo.totalCount > 0"
          v-model:current-page="paginationInfo.currentPage"
          class="flex flex-jus-end mt-10"
          background
          :current-page="paginationInfo.currentPage"
          :page-sizes="paginationInfo.pageSizes"
          :page-size="paginationInfo.pageSize"
          :layout="paginationInfo.layout"
          :total="paginationInfo.totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
    <el-row class="mt-15">
      <el-col>
        <el-button-group>
          <el-button 
            v-for="item in tabList"
            :key="item.value" 
            type="primary" 
            :disabled="item.disabled"
            @click="handleAdd(item)"
          >
            {{ item.label }}
          </el-button>
        </el-button-group>
      </el-col>
    </el-row>
    <ChargeCom v-if="open" :datas="dialogDatas" @open-type="openType" />

    <el-row class="mt-15">
      <el-col :span="8">
        <b>计费标准-合同台账</b>
      </el-col>
      <el-col :span="16" class="pl-20">
        <b>操作记录</b>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8" class="mt-10">
        <el-table :data="contractTableData" style="width: 100%;">
          <!-- <el-table-column prop="operId" label="序号" width="50"></el-table-column> -->
          <el-table-column prop="feeName" label="费用类型"></el-table-column>
          <el-table-column prop="feeInfo" label="标准收费"></el-table-column>
        </el-table>
      </el-col>
      <el-col :span="16" class="mt-10 pl-20">
        <el-table :data="OperationRecordTableData" style="width: 100%;">
          <!-- <el-table-column prop="operId" label="序号" width="50"></el-table-column> -->
          <el-table-column prop="updateTime" label="修改日期" width="200"></el-table-column>
          <el-table-column prop="updateBy" label="修改人" width="150"></el-table-column>
          <!-- <el-table-column prop="operName" label="审核状态" width="150">
            <template #default="scope">
              <el-tag v-if="scope.row.operName" type="success">{{ scope.row.operName }}</el-tag>
              <el-tag v-else type="error">{{ scope.row.operName }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="operName" label="审核人" width="150"></el-table-column>
          <el-table-column prop="operLocation" label="操作电脑" width="200" ></el-table-column>
          <template #default="scope">
              <el-tag>{{scope.row.operLocation}}</el-tag>
            </template> -->
          <el-table-column prop="formulaDescription" label="修改内容" min-width="350"></el-table-column>
          <el-table-column prop="title" label="跳转到" min-width="120" fixed="right">
            <template #default="scope">
              <el-button
                class="noborder" type="primary" link icon="View" size="small"
                @click="toLink(scope.row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-model:current-page="OperationRecordHandleSizeChange.currentPage"
          class="flex flex-jus-end mt-10"
          background
          :current-page="OperationRecordPaginationInfo.currentPage"
          :page-sizes="OperationRecordPaginationInfo.pageSizes"
          :page-size="OperationRecordPaginationInfo.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="OperationRecordPaginationInfo.totalCount"
          @size-change="OperationRecordHandleSizeChange"
          @current-change="OperationRecordHandleCurrentChange"
        ></el-pagination>
      </el-col>
    </el-row>
  </div>
</template>
<script setup>
// 门店计费规则设置
import {
  paginationInfoConstant, tabList
} from '@/utils/constants.js'
import { ElMessage, ElMessageBox } from 'element-plus'
  
// 引入组件 
import {
  delCharge, // 列表中修改批次号
  getBasisShopFee,
  getChargeList, // 停止门店计费
  getCheckAudit, // 删除收费项目
  getLogList, // 查询门店合同台账
  stopBasisCharge, // 查询是否有待审核计费规则
  submitAudit // 门店计费规则提交审核
  , // 获取操作日志列表
  updateBatchNumber
} from '@/api/chargingManager/FeeManager'
import ChargeCom from '@/components/ChargingManager/FeeManager/ChargeCom.vue'
import { ref } from 'vue'

import store from '@/store'
// console.log(store.state.value.user)

import chargingManagerStore from '@/store/modules/ChargingManager'
const chargingManager = chargingManagerStore()

const { proxy } = getCurrentInstance()

const { charge_audit_status } = proxy.useDict('charge_audit_status')

// 获取门店列表方法
import router from '@/router'
import { getSearchShopListFunc } from '@/utils/getSearchShopListFunc'

// 接收url中的参数
const route = useRoute()
console.log(route.query)
// 判断模式是编辑还是新增
const pageType = ref(route.query.shopId === -1) // -1为新增
const shopId = ref(!pageType.value ? route.query.shopId : '') // 当前选中的门店id
const shopName = ref(route.query.shopName) // 当前选中的门店名称
const stopCharge = ref(route.query.stopCharge) // 当前选中的门店状态
const schemeType = ref(route.query.schemeType) // 计费方案类型 当值为1 2时 输送客源费不能编辑只能查看
 
// 如果是方案AB且是输送客源费 则按钮不可点击
for(let item of tabList){
  item.disabled = ['1', '2'].includes(schemeType.value) && item.label === '输送客源费'
}

// 表格数据
const tableData = ref([])
  
// 分页信息
const paginationInfo = reactive({
  ...structuredClone(paginationInfoConstant)
})
paginationInfo.pageSize = 10
// 每页条数改变
const handleSizeChange = (val) => {
  paginationInfo.pageSize = val
  searchShopDatasList(shopId.value)
}
// 当前页码改变
const handleCurrentChange = (val) => {
  paginationInfo.currentPage = val
  searchShopDatasList(shopId.value)
}
// 切换门店
const handleShopChange = (val) => {
  shopId.value = val
  searchShopDatasList(val)
  getOperationLog()
}
// 查询数据
const searchShopDatasList = (id) => {
  getChargeList({ 
    pageNum:paginationInfo.currentPage,
    pageSize:paginationInfo.pageSize, 
    shopId:id
  }).then((res) => {
    if (res.code === 200) {
      tableData.value = res.data.records
      paginationInfo.totalCount = res.data.total
    }
  })
}
// 如果为编辑则默认调用查询数据
if(!pageType.value){
  searchShopDatasList(route.query.shopId)
}
//批次双击修改内容
const handleInputBlur = (row) => {
  row.isEditValue = false
  if (row.batchNumber === '' || row.batchNumber === null) {
    return ElMessage.error('批次号不能为空')
  }
  // 是否为纯数字;
  if (!/^\d+$/.test(row.batchNumber)) {
    return ElMessage.error('批次号必须为纯数字')
  }
  console.log('handleInputBlur=>', row.id, row.batchNumber)
  updateBatchNumber({ id: row.id, batchNumber: row.batchNumber })
}
/** 表格中编辑按钮 */
function handleUpdate (row, type = '') {
  open.value = false
  // console.log("编辑或预览", row ,type);
  row.preview = (type === 'preview')
  row.feeName = row.chargeName// 费用名称
  // console.log(shopId.value);
  row.shopId = shopId.value
  // console.log(row);
  setTimeout(() => {
    dialogDatas.value = row
    open.value = true
  }, 50)
}
// 删除
const handleDelete = (id) => {
  // 确认要删除吗？
  ElMessageBox.confirm(
    '确认删除本条计费规则?',
    '警告',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    delCharge({
      id:id,
      shopId:shopId.value,
      userId:store.state.value.user.id,
      username:store.state.value.user.name
    }).then(() => {
      ElMessage ({
        type: 'success',
        message: '删除成功!'
      })
      open.value = false // 关闭编辑窗口
      searchShopDatasList(route.query.shopId)
      getOperationLog()
    })
  }).catch(() => {
    console.log('取消删除')      
  })
}
// 弹窗展示状态
const open = ref(false)
const dialogDatas = ref({}) // 传递到弹窗的数据
/** 新增按钮操作 */
function handleAdd (args) {
  if(shopId.value.length <= 0){
    ElMessage.error('请先选择门店!')
    return false
  }
  open.value = false
  console.log('新增', args)
  args.shopId = shopId.value
  args.feeName = args.label// 费用名称
  nextTick(() => {
    open.value = true
    dialogDatas.value = args
  })
}
// 修改弹窗打开状态
const openType = (type) => {
  open.value = type
  searchShopDatasList(shopId.value)
  getOperationLog()
}
 
// 加载中状态
const searchTime = ref(null)

// 动态搜索select下拉框数据
const options = ref([])
const searchShopList = ref([])
const remoteMethod = (query) => {
  // console.log("动态搜索:",query);
  // 获取所有门店名称和id信息
  if(searchShopList.value.length <= 0){
    chargingManager.search = true
    getSearchShopListFunc().then((res) => {
      searchShopList.value = res
      chargingManager.search = false
    })
  }
  clearTimeout(searchTime.value)
  searchTime.value = setTimeout(() => {
    if (query && query.length >= 2) {
      // 过滤出所有包含查询字符串的选项 取前100条数据展示出来
      options.value = searchShopList.value.filter((item) => {
        return item.label.includes(query)
      }).slice(0, 500)
    } else {
      options.value = []
    }
  }, 300)
}

// 操作记录
const OperationRecordTableData = ref([])
// 分页信息
const OperationRecordPaginationInfo = reactive({
  ...structuredClone(paginationInfoConstant)
})
OperationRecordPaginationInfo.pageSizes.unshift(5)
OperationRecordPaginationInfo.pageSize = 5

// 获取操作日志
const getOperationLog = () => {
  // console.log("获取操作日志");
  const params = {
    shopId:shopId.value,
    pageNum:OperationRecordPaginationInfo.currentPage,
    pageSize:OperationRecordPaginationInfo.pageSize
  }
  getLogList(params).then((res) => {
    if(res.data){
      OperationRecordTableData.value = res.data.records
      OperationRecordPaginationInfo.currentPage = res.data.current
      OperationRecordPaginationInfo.totalCount = res.data.total
    }
  })
}
getOperationLog()

// 计费标准-合同台账
const contractTableData = ref([])
// 查询门店合同台账
const getBasisShopFeeFun = () => {
  const params = {
    shopId:shopId.value
  }
  getBasisShopFee(params).then((res) => {
    if(res.code === 200 && res.data.length > 0){
      contractTableData.value = res.data
      // {"msg":"操作成功","code":200,"data":[{"shopId":"00001","feeType":null,"feeName":"会员管理费","feeInfo":"0.04元/积分"}]}
    }
  })
}
getBasisShopFeeFun()

// 每页条数改变
const OperationRecordHandleSizeChange = (val) => {
  OperationRecordPaginationInfo.pageSize = val
  getOperationLog()
}
// 当前页码改变
const OperationRecordHandleCurrentChange = (val) => {
  OperationRecordPaginationInfo.currentPage = val
  getOperationLog()
}
// 双击
const handleCellDoubleClick = (row, column) => {
  // console.log("双击单元格", column.property);
  if (column.property === 'batchNumber') {
    row.isEditValue = true
  }
}

// 停止计费
const stopBilling = (num) => {
  ElMessageBox.confirm(
    ['恢复计费？', '除工资以外的其他费用都停止计费？'][num],
    '警告',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 停止计费接口
    chargingManager.search = true
    stopBasisCharge({shopId:shopId.value, type:num}).then((res) => {
      if (res.code === 200) {
        ElMessage ({
          type: 'success',
          message: res.msg
        })
        stopCharge.value = num
        const newQuery = {
          ...route.query, // 保留原有的路由参数
          stopCharge: num // 示例：添加或更新一个新的路由参数
        }
        router.push({
          path: '/chargingManager/feeManagerAdd',
          query: newQuery
        })
      }
    })
  }).catch(() => {
    console.log('取消操作')      
  })
}
const reload = () => {
  searchShopDatasList(route.query.shopId)
  getBasisShopFeeFun()
  getOperationLog()
}
// 提交审核
const submitReview = () => {
  // console.log('提交审核',shopId.value);
  // 提交审核接口
  chargingManager.search = true
  ElMessageBox.confirm(
    '确认保存本次计费规则的修改？',
    '警告',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const params = {
      shopId: shopId.value
    }
    getCheckAudit(params).then((res) => {
      // console.log(res)
      if (res.code === 200 && res.data.existAudit) {
        submitAudit(params).then((res) => {
          // console.log(res)
          if (res.code === 200) {
            ElMessage ({
              type: 'success',
              message: res.msg
            })
            reload()
          }else{
            ElMessage ({
              type: 'error',
              message: res.msg
            })
          }
        })
      }else{
        ElMessage ({
          type: 'error',
          message: '暂无待审核计费规则!'
        })
      }
    })
  }).catch(() => {
    console.log('取消操作')      
  })
}
const toLink = (row) => {
  // console.log(row)
  router.push({
    path: '/chargingManager/review', 
    query: {
      shopId: shopId.value, 
      shopName: shopName.value,
      id:row.feeAuditId,
      auditStatus: row.auditStatus
    }
  })
}
</script>
<style scoped lang="scss">
  
</style>
