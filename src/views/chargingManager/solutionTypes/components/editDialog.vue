<template>
  <el-dialog v-model="dialogOpen" title="修改" width="600px" append-to-body>
    <el-form ref="modelRef" :model="form" :rules="rules" label-width="160px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="输送客源费方案类型" prop="schemeType">
            <el-select
              v-model="form.schemeType"
              placeholder="请选择输送客源费方案类型"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in schemeType"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>              
      </el-row>
      <el-row v-if="!['3','4'].includes(form.schemeType)">
        <el-col :span="24">
          <el-form-item label="上限为营业收入的%" prop="upperLimitPercent">
            <el-input
              v-model="form.upperLimitPercent"
              placeholder="请输入上限为营业收入的%"
              clearable
              style="width: 240px"
            />
          </el-form-item>
        </el-col>              
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submit">
          确 认
        </el-button>
        <el-button @click="cancel">
          取 消
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="EditDialog">
import {
  editCustomerSourceScheme
} from '@/api/chargingManager/solutionTypes/index'

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },

  detail: {
    type: Object,
    default: () => ({})
  },

  schemeType: {
    type: Array,
    default: () => []
  }
})
const emit = defineEmits(['update:open', 'updateList'])
const { proxy } = getCurrentInstance()

watch(() => props.open, (newValue) => {
  if (newValue) {
    form.value = {
      ...props.detail,
      schemeType: props.detail.schemeType?.toString()
    }
  }
})

const dialogOpen = computed({
  get () {
    return props.open
  },
  set (val) {
    emit('update:open', val)
  }
})
const validateUpperLimitPercent = (rule, value, callback) => {
  if (!['3', '4'].includes(form.value.schemeType)) {
    if (!value && value !== 0) {
      callback(new Error('请输入上限为营业收入的%'))
    } else if (isNaN(value)) {
      callback(new Error('请输入有效的数字'))
    } else if (Number(value) > 1000) {
      callback(new Error('输入的数字必须小于等于1000'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}
const form = ref({})
const data = reactive({
  rules: {
    schemeType: [{ required: true, message: '请选择输送客源费方案类型', trigger: 'change' }],
    upperLimitPercent: [{ required: true, validator:validateUpperLimitPercent, trigger: 'blur' }]
  }
})
const { rules } = toRefs(data)
const loading = ref(false)

/** 取消按钮 */
const cancel = () => {
  dialogOpen.value = false
  reset()
}

const modelRef = ref(null)
/** 重置表单内容 */
const reset = () => {
  modelRef.value.resetFields()
}

// 提交
const submit = () => {
  proxy.$refs['modelRef'].validate((valid) => {
    if (valid) {
      editCustomerSourceScheme(form.value).then(() => {
        proxy.$modal.msgSuccess('修改成功')
        emit('updateList')
        cancel()
      })
    }
  })
}
</script>