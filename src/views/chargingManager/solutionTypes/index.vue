<template>
  <div class="app-container">
    <el-alert
      title="按照输送客源费方案类型不同，对门店输送客源费进行计费规则批量设置。"
      type="info"
      style="margin-bottom: 10px"
      show-icon
      :closable="false"
    />
    <el-form ref="queryRef" :model="queryParams" :inline="true" label-width="110px">
      <el-form-item label="门店" prop="shopName">
        <el-input
          v-model="queryParams.shopName"
          placeholder="请输入门店编码/门店名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否新动力" prop="isNewPower">
        <el-select
          v-model="queryParams.isNewPower"
          placeholder="请选择是否新动力"
          clearable
          style="width: 240px"
        >
          <el-option label="是" value="true" />
          <el-option label="否" value="false" />
        </el-select>
      </el-form-item>
      <el-form-item label="输送客源类型" prop="schemeType">
        <el-select
          v-model="queryParams.schemeType"
          placeholder="请选择输送客源类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in scheme_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
        <el-button type="info" plain icon="Upload" @click="handleUpload"> 导入 </el-button>
        <el-button type="warning" icon="Download" @click="handleExport"> 导出 </el-button>
      </el-form-item>
    </el-form>

    <el-table
      ref="tableContainer"
      v-loading="loading"
      :data="budgetChangeList"
      :height="tableHeight"
      class="mt-15"
    >
      <el-table-column type="index" label="序号" width="50" align="left" />
      <template v-for="item in columns">
        <el-table-column
          v-if="item.prop === 'isNewPower'"
          :key="`isNewPower-${item.prop}`"
          :label="item.label"
          :align="item.align || 'left'"
          :prop="item.prop"
          :width="item.width"
        >
          <template #default="scope">
            {{ scope.row[item.prop] ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.prop === 'schemeType'"
          :key="`schemeType-${item.prop}`"
          :label="item.label"
          :align="item.align || 'left'"
          :prop="item.prop"
          :width="item.width"
        >
          <template #default="scope">
            {{ scheme_type.find((dict) => dict.value == scope.row[item.prop])?.label }}
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="item.visible"
          :key="item.prop"
          :label="item.label"
          :align="item.align || 'left'"
          :prop="item.prop"
          :width="item.width"
        />
      </template>
      <el-table-column label="操作" align="center" width="120">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleEdit(scope.row)">
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <CommonUpload
      v-model:open="open"
      v-model:upload-loading="uploadLoading"
      @download-temp="downloadTemp"
      @upload="uploadFile"
    ></CommonUpload>
    <EditDialog
      v-model:open="editOpen"
      :detail="editData"
      :scheme-type="scheme_type"
      @update-list="handleQuery"
    ></EditDialog>
  </div>
</template>

<script setup name="SolutionTypes">
import {
  downloadTempFile,
  exportCustomerSourceScheme,
  getListData,
  importCustomerSourceScheme
} from '@/api/chargingManager/solutionTypes/index'
import CommonUpload from '@/components/CommonUpload'
import EditDialog from './components/editDialog.vue'

// 列信息
const columns = ref([
  { key: 0, label: `门店ID`, visible: true, prop: 'shopId', width: '100' },
  { key: 1, label: `门店名称`, visible: true, prop: 'shopName' },
  { key: 2, label: `是否新动力`, visible: true, prop: 'isNewPower', width: '160' },
  { key: 3, label: `输送客源费方案类型`, visible: true, prop: 'schemeType', width: '160' },
  { key: 4, label: `订单金额*%`, visible: true, prop: 'orderAmountPercent', width: '160' },
  { key: 5, label: `上限营业收入*%`, visible: true, prop: 'upperLimitPercent', width: '160' }
])
const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)
const { proxy } = getCurrentInstance()
const { scheme_type } = proxy.useDict('scheme_type')
const dateRange = ref([])
const budgetChangeList = ref([])
const loading = ref(false)
const total = ref(0)

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    schemeType: undefined,
    shopName: '',
    isNewPower: undefined
  }
})

const { queryParams } = toRefs(data)

/** 查询用户列表 */
const getList = () => {
  loading.value = true
  const params = {
    ...queryParams.value
  }

  getListData(params)
    .then((res) => {
      budgetChangeList.value = res.rows
      total.value = res.total
    })
    .finally(() => {
      loading.value = false
    })
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = []
  proxy.resetForm('queryRef')
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy.$modal
    .confirm('确认导出此报表吗？')
    .then(function () {
      const params = {
        ...queryParams.value
      }

      exportCustomerSourceScheme(params, '输送客源费方案.xls')
    })
    .catch(() => {})
}

// 导入相关
const open = ref(false)
const uploadLoading = ref(false)
const handleUpload = () => {
  open.value = true
}

const downloadTemp = () => {
  downloadTempFile(
    { saveFileName: '输送客源费导入模板.xlsx', fileName: '输送客源费导入模板.xlsx' },
    '输送客源费导入模版.xlsx'
  )
}

const uploadFile = (fileList) => {
  uploadLoading.value = true
  let formData = new FormData()
  fileList.forEach((item) => {
    formData.append('file', item.raw)
  })
  importCustomerSourceScheme(formData)
    .then(() => {
      proxy.$modal.msgSuccess('上传成功')
      open.value = false
      resetQuery()
    })
    .finally(() => {
      uploadLoading.value = false
    })
}

const editOpen = ref(false)
const editData = ref({})
const handleEdit = (row) => {
  editOpen.value = true
  editData.value = row
}

getList()
</script>

<style lang="scss" scoped>
.app-container {
  overflow: auto;

  .click-item {
    color: #409eff;
    text-decoration: underline;
  }

  .flex-row {
    display: flex;
    justify-content: space-between;
  }
}
</style>
