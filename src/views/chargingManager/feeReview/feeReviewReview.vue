<template>
  <div class="app-container">
    <div class="review-title">
      <div class="title-left">
        <span class="title-name">{{ shopInfo.shopId }} {{ shopInfo.name }}</span>
        <span class="title-status">
          <template v-for="item in charge_audit_status" :key="item.value">
            <el-tag v-if="item.value == shopInfo.status" :type="item.type">{{ item.label }}</el-tag>
          </template>
        </span>
      </div>
      <div class="title-right">
        <el-button type="danger" plain class="noborder" :disabled="shopInfo.status !== '2'" @click="handleReject">
          退回
        </el-button>
        <el-button type="primary" plain class="noborder" :disabled="shopInfo.status !== '2'" @click="setFeeAudit(0)">
          通过
        </el-button>
      </div>
    </div>

    <div v-for="item in tableList" :key="item.id">
      <p class="item-title">
        {{ item.name }}
      </p>
      <!-- :row-class-name="tableRowClassName" -->
      <el-table :data="item.feeAuditDetailList" class="table2">
        <el-table-column label="状态" prop="type" width="150">
          <template #default="scope">
            <div v-if="scope.row.type == 1">
              当前生效
            </div>
            <div v-if="scope.row.type == 2">
              修改后
            </div>
          </template>
        </el-table-column>
        <el-table-column label="生效时间" prop="effectiveTime" width="200" />
        <el-table-column label="公式说明" prop="formulaDescription" width="350" />
        <el-table-column label="类型" prop="feeType" />
        <el-table-column label="批次" prop="batch" />
        <el-table-column label="备注" prop="remark" width="150" />
        <el-table-column label="创建时间" prop="createTime" width="150" />
      </el-table>
    </div>
    <el-dialog
      v-model="reasonShow"
      title="退回"
      width="500"
    >
      <el-form ref="queryRef" :model="queryParams" :rules="rules" :inline="true" label-width="110px">
        <el-form-item label="原因" prop="reason">
          <el-input
            v-model="queryParams.reason"
            placeholder="请输入退回原因"
            :rows="3"
            type="textarea"
            style="width: 300px"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">
            取消
          </el-button>
          <el-button type="primary" @click="handleConfirm">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { feeAudit, queryListByAuditId } from '@/api/chargingManager/FeeReview/index'
import router from '@/router'
import { ElMessage, ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance()
const { charge_audit_status } = proxy.useDict('charge_audit_status')
// 接收url中的参数
const route = useRoute()
// 商户信息
const shopInfo = reactive({
  id: route.query.id,
  shopId: route.query.shopId,
  name: route.query.shopName,
  status: route.query.auditStatus
})
const tableList = ref([])// 表格数据
// 查看审核明细
const getqueryListByAuditId = () => {
  queryListByAuditId(shopInfo.id).then((res) => {
    console.log(res)
    if (res.code === 200) {
      if(res.data && res.data.length > 0){
        tableList.value = res.data
      }else{
        ElMessage({
          type: 'warning',
          message: '没有需要审批的数据!'
        })
      }
    } else {
      ElMessage({
        type: 'error',
        message: res.msg
      })
    }
  })
}
getqueryListByAuditId()

// 显示退回窗口
const handleReject = () => {
  reasonShow.value = true
}

const setFeeAudit = (num) => {
  ElMessageBox.confirm(`审核${['通过', '退回'][num]}该门店计费规则。`, ['通过', '退回'][num], {
    type: 'warning'
  })
    .then(() => {
      const params = {
        id: shopInfo.id, // 门店id
        auditStatus: num, // 审核状态 0:通过 1:退回
        rejectionReason: queryParams.value.reason // 退回原因
      }
      feeAudit(params).then((res) => {
      // console.log(res)
        if (res.code === 200) {
          ElMessage({
            type: 'success',
            message: res.msg
          })
          // 成功后退回到列表页
          router.go(-1)
        } else {
          ElMessage({
            type: 'error',
            message: res.msg
          })
        }
      }).finally(() => {
        reasonShow.value = false
        queryParams.value.reason = ''
      })
    })
    .catch(() => {
    })
}

const rules = ref({
  reason: [
    { required: true, message: '请输入退回原因', trigger: 'blur' }
  ]
})

const queryParams = ref({
  reason: ''
})
const reasonShow = ref(false)
const handleCancel = () => {
  reasonShow.value = false
  proxy.$refs.queryRef.resetFields()
}

const handleConfirm = () => {
  proxy.$refs.queryRef.validate((valid) => {
    if (valid) {
      setFeeAudit(1)
    }
  })
}

</script>
<style>
.table2 tbody tr:last-child td {
  /* background-color: #efefef!important; */
  color:#409EFF;
  font-weight: bold;
}
.old-row {
  opacity: 0.5;
}

</style>
<style lang="scss" scoped>
.review-title {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title-left {
    font-size: 16px;
    
    .title-name {
      font-weight: 600;
      padding-right: 20px;
    }

    .title-status {
      color: #F56C6C;
    }
  }
}

.item-title {
  font-size: 16px;
  font-weight: 600;
  margin-top: 20px;
}
</style>