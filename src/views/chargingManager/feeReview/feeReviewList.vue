<template>
  <div class="app-container">
    <el-form ref="queryRef" :model="queryParams" :inline="true" label-width="110px">
      <el-form-item label="门店" prop="shopId">
        <el-input
          v-model="queryParams.shopName"
          placeholder="请输入门店编码/门店名称"
          clearable
          style="min-width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计费状态" prop="auditStatus">
        <el-select
          v-model="queryParams.auditStatus"
          placeholder="请选择计费状态"
          clearable
          class="w-240-important"
        >
          <el-option
            v-for="item in charge_audit_status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
      </el-form-item>
    </el-form>
    <el-table
      ref="tableContainer"
      v-loading="loading"
      :data="budgetChangeList"
      :height="tableHeight"
      class="mt-15"
    >
      <el-table-column type="index" label="序号" width="50" align="left" />
      <template v-for="item in columns" :key="item.key">
        <el-table-column
          v-if="item.visible"
          :key="item.prop"
          :label="item.label"
          :align="item.align || 'left'"
          :prop="item.prop"
          :min-width="item.width"
        />
      </template>
      <el-table-column label="操作" width="100" align="left" fixed="right">
        <template #default="scope">
          <!-- 审核状态完成 已通过 已驳回状态 审核变为查看 -->
          <el-button
            v-if="['待审核'].includes(scope.row.auditStatus)"
            link
            icon="Edit"
            class="noborder"
            type="primary"
            @click="handleReview(scope.row)"
          >
            审核
          </el-button>
          <el-button
            v-else
            link
            icon="View"
            class="noborder"
            type="info"
            @click="handleReview(scope.row)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- v-show="total > 0" -->
    <pagination
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="FeeReviewList">
import {
  queryList // 查询-门店计费规则审核
} from '@/api/chargingManager/FeeReview'

const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)

const { proxy } = getCurrentInstance()
const { charge_audit_status } = proxy.useDict('charge_audit_status')

// 列信息
const columns = ref([
  { key: 0, label: `门店ID`, visible: true, prop: 'shopId', width: '100' },
  { key: 1, label: `门店名称`, visible: true, prop: 'shopName', width: '250' },
  { key: 2, label: `开业日期`, visible: true, prop: 'startDate', width: '160' },
  // { key: 3, label: `操作日期`, visible: true, prop: 'updateTime', width: '160' },
  // { key: 4, label: `操作人`, visible: true, prop: 'updateBy', width: '160' },
  { key: 5, label: `审核日期`, visible: true, prop: 'auditDate', width: '160' },
  { key: 6, label: `审核人`, visible: true, prop: 'auditUser', width: '160' },
  { key: 7, label: `计费审核状态`, visible: true, prop: 'auditStatus', width: '160' },
  { key: 8, label: `退回原因`, visible: true, prop: 'rejectionReason', width: '250' }
])

const budgetChangeList = ref([])
const loading = ref(false)
const total = ref(0)

const data = reactive({
  queryParams: {
    shopName: '', // 门店号或门店名称
    auditStatus: '', // 审核状态
    pageNum: 1, // 当前页码
    pageSize: 20 // 每页条数
  }
})

const { queryParams } = toRefs(data)

/** 查询用户列表 */
const getList = () => {
  loading.value = true
  const params = {
    ...queryParams.value
  }
  queryList(params)
    .then((res) => {
      let obj = res.rows.map((item) => {
        if (item.auditStatus != '0') {
          item._auditStatus = item.auditStatus
          item.auditStatus = charge_audit_status.value.find((v) => v.value == item.auditStatus)[
            'label'
          ]
        }
        return item
      })

      budgetChangeList.value = obj
      total.value = res.total
    })
    .finally(() => {
      loading.value = false
    })
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm('queryRef')
}

const handleReview = (row) => {
  console.log(row)
  proxy.$router.push({
    path: '/chargingManager/review',
    query: {
      id: row.id,
      shopId: row.shopId,
      shopName: row.shopName,
      auditStatus: row._auditStatus
    }
  })
}

getList()
</script>

<style lang="scss" scoped>
.app-container {
  overflow: auto;

  .click-item {
    color: #409eff;
    text-decoration: underline;
  }
}
</style>
