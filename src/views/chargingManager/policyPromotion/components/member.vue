<template>
  <div class="member">
    <el-form ref="queryRef" :model="formData" :rules="rules" :inline="true" label-width="110px">
      <el-form-item label="推广政策">
        <el-input
          v-model="formData.name"
          placeholder="请输入推广政策"
          style="width: 300px"
          disabled
        />
      </el-form-item>
      <el-form-item label="选择时间" prop="timeType">
        <el-select
          v-model="formData.timeType"
          placeholder="请选择时间"
          style="width: 300px"
          :disabled="!isEdit"
        >
          <el-option
            label="长期"
            value="长期"
          />
          <el-option
            label="每年固定月份"
            value="每年固定月份"
          />
          <el-option
            label="固定日期"
            value="固定日期"
          />
        </el-select>

        <el-date-picker
          v-if="formData.timeType && formData.timeType !== '长期'"
          v-model="formData.date"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :disabled="!isEdit"
          style="width: 300px; margin-left: 12px; flex-grow: revert"
        />

        <el-select
          v-if="formData.timeType === '每年固定月份'"
          v-model="formData.month"
          placeholder="选择月份"
          multiple
          style="width: 260px; margin-left: 12px"
          :disabled="!isEdit"
        >
          <el-option
            v-for="item in 12"
            :key="item"
            :label="item + '月'"
            :value="item"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="条件" prop="groupPolicyJson">
        <div class="condition-box">
          <div v-for="(item, index) in formData.groupPolicyJson" :key="index" class="condition-item">
            <el-button
              v-if="index && isEdit"
              class="del-btn"
              type="danger"
              icon="Delete"
              circle
              @click="handleDelBrand(index)"
            />
            <div class="item-title">
              <span class="title-label">品牌选择</span>
              <el-select
                v-model="item.brandName"
                placeholder="请选择酒店品牌"
                clearable
                multiple
                style="width: 240px"
                :disabled="!isEdit"
              >
                <el-option
                  v-for="brand in brandList"
                  :key="brand"
                  :label="brand"
                  :value="brand"
                  :disabled="getDisbaled(brand, index)"
                />
              </el-select>
            </div>

            <div class="item-content">
              <div v-for="(line, i) in item.groupPolicyMemberList" :key="item.brandName + i" class="content-line">
                <span style="display: inline-block; width: 65px; text-align: right;">售卡金额 [</span>
                <el-input
                  v-model="line.cardAmountMin"
                  style="width: 100px; margin: 0 10px"
                  placeholder="0"
                  :disabled="!isEdit"
                  @blur="handleChange(line, 'cardAmountMin')"
                >
                </el-input>
                <span> - </span>
                <el-input
                  v-model="line.cardAmountMax"
                  style="width: 100px; margin: 0 10px"
                  placeholder="无限大"
                  :disabled="!isEdit"
                  @blur="handleChange(line, 'cardAmountMax')"
                >
                </el-input>
                <span> ）</span>

                <el-input
                  v-model="line.discountPercent"
                  style="width: 240px; margin-left: 12px;"
                  :disabled="!isEdit"
                  @blur="handleChange(line, 'discountPercent')"
                >
                  <template #prepend>
                    减免额度
                  </template>
                  <template #append>
                    %
                  </template>
                </el-input>

                <div v-if="isEdit" class="btn-group">
                  <el-button type="primary" :icon="Plus" circle plain @click="handleAddItem(item)" />
                  <el-button
                    v-if="i" type="danger" :icon="Delete" plain circle
                    @click="handleDelItem(item, i)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <el-button class="edit-btn" type="primary" :icon="isEdit ? 'Check': 'Edit'" :loading="submitLoading" @click="handleEdit">
      {{ isEdit ? '提交' : '编辑' }}
    </el-button>

    <el-button
      v-if="isEdit" class="add-btn" type="primary" circle
      @click="handleAddBrand"
    >
      <el-icon><Plus /></el-icon>
      <span>添加品牌</span>
    </el-button>
  </div>
</template>

<script setup>
import {
  getBrandList,
  queryList,
  saveData
} from '@/api/chargingManager/policyPromotion/index'
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const formData = ref({
  name: '会员管理费减免',
  timeType: '',
  date: [],
  startTime: '',
  endTime: '',
  month: [],
  groupPolicyJson: [
    {
      brandName: [],
      groupPolicyMemberList: [
        {
          cardAmountMin: '',
          cardAmountMax: '',
          discountPercent: ''
        }
      ]
    }
  ]

})
const rules = ref({
  groupPolicyJson: [
    { required: true, message: '请选择品牌', trigger: 'change' }
  ],
  timeType: [
    { required: true, message: '请选择时间', trigger: 'change' }
  ]
})

const handleChange = (row, prop) => {
  if (!/^-?\d+(\.\d+)?$/.test(row[prop])) {
    row[prop] = 0
  }
  if (row[prop]) {
    row[prop] = parseInt(row[prop] * 100) / 100
  } 
}

const getDisbaled = (brand, index) => {
  const arr = formData.value.groupPolicyJson.filter((data, i) => i !== index).map((item) => item.brandName)
  return arr.some((item) => item.includes(brand))
}
const brandList = ref([])
const getBrand = () => {
  getBrandList({}).then((res) => {
    if (res.code === 200) {
      brandList.value = res.data
    }
  })
}

const getData = () => {
  queryList({ type: 2 }).then((res) => {
    if (res.code === 200) {
      formData.value = {
        ...res.data,
        name: '会员管理费减免',
        date: [res.data.startTime, res.data.endTime]
      }
    }
  })
}

const isEdit = ref(false)

// const handleClearBrand = (index) => {
//   formData.value.groupPolicyJson[index] = {
//     brandName: [],
//     groupPolicyMemberList: [
//       {
//         cardAmountMin: '',
//         cardAmountMax: '',
//         discountPercent: ''
//       }
//     ]
//   }
// }
const validateData = () => {
  console.log(formData.value)
  console.log(formData.value.date)
  if (formData.value.timeType !== '长期' && !formData.value.date?.length) {
    return false
  }

  if (formData.value.timeType === '每年固定月份' && !formData.value.month?.length) {
    return false
  }

  if (!formData.value.groupPolicyJson.length) {
    return false
  }

  for(let i = 0; i < formData.value.groupPolicyJson.length; i++) {
    const item = formData.value.groupPolicyJson[i]
    // 未选择品牌
    if (!item.brandName.length) {
      return false
    }

    // 未添加售卡金额
    if (!item.groupPolicyMemberList.length) {
      return false
    }

    for(let j = 0; j < item.groupPolicyMemberList.length; j++) {
      const detail = item.groupPolicyMemberList[j]
      // 未填写减免额度
      if (!detail.discountPercent) {
        return false
      }

      // 售卡金额左侧未填写
      if (!detail.cardAmountMin && detail.cardAmountMin !== 0) {
        return false
      }

      // 售卡金额右侧未填写
      if (!detail.cardAmountMax && j !== item.groupPolicyMemberList.length - 1) {
        return false
      }
    }
  }

  return true
}
const submitLoading = ref(false)
const handleEdit = () => {
  if (isEdit.value) {
    if (!validateData()) {
      ElMessage.error('请填写完整信息')
      return
    }

    submitLoading.value = true
    if (formData.value.timeType !== '长期') {
      formData.value.startTime = formData.value.date[0]
      formData.value.endTime = formData.value.date[1]
      formData.value.month = formData.value.timeType === '每年固定月份' ? formData.value.month : []
    } else {
      formData.value.startTime = ''
      formData.value.endTime = ''
      formData.value.month = []
    }

    saveData(formData.value).then((res) => {
      if (res.code === 200) {
        ElMessage.success('保存成功')
        getData()
        isEdit.value = !isEdit.value
      }
    }).finally(() => {
      submitLoading.value = false
    })
  } else {
    isEdit.value = !isEdit.value
  }
}
const handleAddBrand = () => {
  formData.value.groupPolicyJson.push({
    brandName: [],
    groupPolicyMemberList: [
      {
        cardAmountMin: '',
        cardAmountMax: '',
        discountPercent: ''
      }
    ]
  })
}
const handleDelBrand = (index) => {
  formData.value.groupPolicyJson.splice(index, 1)
}

const handleAddItem = (item) => {
  item.groupPolicyMemberList.push({
    cardAmountMin: '',
    cardAmountMax: '',
    discountPercent: ''
  })
}
const handleDelItem = (item, index) => {
  item.groupPolicyMemberList.splice(index, 1)
}

getBrand()
getData()
</script>

<style lang="scss">
.member {
  position: relative;
  overflow: auto;

  .edit-btn {
    position: absolute;
    top: 0;
    right: 0;
  }

  .add-btn {
    position: fixed;
    bottom: 50px;
    right: 50px;
    width: 70px;
    height: 70px;
    z-index: 10;

    span {
      display: flex;
      flex-direction: column;
      align-items: center;

      .el-icon {
        font-size: 18px;
        color: #fff;
      }

      span {
        padding-top: 5px;
        font-size: 12px;
        text-align: center;
      }
    }
  }

  .el-form-item {
    width: 1000px;
  }

  .condition-box {
    width: 100%;

    .condition-item {
      position: relative;
      background: #eee;
      border-radius: 12px;
      padding: 12px;
      margin-bottom: 12px;

      .del-btn {
        position: absolute;
        top: 8px;
        right: 12px;
      }

      .item-title {
        display: flex;
        align-items: center;

        .title-label {
          padding-right: 12px;
        }
      }

      .content-line {
        padding-top: 12px;
        display: flex;
        align-items: center;

        .btn-group {
          padding-left: 12px;
          
          .el-button {
            width: 24px;
            height: 24px;
          }
        }
      }
    }
  }
}
</style>