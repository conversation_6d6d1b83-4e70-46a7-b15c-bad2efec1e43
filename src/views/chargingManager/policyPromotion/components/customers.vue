<template>
  <div class="customers">
    <el-form ref="queryRef" :model="formData" :rules="rules" :inline="true" label-width="110px">
      <el-form-item label="推广政策" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入推广政策"
          style="width: 300px"
          disabled
        />
      </el-form-item>
      <el-form-item label="条件" prop="groupPolicyJson">
        <div class="condition-box">
          <div v-for="(item, index) in formData.groupPolicyJson" :key="index" class="condition-item">
            <el-button
              v-if="index && isEdit"
              class="del-btn"
              type="danger"
              icon="Delete"
              circle
              @click="handleDelBrand(index)"
            />
            <div class="item-title">
              <span class="title-label">品牌选择</span>
              <el-select
                v-model="item.brandName"
                placeholder="请选择酒店品牌"
                clearable
                multiple
                style="width: 240px"
                :disabled="!isEdit"
              >
                <el-option
                  v-for="brand in brandList"
                  :key="brand"
                  :label="brand"
                  :value="brand"
                  :disabled="getDisbaled(brand, index)"
                />
              </el-select>
            </div>

            <div class="item-content">
              <div v-for="(room, roomI) in item.groupPolicyRoomList" :key="'room' + index + roomI" class="item-room">
                <div class="content-line">
                  <span style="display: inline-block; width: 100px; text-align: right;">房间（</span>
                  <el-input
                    v-model="room.roomNumMin"
                    style="width: 100px; margin: 0 10px"
                    placeholder="0"
                    :disabled="!isEdit"
                    @blur="handleChange(room, 'roomNumMin')"
                  >
                  </el-input>
                  <span> - </span>
                  <el-input
                    v-model="room.roomNumMax"
                    style="width: 100px; margin: 0 10px"
                    placeholder="无限大"
                    :disabled="!isEdit"
                    @blur="handleChange(room, 'roomNumMax')"
                  >
                  </el-input>
                  <span> ]</span>
                  <div v-if="isEdit" class="btn-group">
                    <el-button type="primary" :icon="Plus" circle plain @click="handleAddRoom(item)" />
                    <el-button
                      v-if="roomI" type="danger" :icon="Delete" plain circle
                      @click="handleDelRoom(item, roomI)"
                    />
                  </div>
                </div>
                <div v-for="(line, i) in room.groupPolicyDetailList" :key="'line' + index + roomI + i" class="content-line">
                  <span style="display: inline-block; width: 100px; text-align: right;">售卡金额 [</span>
                  <el-input
                    v-model="line.cardAmountMin"
                    style="width: 100px; margin: 0 10px"
                    placeholder="0"
                    :disabled="!isEdit"
                    @blur="handleChange(line, 'cardAmountMin')"
                  >
                  </el-input>
                  <span> - </span>
                  <el-input
                    v-model="line.cardAmountMax"
                    style="width: 100px; margin: 0 10px"
                    placeholder="无限大"
                    :disabled="!isEdit"
                    @blur="handleChange(line, 'cardAmountMax')"
                  >
                  </el-input>
                  <span> ）</span>
                  
                  <el-input
                    v-model="line.discountPercent"
                    style="width: 240px; margin-left: 12px;"
                    :disabled="!isEdit"
                    @blur="handleChange(line, 'discountPercent')"
                  >
                    <template #prepend>
                      减免额度
                    </template>
                    <template #append>
                      %
                    </template>
                  </el-input>

                  <div v-if="isEdit" class="btn-group">
                    <el-button type="primary" :icon="Plus" circle plain @click="handleAddItem(room)" />
                    <el-button
                      v-if="i" type="danger" :icon="Delete" plain circle
                      @click="handleDelItem(room, i)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <el-button class="edit-btn" type="primary" :icon="isEdit ? 'Check': 'Edit'" :loading="submitLoading" @click="handleEdit">
      {{ isEdit ? '提交' : '编辑' }}
    </el-button>

    <el-button
      v-if="isEdit" class="add-btn" type="primary" circle
      @click="handleAddBrand"
    >
      <el-icon><Plus /></el-icon>
      <span>添加品牌</span>
    </el-button>
  </div>
</template>

<script setup>
import {
  getBrandList,
  queryList,
  saveData
} from '@/api/chargingManager/policyPromotion/index'
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const formData = ref({
  name: '输送客源费减免（1.0+2.0）',
  groupPolicyJson: [
    {
      brandName: [],
      roomNumMin: '',
      roomNumMax: '',
      groupPolicyRoomList: [
        {
          roomNumMin: '',
          roomNumMax: '',
          groupPolicyDetailList: [
            {
              cardAmountMin: '',
              cardAmountMax: '',
              discountPercent: ''
            }
          ]
        }
      ]
    }
  ]
})
const rules = ref({
  groupPolicyJson: [
    { required: true, message: '请选择品牌', trigger: 'change' }
  ]
})

const handleChange = (row, prop) => {
  if (!/^-?\d+(\.\d+)?$/.test(row[prop])) {
    row[prop] = 0
  }
  if (row[prop]) {
    row[prop] = parseInt(row[prop] * 100) / 100
  } 
}

const getDisbaled = (brand, index) => {
  const arr = formData.value.groupPolicyJson.filter((data, i) => i !== index).map((item) => item.brandName)
  return arr.some((item) => item.includes(brand))
}

const brandList = ref([])
const getBrand = () => {
  getBrandList({}).then((res) => {
    if (res.code === 200) {
      brandList.value = res.data
    }
  })
}

const getData = () => {
  queryList({ type: 1 }).then((res) => {
    if (res.code === 200) {
      formData.value = {
        ...res.data,
        name: '输送客源费减免（1.0+2.0）'
      }
    }
  })
}

const isEdit = ref(false)

// const handleClearBrand = (index) => {
//   formData.value.groupPolicyJson[index] = {
//     brandName: [],
//     roomNumMin: '',
//     roomNumMax: '',
//     groupPolicyRoomList: [
//       {
//         roomNumMin: '',
//         roomNumMax: '',
//         groupPolicyDetailList: [
//           {
//             cardAmountMin: '',
//             cardAmountMax: '',
//             discountPercent: ''
//           }
//         ]
//       }
//     ]
//   }
// }
const validateData = () => {
  if (!formData.value.groupPolicyJson.length) {
    return false
  }

  for(let i = 0; i < formData.value.groupPolicyJson.length; i++) {
    const item = formData.value.groupPolicyJson[i]
    // 未选择品牌
    if (!item.brandName.length) {
      return false
    }

    // 未添加房间数量
    if (!item.groupPolicyRoomList.length) {
      return false
    }

    for(let j = 0; j < item.groupPolicyRoomList.length; j++) {
      const room = item.groupPolicyRoomList[j]
      // 未添加售卡金额
      if (!room.groupPolicyDetailList.length) {
        return false
      }

      // 房间左侧未填写
      if (!room.roomNumMin && room.roomNumMin !== 0) {
        return false
      }

      // 房间右侧未填写
      if (!room.roomNumMax && j !== item.groupPolicyRoomList.length - 1) {
        return false
      }

      for(let k = 0; k < room.groupPolicyDetailList.length; k++) {
        const detail = room.groupPolicyDetailList[k]
        // 未填写减免额度
        if (!detail.discountPercent) {
          return false
        }

        // 售卡金额左侧未填写
        if (!detail.cardAmountMin && detail.cardAmountMin !== 0) {
          return false
        }

        // 售卡金额右侧未填写
        if (!detail.cardAmountMax && k !== room.groupPolicyDetailList.length - 1) {
          return false
        }
      }
    }
  }

  return true
}
const submitLoading = ref(false)
const handleEdit = () => {
  if (isEdit.value) {
    if (!validateData()) {
      ElMessage.error('请填写完整信息')
      return
    }

    submitLoading.value = true
    saveData(formData.value).then((res) => {
      if (res.code === 200) {
        ElMessage.success('保存成功')
        getData()
        isEdit.value = !isEdit.value
      }
    }).finally(() => {
      submitLoading.value = false
    })
  } else {
    isEdit.value = !isEdit.value
  }
}

// 新增品牌
const handleAddBrand = () => {
  formData.value.groupPolicyJson.push({
    brandName: [],
    roomNumMin: '',
    roomNumMax: '',
    groupPolicyRoomList: [
      {
        roomNumMin: '',
        roomNumMax: '',
        groupPolicyDetailList: [
          {
            cardAmountMin: '',
            cardAmountMax: '',
            discountPercent: ''
          }
        ]
      }
    ]
  })
}
const handleDelBrand = (index) => {
  formData.value.groupPolicyJson.splice(index, 1)
}

// 新增房间数量
const handleAddRoom = (item) => {
  item.groupPolicyRoomList.push({
    roomNumMin: '',
    roomNumMax: '',
    groupPolicyDetailList: [
      {
        cardAmountMin: '',
        cardAmountMax: '',
        discountPercent: ''
      }
    ]
  })
}

const handleDelRoom = (item, index) => {
  item.groupPolicyRoomList.splice(index, 1)
}

// 新增售卡金额
const handleAddItem = (item) => {
  item.groupPolicyDetailList.push({
    cardAmountMin: '',
    cardAmountMax: '',
    discountPercent: ''
  })
}
const handleDelItem = (item, index) => {
  item.groupPolicyDetailList.splice(index, 1)
}

getBrand()
getData()
</script>

<style lang="scss">
.customers {
  position: relative;
  overflow: auto;

  .edit-btn {
    position: absolute;
    top: 0;
    right: 0;
  }

  .add-btn {
    position: fixed;
    bottom: 50px;
    right: 50px;
    width: 70px;
    height: 70px;
    z-index: 10;

    span {
      display: flex;
      flex-direction: column;
      align-items: center;

      .el-icon {
        font-size: 18px;
        color: #fff;
      }

      span {
        padding-top: 5px;
        font-size: 12px;
        text-align: center;
      }
    }
  }

  .el-form-item {
    width: 1000px;
  }

  .condition-box {
    width: 100%;

    .condition-item {
      position: relative;
      background: #eee;
      border-radius: 12px;
      padding: 12px;
      margin-bottom: 12px;

      .item-room {
        margin-top: 12px;
        border-top: 1px solid #fff;
      }

      .del-btn {
        position: absolute;
        top: 8px;
        right: 12px;
      }

      .item-title {
        display: flex;
        align-items: center;

        .title-label {
          padding-right: 12px;
        }
      }

      .content-line {
        padding-top: 12px;
        display: flex;
        align-items: center;

        .btn-group {
          padding-left: 12px;
          
          .el-button {
            width: 24px;
            height: 24px;
          }
        }
      }
    }
  }
}
</style>