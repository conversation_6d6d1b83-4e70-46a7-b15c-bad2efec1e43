<template>
  <div class="app-container">
    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane label="输送客源费减免" name="first">
        <Customers></Customers>
      </el-tab-pane>
      <el-tab-pane label="会员管理费减免" name="second">
        <Member></Member>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="PolicyPromotion">
import Customers from './components/customers.vue'
import Member from './components/member.vue'

const activeName = ref('first')
</script>