<template>
  <div class="app-container">
    <div class="flex flex-jus-end mb-15">
      <el-button type="primary" icon="Check" @click="submit">
        确定
      </el-button>
      <el-button type="primary" icon="Close" plain @click="cancel">
        取消
      </el-button>
    </div>
    <el-form
      :model="queryForm"
      label-width="auto"
      style="max-width: 700px; padding-left: 15px"
      @keydown.enter="searchForm(-1)"
    >
      <el-form-item label="门店名称">
        <el-input v-model="combinedValue" :disabled="true" />
      </el-form-item>
      <el-form-item label="开始账期">
        <el-date-picker
          v-model="queryForm.atime"
          type="month"
          placeholder="请选择账期"
          style="width: 100%"
          :shortcuts="shortcutsNowLong"
          :clearable="false"
          :disabled="true"
          @change="handleChangeMonth"
        />
      </el-form-item>
      <el-form-item label="是否出具舞弊账单">
        <el-switch v-model="queryForm.cheatBill" />
      </el-form-item>
      <el-form-item label="商标使用费" style="margin-bottom: 5px">
        <el-radio-group v-model="queryForm.feeType">
          <el-radio value="0">
            房量
          </el-radio>
          <el-radio value="1">
            营业收入
          </el-radio>
          <el-radio value="2">
            两者取其高
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label=" ">
        <div class="sbsyf">
          <div
            v-if="queryForm.feeType == '0'"
            class="flex flex-col"
            style="text-wrap: nowrap"
          >
            房间数量<span class="c-red">*</span>
            <el-input
              v-model="queryForm.calValue"
              type="text"
              style="margin: 0 10px"
              @input="
                (value) => handleMinusAmountInput(value, queryForm, 'calValue')
              "
              @change="
                (value) => {
                  if (!value) queryForm.calValue = '';
                }
              "
            />
            元/间/月
          </div>
          <div v-else-if="queryForm.feeType == '1'" class="flex flex-col" style="text-wrap: nowrap">
            营业收入<span class="c-red">*</span>
            <el-input
              v-model="queryForm.calValueYs"
              type="text"
              style="margin: 0 10px"
              @input="
                (value) => handleMinusAmountInput(value, queryForm, 'calValueYs')
              "
              @change="
                (value) => {
                  if (!value) queryForm.calValueYs = '';
                }
              "
            />
            %
          </div>
          <div
            v-else
            style="text-wrap: nowrap"
          >
            <div class="flex flex-col">
              营业收入<span class="c-red">*</span>
              <el-input
                v-model="queryForm.calValueYs"
                type="text"
                style="margin: 0 10px"
                @input="
                  (value) => handleMinusAmountInput(value, queryForm, 'calValueYs')
                "
                @change="
                  (value) => {
                    if (!value) queryForm.calValueYs = '';
                  }
                "
              />
              %
            </div>
            <div class="flex flex-col mt-20">
              房间数量<span class="c-red">*</span>
              <el-input
                v-model="queryForm.calValue"
                type="text"
                style="margin: 0 10px"
                @input="
                  (value) => handleMinusAmountInput(value, queryForm, 'calValue')
                "
                @change="
                  (value) => {
                    if (!value) queryForm.calValue = '';
                  }
                "
              />
              元/间/月
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item
        label="附件"
        class="fujian"
        :class="fileList.length >= 5 || fujianLock ? 'nobtn' : ''"
      >
        <el-dialog v-model="dialogVisible">
          <img
            :src="dialogImageUrl"
            alt="Preview Image"
            style="display: block; width: 100%; height: 100%"
          >
        </el-dialog>
        <el-upload
          v-model:file-list="fileList"
          :action="VITE_APP_BASE_API + '/shop/file/uploadImg'"
          :headers="{ Authorization: 'Bearer ' + token }"
          multiple
          :on-change="handleChange"
          :on-remove="handleRemove"
          :on-exceed="handleExceed"
          :on-preview="handlePreview"
          :on-success="handleSuccess"
          :limit="5"
          accept=".jpg, .png, .bmp"
          :auto-upload="true"
          :file-size="1024 * 1024 * 2"
          list-type="picture-card"
        >
          <template #trigger>
            <div class="flex flex-column">
              <el-icon size="20" color="#999">
                <Plus />
              </el-icon>
              <div style="color: #999; font-size: 12px">
                点击选择图片
              </div>
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <div>操作记录</div>
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      class="mt-15"
      style="width: 100%"
    >
      <el-table-column prop="no" label="序号" min-width="50">
        <template #default="scope">
          <div style="text-wrap: nowrap">
            {{
              (paginationInfo.currentPage - 1) * paginationInfo.pageSize +
                scope.$index +
                1
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="operateDate"
        label="操作日期"
        min-width="250"
      ></el-table-column>
      <el-table-column
        prop="operateUser"
        label="操作人"
        min-width="150"
      ></el-table-column>
      <el-table-column
        prop="calDesc"
        label="商标使用费规则"
        min-width="250"
      ></el-table-column>
      <el-table-column prop="imgUrls" label="附件" min-width="250">
        <template #default="scope">
          <el-text type="danger"></el-text>
          <el-text
            v-if="!scope.row.imgUrls || scope.row.imgUrls.length <= 0"
            class="mx-1"
            type="danger"
          >
            没有附件
          </el-text>
          <div v-else>
            <el-image
              v-for="(item, index) in scope.row.url2"
              :key="index"
              style="width: 40px; height: 40px"
              :src="item"
              :zoom-rate="1.2"
              :preview-src-list="scope.row.url2"
              :initial-index="index"
              :preview-teleported="true"
              class="mr-5"
              :z-index="999999"
            >
            </el-image>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 右对齐 -->
    <el-pagination
      v-if="false"
      class="flex flex-jus-end mt-15"
      background
      :current-page="paginationInfo.currentPage"
      :page-sizes="paginationInfo.pageSizes"
      :page-size="paginationInfo.pageSize"
      :layout="paginationInfo.layout"
      :total="paginationInfo.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
  </div>
</template>
<script setup name="FraudulentShopDetail">
// 舞弊门店详情

const VITE_APP_BASE_API = import.meta.env.VITE_APP_BASE_API
import { getToken } from '@/utils/auth'
// 获取当前token
const token = getToken()
// 获取域名
// const domain = window.location.origin
import {
  //   uploadImg, // 图片上传接口
  downloadImg // 图片下载接口
  , // 舞弊规则编辑记录查询接口
  editDesc,
  getCheatDetail, // 舞弊规则详情查询接口
  getCheatEditList
} from '@/api/fraudulentBillingManagement/index'
import tab from '@/plugins/tab.js'
import { paginationInfoConstant, shortcutsNowLong } from '@/utils/constants.js'
import { formatYearMonth, handleMinusAmountInput } from '@/utils/script.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted } from 'vue'
const route = useRoute()
const message = useMessage()
const tableLoading = ref(false)

// 搜索表单的引用格式
const formInterface = {
  shopName: '', // 门店名称
  atime: new Date(9999, 11, 31), // 账期
  cheatBill: false, // 是否出具舞弊账单
  feeType: '0', // 房量
  calValue: '', // 房间数量
  calValueYs:''
}
const detail = ref({})
// 搜索条件
const queryForm = reactive(Object.assign({}, formInterface))

const combinedValue = computed(() => {
  return `${detail.value.shopName} (${detail.value.shopId})`
})

// 时间为空则默认9999年12月31日
const handleChangeMonth = (val) => {
  if (val == null || val == undefined) {
    queryForm.atime = new Date(9999, 11, 31)
  }
}

// 附件读取锁
const fujianLock = ref(true)

onMounted(() => {
  getCheatDetail(route.query.id).then(async (res) => {
    detail.value = res.data
    for (let item in queryForm) {
      if (res.data[item] != null && res.data[item] != undefined) {
        queryForm[item] = res.data[item]
        if (item == 'atime') {
          queryForm.atime = new Date(res.data.atime)
        }
        if (item == 'cheatBill') {
          queryForm.cheatBill = res.data.cheatBill == '1'
        }
        if(item == 'calValue'){
          queryForm.calValue = !queryForm.calValue ? '' : queryForm.calValue
        }
        if(item == 'calValueYs'){
          queryForm.calValueYs = !queryForm.calValueYs ? '' : queryForm.calValueYs
        }
      }
    }
    fujianLock.value = res.data.imgUrls?.length >= 5
    if (
      res.data.imgUrls &&
      res.data.imgUrls.length > 0 &&
      res.data.imgUrls[0] != 'null'
    ) {
      // console.log(res.data.imgUrls);
      // console.log(fileList.value);
      for (let item of res.data.imgUrls) {
        let res = await downloadImg(item)
        const blob = new Blob([res])
        // 加载完后释放 URL
        fileList.value.push({
          name: `pic_${Math.floor(Math.random() * 100000) + 1}`,
          url: URL.createObjectURL(blob),
          uploadUrl: item
        })
        // console.log(fileList.value)
      }
      // fileList.value = res.data.imgUrls;
    }
  })
  searchForm(-1) // 默认查询第一页数据
})
// 表格数据
const tableData = ref([])

// 分页信息
const paginationInfo = reactive({
  ...structuredClone(paginationInfoConstant)
})
// 每页条数改变
const handleSizeChange = (val) => {
  paginationInfo.pageSize = val
  searchForm()
}
// 当前页码改变
const handleCurrentChange = (val) => {
  paginationInfo.currentPage = val
  searchForm()
}
// 搜索数据
const searchForm = () => {
  getCheatEditList({ id: route.query.id }).then((res) => {
    tableData.value = res.data
    tableData.value.map(async (item) => {
      // console.log(item.imgUrls)
      item.url2 = []
      if (
        item.imgUrls &&
        item.imgUrls.length > 0 &&
        item.imgUrls[0] != 'null'
      ) {
        for (let img of item.imgUrls) {
          let res = await downloadImg(img)
          const blob = new Blob([res], { type: 'image/jpeg' })
          item.url2.push(URL.createObjectURL(blob))
          // console.log(item)
        }
      }
    })

    // paginationInfo.total = res.data.totalCount;
  })
}

const fileList = ref([])
const handleRemove = (file, uploadFiles) => {
  console.log(file, uploadFiles)
  fujianLock.value = false
}
const handleChange = (file) => {
  fujianLock.value = false
  // console.log('change',file);

  const isLt2M = file.size < 2 * 1024 * 1024
  // 如果文件类型不是 png jpg bmp 则不进行上传
  if (!isLt2M || !file.raw.type.match(/png|jpg|bmp/)) {
    ElMessage.error(
      '仅支持上传.jpg/.png/.bmp格式文件，单个文件不超过2mb，最多上传5个文件'
    )
    // 移除文件
    setTimeout(() => {
      //   console.log("移除文件", fileList);
      fileList.value = fileList.value.filter((item) => item.uid !== file.uid)
    }, 10)
  }
  return isLt2M
}
const handleSuccess = (res, file) => {
  file.uploadName = res.data.name
  file.uploadUrl = res.data.url
}
// 预览图片
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const handlePreview = (file) => {
  dialogImageUrl.value = file.url
  dialogVisible.value = true
}
const handleExceed = (files, uploadFiles) => {
  ElMessage.warning('超出限制，最多上传5个文件')
}

// // 图片下载
// const downloadImgFucn = ()=>{
//     const params = {
//         fileName: '',
//         saveFileName: ''
//     }
//     download('/shop/file/downloadImg', params, `附件_${new Date().getTime()}.xlsx`)
// }

// 取消
const cancel = () => {
  ElMessageBox.confirm('是否要取消？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 跳转到列表页
      const obj = { path: '/fraudulentBillingManagement/fraudulentShopList' }
      tab.closeOpenPage(obj)
    })
    .catch(() => {
      // 用户取消操作
    })
}
const submit = () => {
  if( ['1', '2'].includes(queryForm.feeType) && queryForm.calValueYs == ''){
    message.error('请填写营业收入')
    return false
  }
  if( ['0', '2'].includes(queryForm.feeType) && queryForm.calValue == ''){
    message.error('请填写房间数量')
    return false
  }
  ElMessageBox.confirm('确定要提交吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      let params = { ...queryForm }
      params.id = route.query.id * 1
      params.atime = formatYearMonth(params.atime)
      params.cheatBill = params.cheatBill ? '1' : '0'
      params.imgUrls = fileList.value.map((item) => item.uploadUrl)
      editDesc(params).then((res) => {
        console.log(res)
        ElMessage.success('保存成功！')
        // 跳转到列表页
        const obj = { path: '/fraudulentBillingManagement/fraudulentShopList' }
        tab.closeOpenPage(obj)
      })
    })
    .catch(() => {
      // 用户取消操作
    })
}

</script>

<style>
.fujian .el-upload--picture-card {
  width: 100px;
  height: 100px;
}
.fujian .el-upload-list__item {
  width: 100px;
  height: 100px;
}
.fujian.nobtn .el-upload {
  display: none;
}
</style>
<style lang="scss" scoped>
.sbsyf {
  border: 1px dashed #dcdfe6;
  padding: 30px;
  display: block;
  width: 500px;
}
.c-red {
  color: red;
}
</style>
