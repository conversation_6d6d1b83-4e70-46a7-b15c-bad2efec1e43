<template>
  <div class="app-container fee-manager">
    <el-form ref="queryRef" :model="queryParams" :inline="true" label-width="110px">
      <el-form-item label="门店名称" prop="shopName">
        <el-input
          v-model="queryParams.shopName"
          placeholder="请输入门店编号/门店名称"
          clearable
          :prefix-icon="Search"
          class="w-240"
          @keydown.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="舞弊状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择舞弊状态"
          clearable
          filterable
          class="w-240"
        >
          <el-option
            v-for="item in fraudStatusList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="地区总部" prop="divisionName">
        <el-select
          v-model="queryParams.divisionName"
          placeholder="请选择地区总部"
          clearable
          filterable
          class="w-240"
          @change="setBusiness"
        >
          <el-option
            v-for="item in businessList"
            :key="item.freeString"
            :label="item.freeString"
            :value="item.freeString"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="大区" prop="regionName">
        <el-select
          v-model="queryParams.regionName"
          placeholder="请选择大区"
          clearable
          filterable
          class="w-240"
          @change="setRegion"
        >
          <el-option
            v-for="item in regionList"
            :key="item.eareName"
            :label="item.eareName"
            :value="item.eareName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="城区" prop="eareName">
        <el-select
          v-model="queryParams.eareName"
          placeholder="请选择城区"
          clearable
          filterable
          class="w-240"
        >
          <el-option
            v-for="item in urbanList"
            :key="item.eareName"
            :label="item.eareName"
            :value="item.eareName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery()"> 重置 </el-button>
        <el-button type="warning" icon="Download" plain @click="handleExport()"> 导出 </el-button>
        <el-popover
          placement="right"
          :width="430"
          effect="dark"
          trigger="hover"
          content="修改规则后，如果从开始账期重新生成账单，请勾选对应门店。"
        >
          <template #reference>
            <el-button type="warning" icon="Plus" @click="handleCreate"> 生成舞弊账单 </el-button>
          </template>
        </el-popover>
      </el-form-item>
    </el-form>
    <el-table
      ref="tableContainer"
      v-loading="loading"
      :data="list"
      class="mt-15"
      style="width: 100%"
      :height="tableHeight"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column
        align="center"
        :index="1 + queryParams.pageSize * (queryParams.pageNum - 1)"
        label="序号"
        type="index"
        width="60"
      ></el-table-column>
      <el-table-column prop="shopId" label="门店ID" width="90">
        <template #default="scope">
          <el-text type="primary">
            {{ scope.row.shopId }}
          </el-text>
        </template>
      </el-table-column>
      <el-table-column prop="shopName" label="门店名称" min-width="250"></el-table-column>
      <el-table-column prop="atime" label="开始账期" min-width="150">
        <template #default="scope">
          {{ formatDateToYearMonth(scope.row.atime) }}
        </template>
      </el-table-column>
      <el-table-column prop="calDesc" label="商标使用费计费规则" min-width="250"></el-table-column>
      <el-table-column prop="applyDate" label="申请日期" min-width="150"></el-table-column>
      <el-table-column prop="applyUser" label="申请人" min-width="150"></el-table-column>
      <el-table-column prop="cheatBill" label="是否出具舞弊账单" min-width="200">
        <template #default="scope">
          <el-tag v-if="scope.row.cheatBill === '1'" type="success"> 是 </el-tag>
          <el-tag v-else type="danger"> 否 </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="舞弊状态" min-width="150">
        <template #default="scope">
          <el-tag v-if="scope.row.status === '1'" type="success"> 舞弊闭环 </el-tag>
          <el-tag v-else type="danger"> 舞弊默认 </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="closeDate" label="舞弊闭环时间" min-width="150"></el-table-column>
      <el-table-column
        prop="cheatAccount"
        label="舞弊罚款金额/元"
        min-width="150"
      ></el-table-column>
      <el-table-column prop="divisionName" label="地区总部" min-width="150"></el-table-column>
      <el-table-column prop="regionName" label="大区" min-width="200"></el-table-column>
      <el-table-column prop="eareName" label="城区" min-width="200"></el-table-column>
      <el-table-column prop="areaUserName" label="城区总" min-width="200"></el-table-column>
      <el-table-column prop="operation" label="操作" min-width="220" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            link
            class="noborder"
            icon="Edit"
            :disabled="scope.row.status === '1'"
            @click="handleSure(scope.row)"
          >
            闭环确认
          </el-button>
          <el-button type="warning" link class="noborder" icon="View" @click="gotoLink(scope.row)">
            舞弊规则
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 舞弊弹窗 -->
    <el-dialog v-model="dialogFormVisible" title="舞弊闭环确认" width="400" @closed="handleCancel">
      <el-form :model="confirmInfo">
        <el-form-item :label="confirmInfo.shopId" label-width="100">
          <b>{{ confirmInfo.shopName }}</b>
        </el-form-item>
        <el-form-item label="舞弊闭环时间" label-width="100">
          <el-date-picker
            v-model="confirmInfo.closeDate"
            type="date"
            placeholder="请选择时间"
            class="w100p"
            :clearable="false"
          />
        </el-form-item>
        <el-form-item label="舞弊罚款金额" label-width="100">
          <el-input
            v-model="confirmInfo.cheatAccount"
            autocomplete="off"
            placeholder="请输入舞弊罚款金额"
            @input="(value) => handleAmountInput(value, confirmInfo, 'cheatAccount')"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel"> 取消 </el-button>
          <el-button type="primary" @click="handleFormSure"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup name="FraudulentShopList">
// 舞弊门店列表
import router from '@/router'
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
// 引入请求
import {
  getShopCqList, // 查询门店城区列表
  // 查询门店地区总部列表
  getShopDqList,
  getShopHeadOfficeList
} from '@/api/chargingManager/search'
import {
  editStatus,
  exportCheat,
  getCheatList,
  makeCheatBill
} from '@/api/fraudulentBillingManagement/index'

import { fraudStatusList } from '@/utils/constants.js'
import { download0 } from '@/utils/request'
import { formatDate, handleAmountInput } from '@/utils/script.js'
import dayjs from 'dayjs'
import { onMounted } from 'vue'
const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)
const message = useMessage()
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
  shopName: undefined,
  status: undefined,
  divisionName: undefined,
  regionName: undefined,
  eareName: undefined
})
const loading = ref(false)
const list = ref([])
const total = ref(0)
const businessList = ref([]) // 地区总部
const regionList = ref([]) // 大区
const urbanList = ref([]) // 城区
const queryRef = ref(null)
const selectedIds = ref([])
// 地区总部
function getBusinessList() {
  getShopHeadOfficeList().then((res) => {
    if (res.code === 200) {
      businessList.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}

// 大区
const getRegionList = (params) => {
  getShopDqList(params).then((res) => {
    if (res.code === 200) {
      regionList.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}

// 城区
const getUrbanList = (params) => {
  getShopCqList(params).then((res) => {
    if (res.code === 200) {
      urbanList.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}
// 设置地区总部
function setBusiness(val) {
  if (val) {
    getRegionList({ freeString: val })
  }
  regionList.value = []
  queryParams.regionName = undefined
  urbanList.value = []
  queryParams.eareName = undefined
}

// 设置大区
function setRegion(val) {
  if (val) {
    const eareId = regionList.value.find((item) => item.eareName === val).eareId
    getUrbanList({ eareId })
  } else {
    urbanList.value = []
    queryParams.eareName = undefined
  }
}

const getList = () => {
  loading.value = true
  getCheatList(queryParams)
    .then((res) => {
      list.value = res.rows
      total.value = res.total
    })
    .finally(() => {
      loading.value = false
    })
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryRef.value.resetFields()
  handleQuery()
}
// 确认信息
const confirmInfo = ref({
  id: '', // 数据id
  shopId: '', // 门店ID
  shopName: '', // 门店名称
  closeDate: new Date(), // 闭环时间
  cheatAccount: '0.00' // 罚款金额
})
const dialogFormVisible = ref(false) // 对话框显示状态

const formatDateToYearMonth = (dateStr) => {
  if (!dateStr) return ''
  return dayjs(dateStr).format('YYYY-MM')
}

// 闭环确认
const handleSure = (row) => {
  // console.log("显示弹窗:",row);
  confirmInfo.value.id = row.id // 舞弊门店ID
  confirmInfo.value.shopId = row.shopId // 门店ID
  confirmInfo.value.shopName = row.shopName // 门店名称
  dialogFormVisible.value = true // 显示对话框
}

const handleCancel = () => {
  dialogFormVisible.value = false // 关闭对话框
  confirmInfo.value = {
    id: '', // 数据id
    shopId: '', // 门店ID
    shopName: '', // 门店名称
    closeDate: new Date(), // 闭环时间
    cheatAccount: '0.00' // 罚款金额
  }
}
const handleFormSure = () => {
  let params = { ...confirmInfo.value }
  params.closeDate = formatDate(confirmInfo.value.closeDate) // 闭环时间格式化
  editStatus(params)
    .then((res) => {
      if (res.code == 200) {
        ElMessage({
          message: res.msg,
          type: 'success'
        })
        searchForm(-1) // 重新查询列表
      }
    })
    .finally(() => {
      dialogFormVisible.value = false // 关闭对话框
    })
}

// 跳转到新增门店
const gotoLink = (row) => {
  router.push({
    path: '/fraudulentBillingManagement/fraudulentShopDetail',
    query: {
      id: row.id
    }
  })
}

const handleCreate = async () => {
  await message.confirm(`<div>请确认锁账后，点击生成舞弊账单</div><div>确定生成舞弊账单？</div>`, {
    title: '确定',
    dangerouslyUseHTMLString: true,
    type: 'warning'
  })
  const res = await makeCheatBill(selectedIds.value)
  if (res.code === 200) {
    message.success('生成舞弊账单成功')
    selectedIds.value = []
    tableContainer.value?.clearSelection()
  }
}

// 导出
const handleExport = async () => {
  const res = await exportCheat(queryParams)
  if (res) {
    download0(res, '舞弊门店列表', 'application/vnd.ms-excel')
  }
}
// 多选框选中数据
const handleSelectionChange = (selection) => {
  selectedIds.value = selection.map((item) => item.id)
}
onMounted(() => {
  getList()
  getBusinessList()
})
</script>

<style scoped></style>
