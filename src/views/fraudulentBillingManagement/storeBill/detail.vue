<template>
  <div class="app-container store-billing-details">
    <div class="detail-title">
      <div>
        <span class="shop-name">{{ params.shopId }}-{{ params.shopName }}</span>
        <span class="detail-status">账单期间 {{ params.atime }}</span>
      </div>
      
      <el-button type="primary" @click="handleConfirm">
        确认调整
      </el-button>
    </div>

    <div class="detail-info">
      <div class="info-item">
        <p class="item-name">
          期初余额
        </p>
        <p class="item-value">
          {{ balance.proMoney }}
        </p>
      </div>
      <div class="info-item">
        <p class="item-name">
          本期应收+费用调整
        </p>
        <p class="item-value">
          {{ balance.thisMoney }}
        </p>
      </div>
      <div class="info-item">
        <p class="item-name">
          本期清分抵扣
        </p>
        <p class="item-value">
          {{ balance.clearMoney }}
        </p>
      </div>
      <div class="info-item">
        <p class="item-name">
          本期回款
        </p>
        <p class="item-value">
          {{ balance.backMoney }}
        </p>
      </div>
      <div class="info-item">
        <p class="item-name">
          期末余额
        </p>
        <p class="item-value">
          {{ balance.endBalance }}
        </p>
      </div>
    </div>

    <el-table :data="chargeInfoList" :span-method="arraySpanMethod" style="width: 1220px;">
      <el-table-column type="index" label="序号" width="60" align="left" />
      <el-table-column key="feeTitle" label="费用项" align="left" prop="feeTitle" width="160" />
      <el-table-column key="proMoney" label="前期余额" align="left" prop="proMoney" width="100">
        <template #default="scope">
          <el-input v-model="scope.row.proMoney"></el-input>
        </template>
      </el-table-column>
      <el-table-column key="thisMoney" label="本期应收" align="center" prop="thisMoney" width="400">
        <el-table-column key="csyyt" label="冲上月预提" align="center" prop="csyyt" width="100" />
        <el-table-column key="jsysj" label="记上月实际" align="center" prop="jsysj" width="100" />
        <el-table-column key="dyyt" label="当月预提" align="center" prop="dyyt" width="100" />
        <el-table-column key="jdTz" label="季度调整" align="center" prop="jdTz" width="100" />
      </el-table-column>
      <el-table-column key="clearMoney" label="本期清分" align="left" prop="clearMoney" width="100" />
      <el-table-column key="backMoney" label="本期回款" align="left" prop="backMoney" width="100" />
      <el-table-column key="adjustMoney" label="费用调整" align="left" prop="adjustMoney" width="100" />
      <el-table-column key="endBalance" label="期末余额" align="left" prop="endBalance" width="100" />
      <el-table-column key="calculateDesc" label="计费标准" align="left" prop="calculateDesc" width="100" />
    </el-table>
  </div>
</template>

<script setup name="FraudulentBillDetail">
import { ref } from 'vue'
import { getCheatBalance, getCheatChargeInfo } from '@/api/fraudulentBillingManagement/storeBill'
import { ElMessage } from 'element-plus'

const route = useRoute() 

const params = ref({
  shopId: route.query.id,
  shopName: route.query.name,
  atime: route.query.atime
})
const balance = ref({}) // 统计数据
const chargeInfoList = ref([]) // 计费明细
const proMoneyTotal = ref(0) // 期初余额总额

const getDetails = () => {
  const data = {...params.value}

  getCheatChargeInfo(data).then((res) => {
    chargeInfoList.value = res.data
    proMoneyTotal.value = 0
    res.data.forEach((item) => {
      proMoneyTotal.value += Number(item.proMoney)
    })
  }).catch(() => {
    chargeInfoList.value = []
  })

  getCheatBalance(data).then((res) => {
    balance.value = res.data
  }).catch(() => {
    balance.value = {}
  })
}

const handleConfirm = () => {
  console.log('确认调整')
  let total = 0
  chargeInfoList.value.forEach((item) => {
    total += Number(item.proMoney)
  })
  if (total !== proMoneyTotal.value) {
    ElMessage.error('修改前后期初余额不一致，请重新调整')
    return
  }

  ElMessage.success('调整成功')
}

// 合并单元格
const arraySpanMethod = ({ row, columnIndex}) => {
  if (!['营销经理工资', '店长工资'].includes(row.feeTitle)) { 
    row.csyyt = row.thisMoney
    if (columnIndex === 3) {
      return [1, 4]
    } else if (columnIndex > 3 && columnIndex <= 6) {
      return [0, 0]
    }
  }
}

getDetails()
</script>
<style scoped lang="scss">
.store-billing-details {
  .detail-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 30px;

    .shop-name {
      padding-right: 20px;
      font-size: 18px;
      font-weight: 600;
    }

    .detail-status {
      padding-right: 20px;
      font-size: 16px;
    }
  }

  .detail-info {
    padding-bottom: 20px;
    display: flex;

    .info-item {
      width: 220px;
      margin-right: 12px;
      padding: 12px 20px;
      background: #FCF6EB;
      border-radius: 8px;
      color: #E6A23C;

      .item-name {
        margin: 0;
        padding-bottom: 12px;
        font-size: 16px;
        text-align: center;
      }

      .item-value { 
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        text-align: center;
      }
    }
  }
}
</style>
