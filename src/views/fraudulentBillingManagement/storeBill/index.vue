<template>
  <div class="app-container">
    <el-form ref="queryRef" :model="queryParams" :inline="true" label-width="110px">
      <el-form-item label="账单期间" prop="atimeList">
        <el-date-picker
          v-model="queryParams.atimeList"
          type="months"
          :clearable="false"
          placeholder="请选择账单期间"
          style="width: 240px"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="门店名称" prop="shopName">
        <el-input
          v-model="queryParams.shopName"
          placeholder="请输入门店编码/门店名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="邮件发送数" prop="emailCount">
        <el-input
          v-model="queryParams.emailCount"
          placeholder="请输入邮件发送数"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="地区总部" prop="divisionName">
        <el-select
          v-model="queryParams.divisionName"
          placeholder="请选择地区总部"
          clearable
          style="width: 240px"
          @change="setBusiness"
        >
          <el-option
            v-for="item in businessList"
            :key="item.freeString"
            :label="item.freeString"
            :value="item.freeString"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="大区" prop="regionName">
        <el-select
          v-model="queryParams.regionName"
          placeholder="请选择大区"
          clearable
          style="width: 240px"
          @change="setRegion"
        >
          <el-option
            v-for="item in regionList"
            :key="item.eareName"
            :label="item.eareName"
            :value="item.eareName"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="城区" prop="eareName">
        <el-select
          v-model="queryParams.eareName"
          placeholder="请选择城区"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="item in urbanList"
            :key="item.eareName"
            :label="item.eareName"
            :value="item.eareName"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
        <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
        <el-button type="warning" plain icon="Download" @click="handleExport"> 导出 </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="flex">
      <el-col style="flex: 1"> </el-col>
      <right-toolbar :search="false" :show-refresh="false" :columns="columns"></right-toolbar>
    </el-row>
    <el-table
      ref="tableContainer"
      v-loading="loading"
      class="mt-15"
      :data="budgetChangeList"
      :height="tableHeight"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column type="index" label="序号" width="50" align="left" />
      <template v-for="item in columns">
        <el-table-column
          v-if="item.visible"
          :key="item.prop"
          :label="item.label"
          :align="item.align || 'left'"
          :prop="item.prop"
          :width="item.width"
        >
          <template v-if="item.prop === 'atime'" #default="scope">
            {{ scope.row.atime?.slice(0, 7) }}
          </template>
          <template v-else #default="scope">
            {{ scope.row[item.prop] }}
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" align="center" width="120">
        <template #default="scope">
          <!-- <el-button link type="primary" @click="toDetail(scope.row)">
            费用明细
          </el-button> -->
          <el-button link type="primary" icon="View" @click="toBillDetail(scope.row)">
            账单详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="1200">
      <template #default>
        <div v-html="dialogText"></div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel"> 关 闭 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="FraudulentstoreBill">
import {
  getShopCqList, // 查询门店城区列表
  // 查询门店地区总部列表
  getShopDqList,
  getShopHeadOfficeList
} from '@/api/chargingManager/search'
import {
  addExportTask,
  getBillDetail,
  getCheatSendList
} from '@/api/fraudulentBillingManagement/storeBill'
import { getBeforeMonth, getFirstDayOfMonth } from '@/utils'
import { ElMessage } from 'element-plus'
import { onActivated } from 'vue'

// 列信息
const columns = ref([
  { key: 0, label: `门店ID`, visible: true, prop: 'shopId' },
  { key: 1, label: `门店名称`, visible: true, prop: 'shopName' },
  { key: 6, label: `账单期间`, visible: true, prop: 'atime' },
  { key: 2, label: `地区总部`, visible: true, prop: 'divisionName' },
  { key: 3, label: `大区`, visible: true, prop: 'regionName' },
  { key: 4, label: `城区`, visible: true, prop: 'eareName' },
  { key: 5, label: `城区总`, visible: true, prop: 'areaUserName' }
  // { key: 7, label: `邮件发送数`, visible: true, prop: 'emailCount', width: '160' },
  // { key: 8, label: `导出次数`, visible: true, prop: 'exportCount', width: '160' }
])

const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)
const message = useMessage()
const { proxy } = getCurrentInstance()
const dateRange = ref([])
const budgetChangeList = ref([])
const loading = ref(false)
const total = ref(0)

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    shopName: undefined,
    atimeList: [getBeforeMonth()],
    divisionName: undefined,
    regionName: undefined,
    eareName: undefined
  }
})

const { queryParams } = toRefs(data)
const businessList = ref([]) // 地区总部
const regionList = ref([]) // 大区
const urbanList = ref([]) // 城区
// 地区总部
function getBusinessList() {
  getShopHeadOfficeList().then((res) => {
    if (res.code === 200) {
      businessList.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}

// 大区
const getRegionList = (params) => {
  getShopDqList(params).then((res) => {
    if (res.code === 200) {
      regionList.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}

// 城区
const getUrbanList = (params) => {
  getShopCqList(params).then((res) => {
    if (res.code === 200) {
      urbanList.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}

// 设置地区总部
function setBusiness(val) {
  if (val) {
    getRegionList({ freeString: val })
  }
  regionList.value = []
  queryParams.value.regionName = undefined
  urbanList.value = []
  queryParams.value.eareName = undefined
}

// 设置大区
function setRegion(val) {
  if (val) {
    const eareId = regionList.value.find((item) => item.eareName === val).eareId
    getUrbanList({ eareId })
  } else {
    urbanList.value = []
    queryParams.value.eareName = undefined
  }
}

/** 查询用户列表 */
const getList = () => {
  loading.value = true
  const params = {
    ...queryParams.value
  }
  params.atimes = Array.isArray(params.atimeList)
    ? params.atimeList.map((item) => getFirstDayOfMonth(item)).join(',')
    : ''
  delete params.atimeList

  getCheatSendList(params)
    .then((res) => {
      budgetChangeList.value = res.rows
      total.value = res.total
    })
    .finally(() => {
      loading.value = false
    })
}
/** 搜索按钮操作 */
const handleQuery = () => {
  console.log(queryParams.value, ' queryParams.value')
  queryParams.value.pageNum = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = []
  proxy.resetForm('queryRef')
  handleQuery()
}

const selectedIds = ref([])
// 多选框选中数据
const handleSelectionChange = (selection) => {
  selectedIds.value = selection.map((item) => item.id)
}

/** 导出按钮操作 */
const handleExport = () => {
  if (!selectedIds.value.length) {
    message.warning('请选择需要导出的门店')
    return
  }
  proxy.$modal
    .confirm('确认导出此报表吗？')
    .then(async function () {
      const params = {
        billIds: selectedIds.value
      }
      const res = await addExportTask(params)
      if (res.code === 200) {
        message.success('文件导出中，请前往【导出管理】-【文件下载】列表下载文件。')
      }
    })
    .catch(() => {})
}

const toDetail = (row) => {
  proxy.$router.push({
    path: '/fraudulentBillingManagement/fraudulentBillDetail',
    query: {
      id: row.shopId,
      name: row.shopName,
      atime: row.atime
    }
  })
}
// 账单详情
const dialogVisible = ref(false)
const dialogTitle = ref('账单详情')
const dialogText = ref('')
const toBillDetail = async (row) => {
  try {
    const res = await getBillDetail(row.shopId, { atime: row.atime, cheatId: row.cheatId })
    dialogText.value = res
    dialogVisible.value = true
  } catch (e) {
    message.error(e || '账单详情获取失败')
  }
}
const handleCancel = () => {
  dialogVisible.value = false
}
onActivated(() => {
  handleQuery()
})
getList()
getBusinessList()
</script>
<style scoped>
.flex {
  display: flex;
}
</style>
