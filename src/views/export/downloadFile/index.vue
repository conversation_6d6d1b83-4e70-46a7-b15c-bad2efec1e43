<template>
  <div class="app-container">
    <h3>文件下载</h3>
    <el-table
      ref="tableContainer"
      v-loading="loading"
      :data="list"
      class="mt-15 w-full"
      :height="tableHeight"
    >
      <el-table-column type="index" label="序号" width="100" align="left" />
      <el-table-column label="任务ID" align="left" prop="taskId" width="200" />
      <el-table-column label="文件名称" align="left" prop="fileName" width="400" />
      <el-table-column label="操作者" align="left" prop="operatorName" width="300" />
      <el-table-column label="导出时间" align="left" width="200">
        <template #default="scope">
          {{ scope.row.sucTimeStr ?? '--' }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="statusStr">
        <template #default="scope">
          <el-tag :type="status[scope.row.statusStr]">
            {{ scope.row.statusStr }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            v-if="scope.row.statusStr === '导出完成'"
            icon="Download"
            link
            type="primary"
            @click="handleDownload(scope.row)"
          >
            下载
          </el-button>
        </template>
      </el-table-column>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </el-table>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { downloadFile, getFileList } from '@/api/fraudulentBillingManagement/storeBill'
import { download0 } from '@/utils/request'
import { onMounted, reactive } from 'vue'
// 文件下载
defineOptions({ name: 'DownloadFile' })

const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)

const message = useMessage()

// 表格相关数据
const loading = ref(false)
const total = ref(0)
const list = ref([])

const queryParams = reactive({
  pageSize: 20,
  pageNum: 1
})
const status = reactive({
  初始化: 'primary',
  导出中: 'warning',
  导出完成: 'success'
})
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await getFileList(queryParams)
    list.value = res.data.list
    total.value = res.data.total
  } finally {
    loading.value = false
  }
}

/** 下载按钮操作 */
const handleDownload = (row) => {
  message.confirm('确认下载此文件吗？').then(async () => {
    const res = await downloadFile(row.fileId)
    if (res) {
      download0(res, row.fileName, 'application/zip')
    }
  })
}
onMounted(() => {
  getList()
})
</script>
<style lang="scss" scoped></style>
