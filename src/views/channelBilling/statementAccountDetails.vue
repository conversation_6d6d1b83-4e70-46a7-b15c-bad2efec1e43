<template>
  <div class="app-container">
    <queryForm
      :query-form-datas="queryFormDatas"
      :form-interface="formInterface"
      @search-form="searchForm"
    ></queryForm>
    <queryTableData
      :query-form-datas="queryFormDatas"
      :pagination-info="paginationInfo"
      :table-data="tableData"
      :table-loading="tableLoading"
      @search-form="searchForm"
    ></queryTableData>
  </div>
</template>
<script setup>
// 对账单详情
import { shopDetailList, shopList } from '@/api/channelBilling/index.js'
import queryForm from '@/components/statementAccount/queryForm.vue'
import queryTableData from '@/components/statementAccount/queryTableData.vue'
import { billTypeMap, paginationInfoConstant } from '@/utils/constants.js'

// 接收url中的参数
const route = useRoute()
// console.log(route.query)
const tableData = ref([]) // 表格数据
const tableLoading = ref(false) // 表格加载状态

// 对账单类型
let billType = ''
for(let item in billTypeMap){
  if(billTypeMap[item] === route.query.billType){
    billType = item
  }
}
// 搜索条件 
const formInterface = {
  shopId: '', // 门店ID
  fieldNo: '', // 入住单号
  bookNo: '', // 预定单号
  channelNo: '', // 渠道单号
  tabPosition: 1, // 1 订单明细 2 分店汇总
  billNo: route.query.billNo, // 对账单号 3895, //
  billType: billType // 对账单类型
}
const queryFormDatas = reactive(Object.assign({}, formInterface))

// 分页信息
const paginationInfo = reactive({
  ...structuredClone(paginationInfoConstant)
})
// 查询
const searchForm = (args) => {
  if (args) {
    Object.assign(queryFormDatas, args)
  }

  tableLoading.value = true // 加载状态
  let params = {
    pageNum: paginationInfo.currentPage, // 当前页码 number
    pageSize: paginationInfo.pageSize // 每页条数 number
  }
  // console.log('查询参数:', params)
  if(queryFormDatas.tabPosition === 2){
    params.shopId = queryFormDatas.shopId
    params.billNo = queryFormDatas.billNo
    // 分店汇总 查询门店列表接口调用
    shopList(params).then((res) => {
      // console.log('分店汇总列表:', res)
      if(res.code === 200){
        tableData.value = res.data.list || []
        paginationInfo.totalCount = res.data.total // 总条数
      }
    }).finally(() => {
      tableLoading.value = false // 加载状态
    })
  }else{
    Object.assign(params, {
      ...queryFormDatas,
      shopName: queryFormDatas.shopId // 门店ID
    })
    delete params.shopId
    shopDetailList(params).then((res) => {
      // console.log('账单详情列表:', res)
      if(res.code === 200){
        tableData.value = res.rows || []
        paginationInfo.totalCount = res.total // 总条数
        // console.log('分页信息:', paginationInfo)
        tableLoading.value = false // 加载状态
      }
    }).finally(() => {
      tableLoading.value = false // 加载状态
    })
  }
}
let tabPosition = ''
watchEffect(() => {
  if(queryFormDatas.tabPosition !== tabPosition){
    tabPosition = queryFormDatas.tabPosition
    tableData.value = []
    paginationInfo.totalCount = 0
    setTimeout(() => {
      searchForm()
    }, 10)
  }
})

</script>