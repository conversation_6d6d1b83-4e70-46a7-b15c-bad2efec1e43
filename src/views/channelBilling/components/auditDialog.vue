<template>
  <el-dialog v-model="dialogVisible" :title="title" width="600" @close="handleCancel">
    <el-alert title="审核确认后，次日会进入清分账户" type="warning" style="margin-bottom: 10px" show-icon :closable="false" />
    <el-form
      ref="formRef"
      :model="formData"
      label-width="100px"
      class="mt-20"
      :rules="rules"
    >
      <el-form-item label="入账金额" prop="amount">
        <el-input-number
          v-model="formData.amount" :precision="2" :step="0.1" :min="0" placeholder="请输入入账金额"
          class="w-200"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleConfirm">
          确 定
        </el-button>
        <el-button @click="handleCancel">
          取 消
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
// 审核弹窗
import { auditBill } from '@/api/channelBilling/index'
import { watch } from 'vue'
const props = defineProps({
  auditVisible: {
    type: Boolean,
    default: false
  },
  detail:{
    type:Object,
    default:() => {
      return {}
    }
  }
})
const emit = defineEmits(['updateList', 'update:auditVisible'])
const message = useMessage()
const title = ref('审核')
const formRef = ref()
const dialogVisible = ref(false) 

const formData = ref({
  amount: 0
})
const rules = reactive({
  amount: [{ required: true, message: '请填写入账金额', trigger: 'blur' }]
})

const handleConfirm = async () => {
  await auditBill(props.detail.billNo, {
    operate:1,
    ...formData.value
  })
  message.success('审核成功')
  // 更新外层列表 关闭当前弹窗
  emit('updateList')
  dialogVisible.value = false
}

const handleCancel = () => {
  emit('update:auditVisible')
  dialogVisible.value = false
}
const resetForm = () => {
  formData.value = {amount: 0}
  formRef.value?.resetFields()
}
watch(() => props.auditVisible, (val) => { 
  resetForm()
  dialogVisible.value = val
})
</script>

<style lang="scss" scoped>

</style>
