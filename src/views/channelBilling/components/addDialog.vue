<template>
  <el-dialog v-model="dialogVisible" :title="title" width="600">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      label-width="100px"
      class="mt-20"
      :rules="rules"
    >
      <el-form-item label="单据类型" prop="billType">
        <el-input v-model="billTypeValue" class="w-300" :disabled="true" />
      </el-form-item>
      <el-form-item label="账单期间" prop="dateRange">
        <el-date-picker
          v-model="formData.dateRange"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 300px;flex-grow: revert"
        />
      </el-form-item>
      <el-form-item label="渠道名称" prop="channels">
        <el-select
          v-model="formData.channels"
          placeholder="请选择渠道名称"
          clearable
          multiple
          style="width: 240px"
        >
          <el-option
            v-for="channel in channelList"
            :key="channel.id"
            :label="channel.name"
            :value="channel.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="账单说明" prop="memo">
        <el-input
          v-model="formData.memo" placeholder="请输入账单说明" class="w-320" type="textarea" maxlength="100"
          show-word-limit :autosize="{ minRows: 2, maxRows: 8 }"
        />
      </el-form-item>
      <el-form-item v-if="billType === 3" prop="noPay">
        <el-checkbox v-model="formData.noPay">
          只计算直销间夜，不结算预付金额
        </el-checkbox>
      </el-form-item>
      <el-form-item prop="isInvoice">
        <el-checkbox v-model="formData.isInvoice">
          是否回收发票
        </el-checkbox>
      </el-form-item>
      <el-form-item label="发票抬头" prop="companyName">
        <el-select
          v-model="formData.companyName"
          placeholder="请选择发票抬头"
          clearable
          style="width: 240px"
          :disabled="!formData.isInvoice"
        >
          <el-option
            v-for="(invoice,invoiceIdx) in invoiceList"
            :key="invoiceIdx"
            :label="invoice"
            :value="invoice"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleConfirm">
          确 定
        </el-button>
        <el-button @click="handleCancel">
          取 消
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
// 新建账单弹窗
import { addBill, getBillChannel, getBillInvoice } from '@/api/channelBilling/index'
import { billTypeMap } from '@/utils/constants'
import { ref, reactive, computed, getCurrentInstance} from 'vue'
const props = defineProps({
  // 账单类型
  billType: {
    type: Number,
    default: 0
  }
})
const message = useMessage()
const emit = defineEmits(['updateList'])
const title = ref('新建账单')
const dialogVisible = ref(false) 
const formLoading = ref(false)
const formRef = ref()
const { proxy } = getCurrentInstance()

const billTypeValue = computed(() => {
  return billTypeMap[props.billType]
})
const formData = ref({
  billType: billTypeValue,
  channels:[],
  dateRange:[],
  companyName:'',
  noPay:false,
  isInvoice:false,
  memo:''
})
const channelList = ref([])
const invoiceList = ref([])
const rules = reactive({
  dateRange: [
    { 
      required: true, 
      message: '请选择账单期间', 
      trigger: 'change' 
    },
    { 
      validator: (rule, value, callback) => {
        if (!value || value.length < 2) {
          callback(new Error('请选择完整的起止日期'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ],
  channels: [{ required: props.billType !== 3, message: '请选择渠道名称', trigger: 'change' }],
  memo: [{ required: props.billType === 3, message: '请输入账单说明', trigger: 'blur' }]
})

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  getBillChannelList()
  getBillInvoiceList()
}

// 查询渠道列表
const getBillChannelList = async () => { 
  const res = await getBillChannel()
  channelList.value = res.data
}
// 查询发票抬头列表
const getBillInvoiceList = async () => { 
  const res = await getBillInvoice()
  invoiceList.value = res.data
}
const handleConfirm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 渠道名称：未选择时，提醒：请选择对应渠道
  if(!formData.value.channels) {
    await proxy.$modal.msgError(`请选择对应渠道`) 
    return 
  }
  // 选中是否回收发票：未选择发票抬头时，提醒：请选择开票抬头
  if(formData.value.isInvoice && !formData.value.companyName) {
    await proxy.$modal.msgError(`请选择发票抬头`)
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    let tempFormdata = {
      ...formData.value,
      beginDate:formData.value.dateRange?.[0],
      endDate:formData.value.dateRange?.[1],
      channels: formData.value.channels.join(',')
    }
    delete tempFormdata.dateRange
    await addBill(tempFormdata)
    message.success('新增账单成功')
    submitSuccess()
  } finally {
    formLoading.value = false
  }
}
const submitSuccess = () => {
  // 更新外层列表 关闭当前弹窗
  emit('updateList')
  dialogVisible.value = false
  resetForm()
}

const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  formData.value = {
    billType: billTypeValue,
    channels:[],
    dateRange:[],
    companyName:'',
    noPay:false,
    isInvoice:false,
    memo:''
  }
  formRef.value?.resetFields()
}

watch(() => formData.value.isInvoice, (val) => { 
  if(!val) formData.value.companyName = ''
})
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped>
:deep(.el-textarea__inner){
  height: 100px !important;
}
</style>
