<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-change="handleTabs">
      <el-tab-pane label="分店结算规则" name="subbranch">
        <Subbranch ref="subbranch" />
      </el-tab-pane>
      <el-tab-pane label="渠道结算规则" name="channel">
        <Channel ref="channel" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import Subbranch from './components/subbranch.vue'
import Channel from './components/channel.vue'
// 渠道账号规则设置
defineOptions({ name: 'ChannelBillingRule' })

const activeName = ref('subbranch')
const subbranch = ref()
const channel = ref()
const handleTabs = () => {
  activeName.value === 'subbranch' ? subbranch.value?.getList() : channel.value?.getList()
}
</script>
