<template>
  <el-table
    ref="tableContainer"
    v-loading="loading"
    :data="list"
    class="mt-15 w-full"
    :height="tableHeight"
  >
    <el-table-column
      align="center"
      :index="1 + queryParams.pageSize * (queryParams.pageNum - 1)"
      label="序号"
      type="index"
      min-width="60"
    ></el-table-column>

    <el-table-column prop="code" label="代码" min-width="200"> </el-table-column>
    <el-table-column prop="name" label="渠道名称" min-width="250"></el-table-column>
    <el-table-column prop="companyCode" label="财务代码" width="250"> </el-table-column>
    <el-table-column prop="companyName" label="付款账户" width="350"></el-table-column>
    <el-table-column prop="orgcode" label="财务组织" width="250"></el-table-column>
    <el-table-column label="操作" align="center" width="120" fixed="right">
      <template #default="scope">
        <el-button type="primary" link icon="Edit" class="noborder" @click="handleEdit(scope.row)">
          编辑
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  <pagination
    v-show="total > 0"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    :total="total"
    @pagination="getList"
  />
  <ChannelEditDialog ref="channelEditDialog" @update-list="handleQuery"></ChannelEditDialog>
</template>

<script setup>
import { getBillChannels } from '@/api/channelBilling'
import { onMounted } from 'vue'
import { useTableHeight } from '@/hooks/useTableHeight'
import ChannelEditDialog from './channelEditDialog.vue'
// 渠道结算规则
defineOptions({ name: 'ChannelRule' })

const tableContainer = ref(null)
const { tableHeight, resetTableHeight } = useTableHeight(tableContainer)

const loading = ref(false)
const total = ref(0)
const list = ref([])
const queryParams = reactive({
  pageSize: 20,
  pageNum: 1
})
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await getBillChannels(queryParams)
    list.value = res.rows
    total.value = res.total
  } finally {
    loading.value = false
    resetTableHeight()
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

const dialogTitle = ref('')
const channelEditDialog = ref()
const handleEdit = (row) => {
  channelEditDialog.value.open(row)
}

onMounted(() => {
  getList()
})
defineExpose({ getList }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped></style>
