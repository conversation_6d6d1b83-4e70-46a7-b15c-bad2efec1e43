<template>
  <el-dialog v-model="dialogVisible" title="分店结算规则" width="600">
    <el-form ref="formRef" :model="formData" label-width="100px">
      <el-form-item label="供应商代码" prop="code">
        <span class="font-bold">{{ formData.code }}</span>
      </el-form-item>
      <el-form-item label="供应商名称" prop="name">
        <span class="font-bold">{{ formData.name }}</span>
      </el-form-item>
      <el-form-item label="财务代码" prop="orgcode">
        <el-input v-model="formData.orgcode" placeholder="请输入财务代码" class="w-250" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" :disabled="loading" @click="handleConfirm">
          确 定
        </el-button>
        <el-button @click="handleCancel"> 取 消 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { updateBillBaseStore } from '@/api/channelBilling'

const dialogVisible = ref(false)
const formRef = ref()
const loading = ref(false)
const formData = ref({
  code: undefined,
  name: undefined,
  orgcode: undefined
})
const emit = defineEmits(['updateList'])
const message = useMessage()

/** 打开弹窗 */
const open = async (row) => {
  resetForm()
  dialogVisible.value = true
  formData.value = { ...formData.value, ...row }
}

const handleConfirm = async () => {
  loading.value = true
  try {
    const res = await updateBillBaseStore(formData.value)
    if (!res) return
    message.success('修改成功')
    dialogVisible.value = false
    emit('updateList')
  } finally {
    loading.value = false
  }
}
const resetForm = () => {
  formData.value = {
    code: undefined,
    name: undefined,
    companyCode: undefined,
    companyName: undefined,
    orgcode: undefined
  }
  formRef.value?.resetFields()
}
const handleCancel = () => {
  dialogVisible.value = false
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped></style>
