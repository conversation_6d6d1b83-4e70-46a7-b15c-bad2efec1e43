<template>
  <el-table
    ref="tableContainer"
    v-loading="loading"
    :data="list"
    class="mt-15 w-full"
    :height="tableHeight"
  >
    <el-table-column
      align="center"
      :index="1 + queryParams.pageSize * (queryParams.pageNum - 1)"
      label="序号"
      type="index"
      min-width="60"
    ></el-table-column>

    <el-table-column prop="code" label="供应商代码" min-width="350"> </el-table-column>
    <el-table-column prop="name" label="供应商名称" min-width="350"></el-table-column>
    <el-table-column prop="orgcode" label="财务代码" min-width="250"> </el-table-column>
    <el-table-column label="操作" align="center" min-width="120" fixed="right">
      <template #default="scope">
        <el-button type="primary" link icon="Edit" class="noborder" @click="handleEdit(scope.row)">
          编辑
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  <pagination
    v-show="total > 0"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    :total="total"
    @pagination="getList"
  />
  <SubbranchEditDialog ref="subbranchEditDialog" @update-list="handleQuery"></SubbranchEditDialog>
</template>

<script setup>
import { getBillBaseStore } from '@/api/channelBilling'
import { onMounted } from 'vue'
import SubbranchEditDialog from './subbranchEditDialog.vue'
import { useTableHeight } from '@/hooks/useTableHeight'
// 分店结算规则
defineOptions({ name: 'SubbranchRule' })

const tableContainer = ref(null)
const { tableHeight, resetTableHeight } = useTableHeight(tableContainer)

const loading = ref(false)
const total = ref(0)
const list = ref([])
const queryParams = reactive({
  pageSize: 20,
  pageNum: 1
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await getBillBaseStore(queryParams)
    list.value = res.rows
    total.value = res.total
  } finally {
    loading.value = false
    resetTableHeight()
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

const subbranchEditDialog = ref()
const handleEdit = (row) => {
  subbranchEditDialog.value.open(row)
}

onMounted(() => {
  getList()
})
defineExpose({ getList }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss"></style>
