<template>
  <div class="app-container">
    <el-row :gutter="10" class="mt-15">
      <el-col>
        <el-button type="primary" icon="Plus" plain @click="handleAdd()"> 新增 </el-button>
      </el-col>
    </el-row>
    <el-table
      ref="tableContainer"
      v-loading="loading"
      :data="list"
      class="mt-15 w-full"
      :height="tableHeight"
    >
      <el-table-column
        align="center"
        :index="1 + queryParams.pageSize * (queryParams.pageNum - 1)"
        label="序号"
        type="index"
        min-width="60"
      ></el-table-column>

      <el-table-column prop="companyName" label="户名" min-width="250"> </el-table-column>
      <el-table-column prop="tariffCode" label="税号" min-width="250"></el-table-column>
      <el-table-column prop="openingBank" label="开户行" min-width="250"> </el-table-column>
      <el-table-column prop="bankAccount" label="银行账号" min-width="250"></el-table-column>
      <el-table-column prop="address" label="地址" min-width="250"></el-table-column>
      <el-table-column prop="concat" label="电话" min-width="250"></el-table-column>
      <el-table-column label="操作" align="center" min-width="120" fixed="right">
        <template #default="scope">
          <el-button type="primary" link icon="Edit" @click="handleEdit(scope.row)">
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />
    <CompanyInvoicingDialog
      ref="companyInvoicingDialog"
      :title="dialogTitle"
      @update-list="handleQuery"
    ></CompanyInvoicingDialog>
  </div>
</template>

<script setup>
import { getBillInvoicePage } from '@/api/channelBilling'
import { onMounted } from 'vue'
import { useTableHeight } from '@/hooks/useTableHeight'
import CompanyInvoicingDialog from './companyInvoicingDialog.vue'
// 企业商旅开票设置
defineOptions({ name: 'CompanyBusinessInvoicing' })

const tableContainer = ref(null)
const { tableHeight } = useTableHeight(tableContainer)

const loading = ref(false)
const total = ref(0)
const list = ref([])
const queryParams = reactive({
  pageSize: 20,
  pageNum: 1
})
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await getBillInvoicePage(queryParams)
    list.value = res.data.list
    total.value = res.data.total
  } finally {
    loading.value = false
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

const dialogTitle = ref('')
const detail = ref({})
const companyInvoicingDialog = ref()
const handleEdit = (row) => {
  dialogTitle.value = '修改-企业商旅开票设置'
  detail.value = row
  companyInvoicingDialog.value.open(row)
}

const handleAdd = () => {
  companyInvoicingDialog.value.open()
  dialogTitle.value = '新增-企业商旅开票设置'
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped></style>
