<template>
  <el-dialog v-model="dialogVisible" :title="title" width="600">
    <el-form ref="formRef" :model="formData" label-width="100px" class="mt-20" :rules="rules">
      <el-form-item label="户名" prop="companyName">
        <el-input v-model="formData.companyName" placeholder="请输入户名" class="w-250" />
      </el-form-item>
      <el-form-item label="税号" prop="tariffCode">
        <el-input v-model="formData.tariffCode" placeholder="请输入税号" class="w-250" />
      </el-form-item>
      <el-form-item label="开户行" prop="openingBank">
        <el-input v-model="formData.openingBank" placeholder="请输入开户行" class="w-250" />
      </el-form-item>
      <el-form-item label="银行账号" prop="bankAccount">
        <el-input v-model="formData.bankAccount" placeholder="请输入银行账号" class="w-250" />
      </el-form-item>
      <el-form-item label="地址" prop="address">
        <el-input v-model="formData.address" placeholder="请输入地址" class="w-250" />
      </el-form-item>
      <el-form-item label="电话" prop="concat">
        <el-input v-model="formData.concat" placeholder="请输入电话" class="w-250" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" :loading="loading" :disabled="loading" @click="handleConfirm">
          确 定
        </el-button>
        <el-button @click="handleCancel"> 取 消 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { saveBillInvoice } from '@/api/channelBilling'
const props = defineProps({
  title: {
    type: String,
    default: ''
  }
})
const emit = defineEmits(['updateList'])

const dialogVisible = ref(false)
const loading = ref(false)
const message = useMessage()
const formData = ref({
  companyName: undefined,
  tariffCode: undefined,
  openingBank: undefined,
  bankAccount: undefined,
  address: undefined,
  concat: undefined
})
const formRef = ref()
const rules = reactive({
  companyName: [{ required: true, message: '户名不能为空', trigger: 'blur' }],
  tariffCode: [{ required: true, message: '税号不能为空', trigger: 'blur' }],
  openingBank: [{ required: true, message: '开户行不能为空', trigger: 'blur' }],
  bankAccount: [{ required: true, message: '账号不能为空', trigger: 'blur' }],
  address: [{ required: true, message: '地址不能为空', trigger: 'blur' }],
  concat: [{ required: true, message: '电话不能为空', trigger: 'blur' }]
})

/** 打开弹窗 */
const open = async (row) => {
  resetForm()
  dialogVisible.value = true
  if (row && row.id) {
    formData.value = { ...formData.value, ...row }
  }
}

const handleConfirm = async () => {
  // 校验表单
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  loading.value = true
  try {
    const res = await saveBillInvoice(formData.value)
    if (res.code === 200) {
      message.success(formData.value.id ? '修改成功' : '新增成功')
      dialogVisible.value = false
      emit('updateList')
    }
  } finally {
    loading.value = false
  }
}
const resetForm = () => {
  formData.value = {
    companyName: undefined,
    tariffCode: undefined,
    openingBank: undefined,
    bankAccount: undefined,
    address: undefined,
    concat: undefined
  }
  formRef.value?.resetFields()
}
const handleCancel = () => {
  dialogVisible.value = false
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style lang="scss" scoped></style>
