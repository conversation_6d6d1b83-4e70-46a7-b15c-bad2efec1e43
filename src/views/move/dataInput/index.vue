<template>
  <div class="app-container home">
    <el-row :gutter="10">
      <el-col :sm="24" :lg="24">
        <!--  show-summary
          :summary-method="getSummaries" -->
        <el-table
          :data="tableData" 
          height="350" 
          style="width: 100%" 
          :stripe="true" 
          border
          row-key="branchNo"
          @row-click="trClick"
        >
          <el-table-column type="index" label="序号" width="60" fixed="left" align="center" />
          <el-table-column prop="branchNo" label="门店id" width="100" align="center" />
          <el-table-column prop="branchName" label="门店名称" width="120" align="center" />
          <el-table-column prop="period" label="地区总部" width="100" align="center" />
          <el-table-column prop="region" label="大区" width="100" align="center" />
          <el-table-column prop="district" label="城区" width="100" align="center" />          
          <el-table-column label="基础" align="center">
            <el-table-column prop="lastDebt" label="前期欠款" width="100" align="right" />
            <el-table-column prop="currentPayment" label="本月应收" width="100" align="right" />
            <el-table-column prop="penalty" label="本月实收" width="100" align="right" />
            <el-table-column prop="currentReceivable" label="本月调整" width="100" align="right" />
            <el-table-column prop="prepaymentOffset" label="实时欠款" width="100" align="right" />
          </el-table-column>
          <el-table-column label="高新" align="center">
            <el-table-column prop="lastDebt" label="前期欠款" width="100" align="right" />
            <el-table-column prop="currentPayment" label="本月应收" width="100" align="right" />
            <el-table-column prop="penalty" label="本月实收" width="100" align="right" />
            <el-table-column prop="currentReceivable" label="本月调整" width="100" align="right" />
            <el-table-column prop="prepaymentOffset" label="实时欠款" width="100" align="right" />
          </el-table-column>	
          <el-table-column label="工资" align="center">
            <el-table-column prop="lastDebt" label="前期欠款" width="100" align="right" />
            <el-table-column prop="currentPayment" label="本月应收" width="100" align="right" />
            <el-table-column prop="penalty" label="本月实收" width="100" align="right" />
            <el-table-column prop="currentReceivable" label="本月调整" width="100" align="right" />
            <el-table-column prop="prepaymentOffset" label="实时欠款" width="100" align="right" />
          </el-table-column>	
          <el-table-column label="合计" align="center">
            <el-table-column prop="lastDebt" label="前期欠款" width="100" align="right" />
            <el-table-column prop="currentPayment" label="本月应收" width="100" align="right" />
            <el-table-column prop="penalty" label="本月实收" width="100" align="right" />
            <el-table-column prop="currentReceivable" label="本月调整" width="100" align="right" />
            <el-table-column prop="prepaymentOffset" label="实时欠款" width="100" align="right" />
          </el-table-column>	
          <el-table-column label="操作" align="center" width="90" fixed="right">
            <template #default="scope">
              <el-button v-hasPermi="['tool:gen:edit']" type="primary" size="small" @click="edit(scope.row)">查看</el-button>
            </template>
          </el-table-column>	
        </el-table>
      </el-col>
    </el-row>
    <el-divider />
    <el-tabs v-model="activeName" class="mt-10" @tab-click="handleClick">
      <el-tab-pane label="基础" name="basic">
        <el-table 
          :data="table1" 
          style="width: 100%" 
          :stripe="true" 
          border
        >
          <el-table-column prop="companyName" label="公司" align="center" width="130" />
          <el-table-column prop="name" label="尚美/鲸喜/优恒等" align="center">
            <el-table-column prop="averageRoomRate" label="前期欠款" width="110" align="right" />
            <el-table-column prop="pointsDeduction" label="本月应收" align="right" />
            <el-table-column prop="managerSalaryPrepaid" label="本月清分" align="right" />
            <el-table-column prop="managerPerformanceSalary" label="本月收款" align="right" />
            <el-table-column prop="averageRoomRate" label="本月调整" align="right" />
            <el-table-column prop="pointsDeduction" label="实时欠款" align="right" />
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="高新" name="new">
        <el-table 
          :data="table1" 
          style="width: 100%" 
          :stripe="true" 
          border
        >
          <el-table-column prop="companyName" label="公司" align="center" width="130" />
          <el-table-column prop="name" label="腾容" align="center">
            <el-table-column prop="managerPerformanceSalary" label="前期欠款" width="110" align="right" />
            <el-table-column prop="pointsDeduction" label="本月应收" align="right" />
            <el-table-column prop="averageRoomRate" label="本月清分" align="right" />
            <el-table-column prop="managerPerformanceSalary" label="本月收款" align="right" />
            <el-table-column prop="pointsDeduction" label="本月调整" align="right" />
            <el-table-column prop="managerPerformanceSalary" label="实时欠款" align="right" />
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="工资" name="prize">
        <el-table 
          :data="table1" 
          style="width: 100%" 
          :stripe="true" 
          border
        >
          <el-table-column prop="companyName" label="公司" align="center" width="130" />
          <el-table-column prop="name" label="尚润" align="center">
            <el-table-column prop="averageRoomRate" label="前期欠款" width="110" align="right" />
            <el-table-column prop="managerSalaryPrepaid" label="上月应收" align="right" />
            <el-table-column prop="managerPerformanceSalary" label="上月实际" align="right" />
            <el-table-column prop="pointsDeduction" label="本月应收" align="right" />
            <el-table-column prop="managerSalaryPrepaid" label="本月清分" align="right" />
            <el-table-column prop="managerPerformanceSalary" label="本月收款" align="right" />
            <el-table-column prop="averageRoomRate" label="本月调整" align="right" />
            <el-table-column prop="pointsDeduction" label="实时欠款" align="right" />
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!-- 
    <el-row :gutter="10" class="mt-10">
      <el-col :sm="24" :lg="4">
        
      </el-col>
      <el-col :sm="24" :lg="4">
        
      </el-col>
      <el-col :sm="24" :lg="4">
        
      </el-col>
      <el-col :sm="24" :lg="4">
        
      </el-col>
    </el-row> -->
  </div>
</template>
<script setup>
// 数据录入页面
import router from '@/router'
import { generateRandomData, table1Demo } from '@/utils/move/dataRandom'
// 表格字段 分店号 分店名称 期间 审核否 是否结清 上期欠款 本期收款 违约金 本期应收 冲预收款 应缴金额 本期实收 期末结余 期初预收 本期预收 期末预收 缴费时间 缴费日期 状态 开业日期 解约日期 大区 城区 营业收入 商标使用费 积分产生数 积分款 预定间夜数 预定佣金 运营质检费 系统维护费 房间数量 出租率 平均房价 积分抵现 店长工资(预收) 店长绩效工资 中央订单金额 市场营销费 店长基本工资 品质督导费 运营顾问费 积分抵现金额 预定返佣金额 预定返佣数 夜深天数 舆情系统使用费 财务咨询费 工资季度调整 客源输送费 会员管理费 线上平台费 营销经理工资 营销经理绩效工资 营销经理工资季度调整 营销经理基本工资 录入人员 收费人员
const tableData = ref([])
const table1 = ref([])
table1.value = table1Demo()
// 初始化表格数据
tableData.value = generateRandomData()
  
const activeName = ref('basic')

const handleClick = (tab, event) => {
  console.log(tab, event)
}
const trClick = (row) => {
  console.log(row)
  table1.value = table1Demo()
}
const edit = (row) => {
  console.log(row, row.branchNo)
  router.push({ path: '/move/dataInputDetails', query: { id: row.branchNo } })
}
</script>
<style>
  /* .el-table_1_column_6,
  .el-table_1_column_7,
  .el-table_1_column_7_column_12,
  .el-table_1_column_13,
  .el-table_1_column_13_column_18,
  .el-table_1_column_19,
  .el-table_1_column_19_column_24{
  border-right: 1px solid #ccc!important;
} */
</style>
<style scoped lang="scss">
.el-divider--horizontal {
  margin: 0!important;
}
.el-form-item--small {
  margin-bottom: 8px!important;
}
.home {
  height:calc(100vh - 84px);
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}
</style>
