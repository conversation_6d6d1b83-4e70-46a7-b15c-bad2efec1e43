<template>
  <div class="app-container datainput-detail">

    <el-descriptions
      title="基本信息"
      :column="5"
      direction="vertical"
      size="default"
      border
    >
      <!-- <template #extra>
        <el-button type="primary">Operation</el-button>
      </template> -->
      <el-descriptions-item>
        <template #label>
          门店id
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <user />
            </el-icon>
            
          </div> -->
        </template>
        00001
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          门店名称
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <iphone />
            </el-icon>
            门店名称
          </div> -->
        </template>
        尚客优精选酒店(南京南站店)
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          大区
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <location />
            </el-icon>
            大区
          </div> -->
        </template>
        东南大区
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          城区
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <tickets />
            </el-icon>
            城区
          </div> -->
        </template>
        <!-- <el-tag size="small">School</el-tag> -->
        南京南站店
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          合同期限
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <office-building />
            </el-icon>
            合同期限
          </div> -->
        </template>
        2023-01-01 至 2024-01-01
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions
      class="mt-10"
      title="业主信息"
      :column="6"
      direction="vertical"
      size="default"
      border
    >
      <el-descriptions-item width="200">
        <template #label>
          业主类型（个人/公司）
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <user />
            </el-icon>
            业主类型（个人/公司）
          </div> -->
        </template>
        个人
      </el-descriptions-item>
      <el-descriptions-item width="250">
        <template #label>
          业主名称
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <iphone />
            </el-icon>
            业主名称
          </div> -->
        </template>
        尚客优精选酒店(南京南站店)
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          税号
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <location />
            </el-icon>
            税号
          </div> -->
        </template>
        1234567890
      </el-descriptions-item>
      <el-descriptions-item width="200">
        <template #label>
          电话
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <tickets />
            </el-icon>
            电话
          </div> -->
        </template>
        <!-- <el-tag size="small">School</el-tag> -->
        <!-- <el-input v-model="demo" disabled></el-input> -->
        <div class="pointer" @click="showEdit">
          {{ demo }}
          <el-icon color="#E6A23C"><Edit /></el-icon>
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          邮箱
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <office-building />
            </el-icon>
            邮箱
          </div> -->
        </template>
        <EMAIL>
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          开户行
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <office-building />
            </el-icon>
            开户行
          </div> -->
        </template>
        1234567890
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          发票类型
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <office-building />
            </el-icon>
            发票类型
          </div> -->
        </template>
        增值税专用发票

      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          合规付款人1
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <office-building />
            </el-icon>
            合规付款人1
          </div> -->
        </template>
        张三
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          合规付款人2
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <office-building />
            </el-icon>
            合规付款人2
          </div> -->
        </template>
        李四
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          合规付款人3
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <office-building />
            </el-icon>
            合规付款人3
          </div> -->
        </template>
        王五
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          合规付款人4
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <office-building />
            </el-icon>
            合规付款人4
          </div> -->
        </template>
        赵六
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          合规付款人5
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <office-building />
            </el-icon>
            合规付款人5
          </div> -->
        </template>
        冯七
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions
      class="mt-10"
      title="经营数据"
      :column="7"
      direction="vertical"
      size="default"
      border
    >
      <!-- <template #extra>
        <el-button type="primary">Operation</el-button>
      </template> -->
      <el-descriptions-item width="230">
        <template #label>
          门店状态（经营/封存/停业）
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <user />
            </el-icon>
            门店状态（经营/封存/停业）
          </div> -->
        </template>
        经营中
      </el-descriptions-item>
      <el-descriptions-item width="150">
        <template #label>
          房量
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <iphone />
            </el-icon>
            房量
          </div> -->
        </template>
        200
      </el-descriptions-item>
      <el-descriptions-item width="150">
        <template #label>
          OCC
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <location />
            </el-icon>
            OCC
          </div> -->
        </template>
        10%

      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          ADR
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <tickets />
            </el-icon>
            ADR
          </div> -->
        </template>
        <!-- <el-tag size="small">School</el-tag> -->
        10%
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          营业收入
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <office-building />
            </el-icon>
            营业收入
          </div> -->
        </template>
        99,999.00
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          夜审天数
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <office-building />
            </el-icon>
            夜审天数
          </div> -->
        </template>
        1188
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          会员积分
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <office-building />
            </el-icon>
            会员积分
          </div> -->
        </template>
        666
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      class="mt-10"
      title="收费标准"
      :column="4"
      direction="vertical"
      size="default"
      border
    >
      <el-descriptions-item>
        <template #label>
          计费状态
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <user />
            </el-icon>
            计费状态
          </div> -->
        </template>
        正常

      </el-descriptions-item>
      <el-descriptions-item>
        
        <template #label>
          商标使用费
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <iphone />
            </el-icon>
            商标使用费
          </div> -->
        </template>
        10,000.00

      </el-descriptions-item>
      <el-descriptions-item>
        <template #label>
          费用项目etc
          <!-- <div class="cell-item">
            <el-icon :style="iconStyle">
              <location />
            </el-icon>
            费用项目etc
          </div> -->
        </template>
        10%

      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      class="mt-10"
      title="历史账单"
      :column="3"
      size="default"
    >
      <template #extra>
        <el-select v-model="select" style="width:200px" placeholder="选择日期" clearable @change="dateChange">
          <el-option label="全部" value="all"></el-option>
          <el-option label="2024年1月" value="202401"></el-option>
          <el-option label="2024年2月" value="202402"></el-option>
          <el-option label="2024年3月" value="202403"></el-option>
          <el-option label="2024年4月" value="202404"></el-option>
          <el-option label="2024年5月" value="202405"></el-option>
          <el-option label="2024年6月" value="202406"></el-option>
          <el-option label="2024年7月" value="202407"></el-option>
          <el-option label="2024年8月" value="202408"></el-option>
          <el-option label="2024年9月" value="202409"></el-option>
          <el-option label="2024年10月" value="202410"></el-option>
          <el-option label="2024年11月" value="202411"></el-option>
          <el-option label="2024年12月" value="202412"></el-option>
        </el-select>
      </template>
    </el-descriptions>
    <el-row :gutter="10">
      <el-col :sm="24" :lg="24">
        <el-table
          :data="tableData" 
          style="width: 100%" 
          :stripe="true" 
          
          row-key="branchNo"
        >
          <el-table-column type="index" label="序号" width="60" fixed="left" />
          <el-table-column prop="period" label="月份" width="100" />
          <el-table-column label="基础" align="center">
            <el-table-column prop="trademarkFee" label="当月费用" align="right" />
            <el-table-column prop="pointsAmount" label="月末欠款" align="right" />
          </el-table-column>
          <el-table-column label="高新" align="center">
            <el-table-column prop="trademarkFee" label="当月费用" align="right" />
            <el-table-column prop="pointsAmount" label="月末欠款" align="right" />
          </el-table-column>
          <el-table-column label="工资" align="center">
            <el-table-column prop="trademarkFee" label="当月费用" align="right" />
            <el-table-column prop="pointsAmount" label="月末欠款" align="right" />
          </el-table-column>
          <el-table-column label="合计" align="center">
            <el-table-column prop="trademarkFee" label="当月费用" align="right" />
            <el-table-column prop="pointsAmount" label="月末欠款" align="right" />
          </el-table-column>
          <el-table-column prop="period" label="链接" align="center" width="90">
            <template #default="scope">
              <el-button type="primary" size="small" @click="link">链接</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-divider />

    <!-- 修改电话 -->
    <el-dialog v-model="dialogFormVisible" title="编辑" width="500">
      <el-form :model="form">
        <el-form-item label="修改电话" :label-width="100">
          <el-input v-model="tel" autocomplete="off" placeholder="请输入电话号码" />
        </el-form-item>
        <!-- <el-form-item label="城市" :label-width="100">
          <el-select v-model="region" placeholder="选择城市">
            <el-option label="Zone No.1" value="shanghai" />
            <el-option label="Zone No.2" value="beijing" />
          </el-select>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button type="primary" @click="dialogFormVisible = false">
            提交
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { generateRandomData, table1Demo } from '@/utils/move/dataRandom'
import { ElMessage } from 'element-plus'
// 表格字段 分店号 分店名称 期间 审核否 是否结清 上期欠款 本期收款 违约金 本期应收
// 表格字段 分店号 分店名称 期间 审核否 是否结清 上期欠款 本期收款 违约金 本期应收 冲预收款 应缴金额 本期实收 期末结余 期初预收 本期预收 期末预收 缴费时间 缴费日期 状态 开业日期 解约日期 大区 城区 营业收入 商标使用费 积分产生数 积分款 预定间夜数 预定佣金 运营质检费 系统维护费 房间数量 出租率 平均房价 积分抵现 店长工资(预收) 店长绩效工资 中央订单金额 市场营销费 店长基本工资 品质督导费 运营顾问费 积分抵现金额 预定返佣金额 预定返佣数 夜深天数 舆情系统使用费 财务咨询费 工资季度调整 客源输送费 会员管理费 线上平台费 营销经理工资 营销经理绩效工资 营销经理工资季度调整 营销经理基本工资 录入人员 收费人员
const tableData = ref([])
const table1 = ref([])
table1.value = table1Demo()
const select = ref('')
const demo = ref('')
const dialogFormVisible = ref(false)
const route = useRoute()
console.log(route.query.id)
const dateChange = () => {
  tableData.value = generateRandomData().sort(() => {
    return Math.random() > 0.5 ? -1 : 1
  }).slice(0, 12)
}
dateChange()

const link = () => {
  console.log(select.value)
  ElMessage({
    message: '消息提示框.',
    type: 'success'
  })
}
const showEdit = () => {
  dialogFormVisible.value = true
  console.log(123123)
}
const tel = ref('')
const region = ref('')
</script>
<style scoped lang="scss">
.el-divider--horizontal {
  margin: 0!important;
}
.el-form-item--small {
  margin-bottom: 8px!important;
}
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}
</style>
