<template>
  <div class="app-container home">
    <el-row :gutter="10">
      <el-col :sm="24" :lg="24">
        <el-select v-model="value" placeholder="Select" style="width: 240px" size="small">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button-group class="ml-10" size="small">
          <el-button type="primary" plain>搜索</el-button>
          <el-button type="primary" plain>刷新</el-button>
        </el-button-group>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="mt-10">
      <el-col :sm="24" :lg="3">
        <el-form size="small">
          <el-form-item label="分店状态">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="开业日期">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="解约日期">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="分店状态">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="所属大区">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="所属城区">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="省份">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="城市">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="县/区">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="财务序号">
            <el-input v-model="name" />
          </el-form-item>
        </el-form>

        <el-divider />
        <p style="font-size: 12px;font-weight: bold;">可抵扣金额</p>
        <h3 style="font-size: 16px;text-align: center;margin:10px 0;">参与抵扣</h3>
        <el-form size="small">
          <el-form-item label="账户余额">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="线下回款">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="可用额度">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="管理费用">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="店长工资">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="营销工资">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="抵扣合计">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="欠费金额">
            <el-input v-model="name" />
          </el-form-item>
          <el-form-item label="预收余额">
            <el-input v-model="name" />
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :sm="24" :lg="21">
        <el-table
          :data="tableData" 
          height="350" 
          style="width: 100%" 
          :stripe="true" 
          border
          row-key="branchNo"
        >
          <el-table-column type="index" label="序号" width="60" fixed="left" />
          <el-table-column prop="branchNo" label="分店号" width="100" />
          <el-table-column prop="branchName" label="分店名称" width="120" />
          <el-table-column prop="period" label="期间" width="100" />
          <!-- <el-table-column prop="isAudited" label="审核否" width="80">
            <template #default="scope">
              <el-checkbox v-model="scope.row.isAudited" />
            </template>
          </el-table-column> -->
          <el-table-column prop="isSettled" label="是否结清" width="80">
            <template #default="scope">
              <el-checkbox v-model="scope.row.isSettled" />
            </template>
          </el-table-column>
          <el-table-column prop="lastDebt" label="上期欠款" width="120" />
          <el-table-column prop="currentPayment" label="本期收款" width="120" />
          <el-table-column prop="penalty" label="违约金" width="100" />
          <el-table-column prop="currentReceivable" label="本期应收" width="120" />
          <el-table-column prop="prepaymentOffset" label="冲预收款" width="120" />
          <el-table-column prop="amountPayable" label="应缴金额" width="120" />
          <el-table-column prop="actualPayment" label="本期实收" width="120" />
          <el-table-column prop="endingBalance" label="期末结余" width="120" />
          <el-table-column prop="initialPrepayment" label="期初预收" width="120" />
          <el-table-column prop="currentPrepayment" label="本期预收" width="120" />
          <el-table-column prop="endingPrepayment" label="期末预收" width="120" />
          <el-table-column prop="paymentTime" label="缴费时间" width="160" />
          <el-table-column prop="paymentDate" label="缴费日期" width="120" />
          <el-table-column prop="status" label="状态" width="100" />
          <el-table-column prop="openingDate" label="开业日期" width="120" />
          <el-table-column prop="terminationDate" label="解约日期" width="120" />
          <el-table-column prop="region" label="大区" width="100" />
          <el-table-column prop="district" label="城区" width="100" />
          <el-table-column prop="operatingIncome" label="营业收入" width="120" />
          <el-table-column prop="trademarkFee" label="商标使用费" width="120" />
          <el-table-column prop="pointsGenerated" label="积分产生数" width="120" />
          <el-table-column prop="pointsAmount" label="积分款" width="100" />
          <el-table-column prop="reservedRoomNights" label="预定间夜数" width="120" />
          <el-table-column prop="reservationCommission" label="预定佣金" width="120" />
          <el-table-column prop="operationInspectionFee" label="运营质检费" width="120" />
          <el-table-column prop="systemMaintenanceFee" label="系统维护费" width="120" />
          <el-table-column prop="roomCount" label="房间数量" width="100" />
          <el-table-column prop="occupancyRate" label="出租率" width="100" />
          <el-table-column prop="averageRoomRate" label="平均房价" width="120" />
          <el-table-column prop="pointsDeduction" label="积分抵现" width="120" />
          <el-table-column prop="managerSalaryPrepaid" label="店长工资(预收)" width="140" />
          <el-table-column prop="managerPerformanceSalary" label="店长绩效工资" width="120" />
          <el-table-column prop="centralOrderAmount" label="中央订单金额" width="120" />
          <el-table-column prop="marketingFee" label="市场营销费" width="120" />
          <el-table-column prop="managerBaseSalary" label="店长基本工资" width="120" />
          <el-table-column prop="qualitySupervisionFee" label="品质督导费" width="120" />
          <el-table-column prop="operationConsultingFee" label="运营顾问费" width="120" />
          <el-table-column prop="pointsDeductionAmount" label="积分抵现金额" width="120" />
          <el-table-column prop="reservationCommissionAmount" label="预定返佣金额" width="120" />
          <el-table-column prop="reservationCommissionCount" label="预定返佣数" width="120" />
          <el-table-column prop="nightDeepDays" label="夜深天数" width="100" />
          <el-table-column prop="publicOpinionSystemFee" label="舆情系统使用费" width="140" />
          <el-table-column prop="financialConsultingFee" label="财务咨询费" width="120" />
          <el-table-column prop="salaryQuarterlyAdjustment" label="工资季度调整" width="120" />
          <el-table-column prop="customerSourceFee" label="客源输送费" width="120" />
          <el-table-column prop="membershipManagementFee" label="会员管理费" width="120" />
          <el-table-column prop="onlinePlatformFee" label="线上平台费" width="120" />
          <el-table-column prop="marketingManagerSalary" label="营销经理工资" width="120" />
          <el-table-column prop="marketingManagerPerformance" label="营销经理绩效工资" width="140" />
          <el-table-column prop="marketingManagerQuarterlyAdjustment" label="营销经理工资季度调整" width="160" />
          <el-table-column prop="marketingManagerBaseSalary" label="营销经理基本工资" width="140" />
          <el-table-column prop="entryPerson" label="录入人员" width="100" />
          <el-table-column prop="collectionPerson" label="收费人员" width="100" />
        </el-table>
        <el-divider />
        <el-row :gutter="10" class="mt-10">
          <el-col :sm="24" :lg="8">
            <el-table 
              :data="table2" 
              style="width: 100%" 
              :stripe="true" 
              border
              height="300" 
              row-key="branchNo"
            >
              <el-table-column type="index" label="" width="45" />
              <el-table-column prop="name" label="计算项目列表" width="110" />
              <el-table-column prop="val" label="数值" />
              <el-table-column prop="dw" label="单位" width="60" />
            </el-table>
          </el-col>
          <el-col :sm="24" :lg="8">
            <el-table 
              :data="table2" 
              style="width: 100%" 
              :stripe="true" 
              border
              height="300" 
              row-key="branchNo"
            >
              <el-table-column type="index" label="" width="45" />
              <el-table-column prop="name" label="计算项目列表" width="110" />
              <el-table-column prop="val" label="数值" />
              <el-table-column prop="dw" label="单位" width="60" />
            </el-table>
          </el-col>
          <el-col :sm="24" :lg="8">
            <el-table 
              :data="table2" 
              style="width: 100%" 
              :stripe="true" 
              border
              height="300" 
              row-key="branchNo"
            >
              <el-table-column type="index" label="" width="45" />
              <el-table-column prop="name" label="计算项目列表" width="110" />
              <el-table-column prop="val" label="数值" />
              <el-table-column prop="dw" label="单位" width="60" />
            </el-table>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>
<script setup>
// 缴费确认页面
import { generateRandomData, table2Demo } from '@/utils/move/dataRandom'

const value = ref('')
const table2 = ref([])
table2.value = table2Demo
console.log(table2.value)

const options = [
  {
    value: 'Option1',
    label: 'Option1'
  },
  {
    value: 'Option2',
    label: 'Option2'
  },
  {
    value: 'Option3',
    label: 'Option3'
  },
  {
    value: 'Option4',
    label: 'Option4'
  },
  {
    value: 'Option5',
    label: 'Option5'
  }
]
const name = ref('')

const tableData = ref([])
// 初始化表格数据
tableData.value = generateRandomData()

</script>
<style lang="scss" scoped>
  .el-form-item {
    margin-bottom: 10px;
  }
  .el-divider--horizontal {
    margin: 0!important;
  }
</style>
