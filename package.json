{"name": "bill", "version": "1.0.0", "description": "新结算系统", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build", "build:dev": "vite build --mode development", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Cloud.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "@wecom/jssdk": "^1.4.5", "axios": "0.28.1", "clipboard": "2.0.11", "dayjs": "^1.11.13", "echarts": "5.5.1", "element-plus": "2.8.0", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-beautify": "1.14.11", "js-cookie": "3.0.5", "js-md5": "^0.8.3", "jsencrypt": "3.3.2", "lodash": "^4.17.21", "mitt": "^3.0.1", "nprogress": "0.2.0", "pinia": "2.1.7", "postcss": "^8.5.6", "postcss-html": "^1.8.0", "splitpanes": "3.1.5", "vite-plugin-svg-icons": "^2.0.1", "vue": "3.4.31", "vue-cropper": "1.1.1", "vue-router": "4.4.0", "vuedraggable": "4.1.0"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/eslint-parser": "^7.23.10", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "@vitejs/plugin-vue": "5.0.5", "@vue/compiler-sfc": "3.3.9", "@vue/eslint-config-typescript": "^12.0.0", "code-inspector-plugin": "^0.20.17", "eslint": "^8.13.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-html": "^7.1.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^9.9.0", "prettier": "3.6.2", "sass": "1.77.5", "stylelint": "^16.2.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recommended": "^14.0.0", "stylelint-config-standard": "^36.0.0", "stylelint-order": "^6.0.4", "unplugin-auto-import": "^19.3.0", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-qiankun": "^1.0.15", "vite-plugin-svg-icons": "^2.0.1", "vue-eslint-parser": "^10.1.3"}}