module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es2021: true,
    'vue/setup-compiler-macros': true
  },
  parser: 'vue-eslint-parser',
  extends: [
    // 'eslint:recommended', // 使用推荐的eslint
    'plugin:@typescript-eslint/recommended',
    'plugin:vue/vue3-recommended', // 使用插件支持vue3
    'prettier',
    './.eslintrc-auto-import.json'
  ],
  parserOptions: {
    // ecmaVersion: 2020,
    parser: require.resolve('@typescript-eslint/parser'),
    extraFileExtensions: ['.vue']
  },
  rules: {
    'prefer-promise-reject-errors': 'off',

    // this rule, if on, would require explicit return type on the `render` function
    '@typescript-eslint/explicit-function-return-type': 'off',

    // in plain CommonJS modules, you can't use `import foo = require('foo')` to pass this rule, so it has to be disabled
    '@typescript-eslint/no-var-requires': 'off',

    '@typescript-eslint/no-this-alias': ['off'],

    '@typescript-eslint/no-explicit-any': 'off',

    '@typescript-eslint/no-non-null-assertion': 'off',
    '@typescript-eslint/no-unused-vars': 'off',
    // The core 'no-unused-vars' rules (in the eslint:recommended ruleset)
    // does not work with type definitions
    'no-unused-vars': 'off',
    'space-before-function-paren': 'off',
    // allow debugger during development only
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'vue/multi-word-component-names': 0,

    indent: [
      2,
      2,
      {
        // 2缩进
        SwitchCase: 1
      }
    ],
    'vue/html-indent': [2, 2], // html 2锁进
    'no-mixed-spaces-and-tabs': [2, false], // 禁止混用空格
    'vue/max-attributes-per-line': [
      2,
      {
        // 属性超过5个换行
        singleline: 5,
        multiline: {
          max: 5
        }
      }
    ],
    // 'space-before-function-paren': [2, 'always'], // 定义函数，函数名和参数之间有空格
    'vue/html-self-closing': [
      'error',
      {
        html: {
          void: 'never',
          normal: 'any',
          component: 'any'
        },
        svg: 'always',
        math: 'always'
      }
    ],
    eqeqeq: [2, 'allow-null'],
    semi: ['error', 'never'], // 不必要的分号
    '@typescript-eslint/semi': 'off',
    'arrow-spacing': [
      2,
      {
        // 箭头函数空格
        before: true,
        after: true
      }
    ],
    'arrow-parens': 2,
    quotes: [
      2,
      'single',
      {
        // 单引号
        avoidEscape: true,
        allowTemplateLiterals: true
      }
    ],
    'no-multi-spaces': 2, // 多余空格，空行
    'no-multi-str': 2,
    'no-multiple-empty-lines': [
      2,
      {
        max: 1
      }
    ],
    'comma-dangle': [2, 'never'], // 逗号相关
    'comma-spacing': [
      2,
      {
        before: false,
        after: true
      }
    ],
    'comma-style': [2, 'last']
  }
}
