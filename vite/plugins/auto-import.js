import autoImport from 'unplugin-auto-import/vite'
export default function createAutoImport () {
  return autoImport({
    // 目标文件
    include: [
      /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
      /\.vue$/, /\.vue\?vue/, // .vue
      /\.md$/ // .md
    ],
    imports: [
      'vue',
      'vue-router',
      'pinia',
      // 可额外添加需要 autoImport 的组件
      {
        '@/hooks/useMessage': ['useMessage'],
        '@/hooks/useTableHeight': ['useTableHeight']
      }
    ],
    dts: 'src/types/auto-imports.d.js',
    eslintrc: {
      enabled: true, // Default `false`
      filepath: './.eslintrc-auto-import.json', // Default `./.eslintrc-auto-import.json`
      globalsPropValue: true // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
    }
  })
}
